//------------------------------------------------------------------------------
//
//  Formula Name:    Elders AutoEnvelopes
//  Author/Uploader: Hoyala 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2006-01-17 16:51:20
//  Origin:          USA
//  Keywords:        <PERSON>'s AutoEnvelopes
//  Level:           advanced
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=581
//  Details URL:     http://www.amibroker.com/library/detail.php?id=581
//
//------------------------------------------------------------------------------
//
//  Who can translate this formula to Amibroker?
//
//  Please help me!
//
//------------------------------------------------------------------------------

====================
Elder's AutoEnvelope
====================
---8<---------------------------

{Dr A. Elder's AutoEnvelope interpretation v1.0}
{ www.elder.com/MetaStock/AutoEnvelope.htm }
{ http://www.metastocktools.com }

{ User inputs }
pds:=Input("EMA periods",1,252,21);
pdsBak:=Input("lookback periods",1,252,42);
x:=Input("use: Open=1, High=2, Low=3, Close=4, WClose=5",1,5,4);
plot:=Input("[1]AutoEnvelope, [2]Long signals, [3]All signals",1,3,1);
delay:=Input("Entry/Exit signals delay",0,5,0);

{ Price field }
x:=If(x=1,O,If(x=2,H,If(x=3,L,If(x=5,WC(),C))));

{ Envelope bands }
Avg:=Mov(x,pds,E);
hiAvg:=HHV(H,pdsBak);
loAvg:=LLV(L,pdsBak);
shift:=
 Mov(If(hiAvg>Avg,hiAvg-Avg,Avg-loAvg),pds,E);
UpperBand:=Avg+shift;
LowerBand:=Avg-shift;

{ Envelope signals }
In:=Cross(x,LowerBand);
Out:=Cross(x,UpperBand);
Init:=Cum(In+Out>-1)=1;
InInit:=Cum(In)=1;
flag:=BarsSince(Init OR In)
 <BarsSince(Init OR Out)+InInit;
signals:=Ref((InInit AND Alert(InInit=0,2)
  OR flag AND Alert(flag=0,2))
   -(flag=0 AND Alert(flag,2)),-delay);

{ Plot envelope on price chart }
If(plot=1,UpperBand,If(plot=2,signals,In-Out));
If(plot=1,Avg,If(plot=2,0,0));
If(plot=1,LowerBand,If(plot=2,signals,In-Out))

---8<---------------------------


http://www.metastocktools.com
