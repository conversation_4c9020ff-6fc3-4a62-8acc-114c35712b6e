_SECTION_BEGIN( "Background_Setting" );
SetChartBkGradientFill( ParamColor( "BgTop", colorBlack ),
                        ParamColor( "BgBottom", colorBlack ), ParamColor( "titleblock", colorDarkTeal ) );
_SECTION_END();
RoundLotSize = 1;
_SECTION_BEGIN( "Market Setting" );
p = param( "Period", 2, 2, 30, 1 );
IM = ParamToggle( "Intraday Mode", "Off|On", 0 );
TradeStartTime = Param( "Trade Start From(HHMM)", 915, 600, 2400, 1 );
NoEntryTime = Param( "No Entry After(HHMM)", 1445, 900, 2400, 1 );
EXTIME = Param( "Square Off Time", 1500, 900, 2400, 1 );
MinSpread = Param( "Minimum Spread", 0.05, 0.001, 10, 0.001 );
MessageBox = ParamToggle( "Message Box", "Hide|Show", 1 );
_SECTION_END();
_SECTION_BEGIN( "Target-Stoploss Settings" );
PORP1 = ParamList( "Switch", "On|Off", 1 );
PORP = ParamToggle( "Percentage or Point", "Percentage|Point", 0 );
TG = Param( "Target ", 1, 0.25, 100, 0.25 );
SL = Param( "StopLoss", 0.25, 0.25, 100, 0.25 );
_SECTION_END();
FC = DateNum() != Ref( DateNum(), -1 );
LC = DateNum() != Ref( DateNum(), 1 );
EntryTime = IIf( IM, TimeNum() >= TradeStartTime * 100 AND TimeNum() < NoEntryTime * 100, 1 );
MarketClose = IIf( IM, ExRem( TimeNum() >= EXTIME * 100, LC ), 0 );
_SECTION_BEGIN( "The_Beast_2" );
SetBarsRequired( 10000, 10000 );
Prd1 = Param( "ATR Period 1-20", 4, 1, 20, 1 );
Prd2 = Param( "LookBack Period 1-20", 7, 1, 20, 1 );
Green = HHV( LLV( L, Prd1 ) + ATR( Prd1 ), Prd2 );
RED = LLV( HHV( H, Prd1 ) - ATR( Prd1 ), Prd2 );
HaClose = EMA( ( O + H + L + C ) / 4, 3 );
HaOpen = AMA( Ref( HaClose, -1 ), 0.5 );
HaHigh = Max( H, Max( HaClose, HaOpen ) );
HaLow = Min( L, Min( HaClose, HaOpen ) );
Color = IIf( C > Green , colorBrightGreen, IIf( C < RED, colorRed, colorWhite ) );
PlotOHLC( O, H, L, C, "", color, styleCandle, styleThick );
SetChartOptions( 0, chartShowArrows | chartShowDates );
_N( Title = StrFormat( "{{NAME}} - {{INTERVAL}} {{DATE}} Open %g, Hi %g, Lo %g, Close %g (%.1f%%) Vol " + WriteVal( V, 1.0 ) + " {{VALUES}}", O, H, L, C, SelectedValue( ROC( C, 1 ) ) ) );
_SECTION_END();
_SECTION_BEGIN( "Trailing_Stop_Short" );
stoplossPercentage = Param( "Stoploss Percentage", 5, 2, 10 );
lookbackPeriod = Param( "Lookback period", 10, 5, 30 );
Plot( HHV( C, lookbackPeriod ) - HHV( C, lookbackPeriod ) * ( stoplossPercentage / 100 ), "Trailing stoploss", ParamColor( "Color", colorCycle ), ParamStyle( "Style", styleLine | Stylehidden , maskAll ) );
_SECTION_END();
_SECTION_BEGIN( "Trailing_Stop_Long" );
mf = Param( "ATR multiplier", 3, 1, 3, 0.25 );
ap = Param( "ATR Period", 10, 5, 30, 1 );
Lb = Param( "Lookback Period", 20, 10, 40, 1 );
ps = ParamToggle( "Use last low pivot", "Use,Dont", 1 );
t1 = HHV( C, Lb );
t2 = ( mf*ATR( ap ) );
t3 = Trough( C, 2, 1 );
t4 = t1 - t2;
t5 = Min( t4, t3 );

if( ps )
{
    t6 = t1 - t2;
}
else
{
    t6 = t5;
}

initial = t6;
stop[ 0 ] = Close[ 0 ];

for( i = 1 ; i < BarCount; i++ )
{
    if( Close[ i ] > stop[ i - 1] )
    {
        temp = t6[ i ];

        if( temp > stop[ i - 1 ] ) stop[ i ] = temp;
        else stop[ i ] = stop[ i - 1 ];
    }
    else
        stop[ i ] = initial[ i ];
}

Plot( stop, "ATR Stop", ParamColor( "Color", colorSeaGreen ), ParamStyle( "Style", styleLine | Stylehidden, maskAll ) );
_SECTION_END();
_SECTION_BEGIN( "Trailing_Lines" );
Plot( LLV( HHV( H, 5 ) - ATR( 5 ), 8 ), "", ParamColor( "Color Line 0", colorDarkGreen ), ParamStyle( "Style Line 0", styleLine, maskAll ) );
Plot( LLV( HHV( H, 5 ) - ATR( 5 ), 7 ), "", ParamColor( "Color Line 1", colorDarkGrey ), ParamStyle( "styleLine 1", styleLine, maskAll ) );
Plot( LLV( HHV( H, 5 ) - ATR( 5 ), 6 ), "", ParamColor( "Color Line 2", colorDarkGrey ), ParamStyle( "Style Line 2", styleLine, maskAll ) );
Plot( LLV( HHV( H, 5 ) - ATR( 5 ), 5 ), "", ParamColor( "Color Line 3", colorDarkGrey ), ParamStyle( "Style Line 3", styleLine, maskAll ) );
Plot( LLV( HHV( H, 5 ) - ATR( 5 ), 4 ), "", ParamColor( "Color Line 4", colorDarkGrey ), ParamStyle( "Style Line 4", styleLine, maskAll ) );
Plot( LLV( HHV( H, 5 ) - ATR( 5 ), 3 ), "", ParamColor( "Color Line 5", colorDarkYellow ), ParamStyle( "Style Line 5", styleLine, maskAll ) );
_SECTION_END();
_SECTION_BEGIN( "Pivot_Finder" );
farback = Param( "How Far back to go", 100, 0, 5000, 10 );
nBars = Param( "Number of bars", 12, 5, 40 );
GraphXSpace = 7;
aHPivs = H - H;
aLPivs = L - L;
aHPivHighs = H - H;
aLPivLows = L - L;
aHPivIdxs = H - H;
aLPivIdxs = L - L;
nHPivs = 0;
nLPivs = 0;
lastHPIdx = 0;
lastLPIdx = 0;
lastHPH = 0;
lastLPL = 0;
curPivBarIdx = 0;
aHHVBars = HHVBars( H, nBars );
aLLVBars = LLVBars( L, nBars );
aHHV = HHV( H, nBars );
aLLV = LLV( L, nBars );
aVisBars = Status( "barvisible" );
nLastVisBar = LastValue( Highest( IIf( aVisBars, BarIndex(), 0 ) ) );
_TRACE( "Last visible bar: " + nLastVisBar );
curBar = ( BarCount - 1 );
curTrend = "";

if( aLLVBars[curBar] <
        aHHVBars[curBar] )
{
    curTrend = "D";
}
else
{
    curTrend = "U";
}

for( i = 0; i < farback; i++ )
{
    curBar = ( BarCount - 1 ) - i;

    if( aLLVBars[curBar] < aHHVBars[curBar] )
    {
        if( curTrend == "U" )
        {
            curTrend = "D";
            curPivBarIdx = curBar - aLLVBars[curBar];
            aLPivs[curPivBarIdx] = 1;
            aLPivLows[nLPivs] = L[curPivBarIdx];
            aLPivIdxs[nLPivs] = curPivBarIdx;
            nLPivs++;
        }
    }
    else
    {
        if( curTrend == "D" )
        {
            curTrend = "U";
            curPivBarIdx = curBar - aHHVBars[curBar];
            aHPivs[curPivBarIdx] = 1;
            aHPivHighs[nHPivs] = H[curPivBarIdx];
            aHPivIdxs[nHPivs] = curPivBarIdx;
            nHPivs++;
        }
    }
}

curBar = ( BarCount - 1 );
candIdx = 0;
candPrc = 0;
lastLPIdx = aLPivIdxs[0];
lastLPL = aLPivLows[0];
lastHPIdx = aHPivIdxs[0];
lastHPH = aHPivHighs[0];

if( lastLPIdx > lastHPIdx )
{
// -- Bar and price info for candidate pivot
    candIdx = curBar - aHHVBars[curBar];
    candPrc = aHHV[curBar];

    if(
        lastHPH < candPrc AND
        candIdx > lastLPIdx AND
        candIdx < curBar )
    {
// -- OK, we'll add this as a pivot...
        aHPivs[candIdx] = 1;

// ...and then rearrange elements in the
// pivot information arrays
        for( j = 0; j < nHPivs; j++ )
        {
            aHPivHighs[nHPivs - j] = aHPivHighs[nHPivs -
                                                ( j + 1 )];
            aHPivIdxs[nHPivs - j] = aHPivIdxs[nHPivs - ( j + 1 )];
        }

        aHPivHighs[0] = candPrc ;
        aHPivIdxs[0] = candIdx;
        nHPivs++;
    }
}
else
{
// -- Bar and price info for candidate pivot
    candIdx = curBar - aLLVBars[curBar];
    candPrc = aLLV[curBar];

    if(
        lastLPL > candPrc AND
        candIdx > lastHPIdx AND
        candIdx < curBar )
    {
// -- OK, we'll add this as a pivot...
        aLPivs[candIdx] = 1;

// ...and then rearrange elements in the
// pivot information arrays
        for( j = 0; j < nLPivs; j++ )
        {
            aLPivLows[nLPivs - j] = aLPivLows[nLPivs - ( j + 1 )];
            aLPivIdxs[nLPivs - j] = aLPivIdxs[nLPivs - ( j + 1 )];
        }

        aLPivLows[0] = candPrc;
        aLPivIdxs[0] = candIdx;
        nLPivs++;
    }
}

// -- Dump inventory of high pivots for debugging
for( k = 0; k < nHPivs; k++ )
{
    _TRACE( "High pivot no. " + k
            + " at barindex: " + aHPivIdxs[k] + ", "
            + WriteVal( ValueWhen( BarIndex() == aHPivIdxs[k],
                                   DateTime(), 1 ), formatDateTime )
            + ", " + aHPivHighs[k] );
}

// -- OK, let's plot the pivots using arrows
PlotShapes(
    IIf( aHPivs == 1, shapeSmallSquare, shapeNone ), colorCustom12, 0, H, 25 );
PlotShapes(
    IIf( aLPivs == 1, shapeSmallSquare , shapeNone ), colorOrange, 0, L, -20 );
_SECTION_BEGIN( "Graphics" );
GrpPrm = Param( "Graphic Space", 1, 0, 10 );
GraphXSpace = GrpPrm;
_SECTION_END();
_SECTION_BEGIN( "Analysis" );
M1 = EMA( ( ( 2 * EMA( C, 29 / 2 ) ) - EMA( C, 29 ) ), 2 );
M2 = EMA( ( ( 2 * EMA( C, 29 / 2 ) ) - EMA( C, 29 ) ), 5 );
Buy1 = Cross( M1, M2 ) AND  EntryTime;
Short1 = Cross( M2, M1 ) AND  EntryTime;
Buy = Ref( Buy1, -1 ) AND M1  AND EntryTime AND NOT  Short1;
Short = Ref( Short1, -1 ) AND M2 AND  EntryTime AND NOT Buy1;
Sell = Short1 OR MarketClose;
Cover = Buy1 OR MarketClose;
Buy = ExRem( Buy, Sell );
Short = ExRem( Short, cover );
Sell = ExRem( Sell, Buy );
Cover = ExRem( Cover, Short );
BuyPrice = ValueWhen( Buy, HaOpen , 1 );
ShortPrice = ValueWhen( Short, HaOpen , 1 );
Openlong = Flip( Buy, Sell );
Openshort = Flip( Short, Cover );

if( PORP1 == "On" )
{
    if( PORP )
    {
        BTP1 = BuyPrice + TG;
        STP1 = ShortPrice - TG;
    }
    else
    {
        BTP1 = ( round( BuyPrice * ( 1 + TG / 100 ) / MinSpread ) * MinSpread );
        STP1 = ( round( ShortPrice * ( 1 - TG / 100 ) / MinSpread ) * MinSpread );
    }
}
else
    if( PORP1 == "Off" )
    {
        if( PORP )
        {
            BTP1 = Null;
            STP1 = Null;
        }
        else
        {
            BTP1 = Null;
            STP1 = Null;
        }
    }

BTPrice1 = IIf( OpenLong OR Sell, BTP1, Null );
STPrice1 = IIf( OpenShort OR Cover, STP1, Null );

if( PORP1 == "On" )
{
    if( PORP )
    {
        BSLP = BuyPrice - SL;
        SSLP = ShortPrice + SL;
    }
    else
    {
        BSLP = ( round( BuyPrice * ( 1 - SL / 100 ) / MinSpread ) * MinSpread );
        SSLP = ( round( ShortPrice * ( 1 + SL / 100 ) / MinSpread ) * MinSpread );
    }
}

if( PORP1 == "Off" )
{
    if( PORP )
    {
        BSLP = Null;
        SSLP = Null;
    }
    else
    {
        BSLP = Null;
        SSLP = Null;
    }
}

//ApplyStop( stopTypeNBar, stopModeBars, 5 );
ApplyStop( stopTypeLoss, stopModePercent, 1 , True, False, 0 );
ApplyStop( stopTypeProfit, stopModePercent, 3 );
//ApplyStop( stopTypeLoss, stopModePoint,100 , True, False, 0);
//ApplyStop(stopTypeProfit, stopModePoint, 300);
BSLPrice = IIf( OpenLong OR Sell, BSLP, Null );
SSLPrice = IIf( OpenShort OR Cover, SSLP, Null );
//////////////////////////////////////////////////////////////////
TSell1 = ( HaHigh >= BTPrice1 ) AND !IsNull( BTPrice1 );
SLSell = ( HaLow <= BSLPrice AND !Buy ) AND !IsNull( BSLPrice );
TCover1 = ( HaLow <= STPrice1 ) AND !IsNull( STPrice1 );
SLCover = ( HaHigh >= SSLPrice AND !Short ) AND !IsNull( SSLPrice );
Sell = ( Sell OR TSell1 ) OR SLSell ;
Cover = ( Cover OR TCover1 ) OR SLCover;
Buy = ExRem( Buy, Sell );
Short = ExRem( Short, Cover );
Sell = ExRem( Sell, Buy );
Cover = ExRem( Cover, Short );
OpenLong = Flip( Buy, Sell );
OpenShort = Flip( Short, Cover );
TSell1 = ( OpenLong OR Sell ) AND TSell1;
TCover1 = ( OpenShort OR Cover ) AND TCover1;
SLSell = ( OpenLong OR Sell ) AND SLSell;
SLCover = ( OpenShort OR Cover ) AND SLCover;
TSell1 = ExRem( TSell1, Sell );
SLSell = ExRem( SLSell, Sell );
TCover1 = ExRem( TCover1, Cover );
SLCover = ExRem( SLCover, Cover );
BuyPrice = IIf( OpenLong OR Sell, BuyPrice, Null );
ShortPrice = IIf( OpenShort OR Cover, ShortPrice, Null );
BTPrice1 = IIf( OpenLong OR Sell, BTPrice1, Null );
STPrice1 = IIf( OpenShort OR Cover, STPrice1, Null );
BSLPrice = IIf( OpenLong OR Sell, BSLPrice, Null );
SSLPrice = IIf( OpenShort OR Cover, SSLPrice, Null );
SellPrice = IIf( Sell*SLSell, BSLPrice, IIf( Sell*TSell1, BTPrice1, IIf( Sell*Short, ShortPrice,  IIf( Sell*MarketClose, HACLOSE, IIf( Sell, HACLOSE, Null ) ) ) ) );
CoverPrice = IIf( Cover*SLCover, SSLPrice, IIf( Cover*TCover1, STPrice1 , IIf( Cover*Buy, BuyPrice, IIf( Cover*MarketClose, HACLOSE, IIf( Sell, HACLOSE, Null ) ) ) ) );
/////////////////////////////////////////////////////////////////////////////////////
PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorGreen, 0, L, Offset = -40 );
PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorLime, 0, L, Offset = -50 );
PlotShapes( IIf( Buy, shapeUpArrow, shapeNone ), colorWhite, 0, L, Offset = -45 );
PlotShapes( IIf( Short, shapeSquare, shapeNone ), colorRed, 0, H, Offset = 40 );
PlotShapes( IIf( Short, shapeSquare, shapeNone ), colorOrange, 0, H, Offset = 50 );
PlotShapes( IIf( Short, shapeDownArrow, shapeNone ), colorWhite, 0, H, Offset = -45 );
PlotShapes( IIf( Cover, shapeUpTriangle, shapeNone ), colorYellow, 0, L, Offset = -70 );
PlotShapes( IIf( Sell, shapeDownTriangle, shapeNone ), colorPink, 0, H, Offset = -70 );
PlotShapes( TSell1*shapeStar, colorBrightGreen, 0, H, 10, 0 );
PlotShapes( TCover1*shapeStar, colorRed, 0, L, -10, 0 );
PlotShapes( SLSell*shapeStar, colorBlue, 0, L, -20, 0 );
PlotShapes( SLCover*shapeStar, colorYellow, 0, H, 20, 0 );
Plot( BuyPrice, "Buy Price", colorBrightGreen, styleLine | styleNoTitle | styleNoLabel );
Plot( ShortPrice, "Short Price", colorRed, styleLine | styleNoTitle | styleNoLabel );
Plot( BTPrice1, "BuyTargetPrice", colorGreen, styleLine | styleThick | styleDashed | styleNoTitle );
Plot( STPrice1, "ShortTargetPrice", colorRed, styleLine | styleThick | styleDashed | styleNoTitle );
Plot( BSLPrice, "BuySLPrice", colorLightOrange, styleLine | styleDashed | styleNoTitle );
Plot( SSLPrice, "ShortSLPrice", colorLightOrange, styleLine | styleDashed | styleNoTitle );

for( i = 0; i < BarCount; i++ )
{
    if( Buy[i] ) PlotText( "Buy- " + BuyPrice[ i ], i, HaLow[ i ], colorGreen, colorWhite );

    if( Short[i] ) PlotText( "Short- " + ShortPrice[ i ], i, HaHigh[ i ], colorRed, colorYellow );
}

_SECTION_END();
//Exploration
_SECTION_BEGIN( "Signal Scan" );
Filter = Buy  OR Sell OR Short OR Cover;
Color = IIf( Buy OR Cover, colorBrightGreen, colorRed );
TextColor = IIf( Buy OR Cover, colorBlack, colorWhite );
Ep = IIf( Buy, BuyPrice, IIf( Short, ShortPrice, Null ) );
Xp = IIf( Sell, SellPrice, IIf( Cover, CoverPrice, Null ) );
Pr = IIf( Sell, SellPrice - BuyPrice, IIf( Cover, ShortPrice - CoverPrice, Null ) );
AddColumn( IIf( Buy, 66, IIf( Short, 83, IIf( Cover, 67, IIf( Sell, 88, 126 ) ) ) ), "Signal", formatChar, TextColor, Color, 150 );
AddColumn( Ep, "Entry price", 1.2, TextColor, Color, 150 );
AddColumn( Xp, "Exit price", 1.2, TextColor, Color, 150 );
AddColumn( Pr, "Profit", 1.2, TextColor, Color, 150 );
_SECTION_END();
_SECTION_BEGIN( "BackTest Settings" );
Expo = 1000 * Param( "Initial Equity In Thousand", 100, 1, 10000, 1 );
AM = Param( "Account Margin", 0.1, 0.01, 1, 0.01 );
SetOption( "Initialequity", Expo );
SetOption( "AccountMargin", AM );
SetOption( "PriceBoundChecking", 1 );
MaxOpenPos = Param( "MaxOpenPos", 1, 1, 200, 1 );
SetOption( "MaxOpenPositions", MaxOpenPos );
SetPositionSize( 50, spsShares );
RoundLotSize = Param( "Lot Size", 25, 25, 100, 1, 0 );
TickSize = 0.05;
Lot = RoundLotSize;
SetPositionSize( ( Expo / AM ) / MaxOpenPos, spsValue );
_SECTION_END();
_SECTION_BEGIN( "Strategy Settings" );
EXTIME = Param( "Square Off Time", 1500, 900, 2400, 1 );
BSMBL = ParamStr( "Basic Symbol", "NIFTY" );
EXPIRR = ParamStr( "Expiry Bridge", "10JUN2021" );
EXPIRD = ParamStr( "Expiry Data", "" );
STRKST = Param( "Strike Steps", 100, 10, 1000, 50 );
STRKM = Param( "Strike Multiplier", 0, -10, 10, 1 );
LOT = Param( "Lot Size", 25, 1, 1000, 1 );
QNT = Param( "Qauntity", 1, 0, 10000, 1 );
RESET = ParamTrigger( "Reset", "Click To Reset" );
_SECTION_END();
STRDLO_CE = LastValue( ValueWhen( Buy, HaOpen, 1 ) );
STRDLO_PE = LastValue( ValueWhen( Short, HaOpen, 1 ) );
ATMCESTRK = round( STRDLO_CE / STRKST ) * STRKST;
ATMPESTRK = round( STRDLO_PE / STRKST ) * STRKST;
CESTRK = ATMCESTRK + STRKM * STRKST;
PESTRK = ATMPESTRK - STRKM * STRKST;
CESTRKS = NumToStr( CESTRK, 1.0, False );
PESTRKS = NumToStr( PESTRK, 1.0, False );
CESYMR = BSMBL + "|" + EXPIRR + "|" + CESTRKS + "|" + "CE";
PESYMR = BSMBL + "|" + EXPIRR + "|" + PESTRKS + "|" + "PE";
////////////////////////////////////
// For Global datafeed please replace "CE" and "PE" to "CE.NFO" and "PE.NFO"
//CESYMD = BSMBL+EXPIRD+CESTRKS+"CE";
//PESYMD = BSMBL+EXPIRD+PESTRKS+"PE";
////////////////////////////////////////
CESYMD = BSMBL + CESTRKS + "CE";
PESYMD = BSMBL + PESTRKS + "PE";
SetForeign( CESYMD );
Om = MA( O, p );
hm = MA( H, p );
lm = MA( L, p );
Cm = MA( C, p );
HACLOSE = ( Om + Hm + Lm + Cm ) / 4;
HaOpen = AMA( Ref( HaClose, -1 ), 0.5 );
HaHigh = Max( Hm, Max( HaClose, HaOpen ) );
HaLow = Min( Lm, Min( HaClose, HaOpen ) );
CE_CLOSE = HACLOSE;
CE_OPEN = HaOpen ;
CE_HIGH = HaHigh;
CE_LOW = HaLow;
RestorePriceArrays();
SetForeign( PESYMD );
Om = MA( O, p );
hm = MA( H, p );
lm = MA( L, p );
Cm = MA( C, p );
HACLOSE = ( Om + Hm + Lm + Cm ) / 4;
HaOpen = AMA( Ref( HaClose, -1 ), 0.5 );
HaHigh = Max( Hm, Max( HaClose, HaOpen ) );
HaLow = Min( Lm, Min( HaClose, HaOpen ) );
PE_CLOSE = HACLOSE;
PE_OPEN = HaOpen;
PE_HIGH = HaHigh;
PE_LOW = HaLow;
RestorePriceArrays();
CE_BUY = ValueWhen( Buy, CE_CLOSE, 1 );
PE_BUY = ValueWhen( Short, PE_CLOSE, 1 );
