//------------------------------------------------------------------------------
//
//  Formula Name:    Double top detection
//  Author/Uploader: <PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-06-16 08:45:38
//  Origin:          Created by <PERSON><PERSON>
//  Keywords:        pattern,recognition,top,bottom
//  Level:           semi-advanced
//  Flags:           commentary
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=19
//  Details URL:     http://www.amibroker.com/library/detail.php?id=19
//
//------------------------------------------------------------------------------
//
//  Detecting patterns is somewhat tricky thing mainly because you can be sure
//  about that only if the pattern is complete. This implies delay in detecting
//  patterns and/or formations and limits usefulness of automatic detection
//  formulas. Anyway I will try to show you how to write pattern recognition
//  formulas using AFL. In this article we will focus on detecting very well
//  known pattern - a double top / double bottom.
//
//  The double top is a major reversal pattern that forms after an extended
//  uptrend. As its name implies, the pattern is made up of two consecutive
//  peaks that are roughly equal, with a moderate trough in between. Although
//  there can be variations, the classic double top marks at least an
//  intermediate change, if not long-term change, in trend from bullish to
//  bearish. Many potential double tops can form along the way up, but until
//  key support is broken, a reversal cannot be confirmed. The whole formation
//  consists of the following parts: prior trend, first peak, trough, second
//  peak, decline from the peak, support break and then decline to the price
//  target. The pattern seems straightforward but there are many details to
//  watch out. For the purpose of this article I will use simplified model of
//  double top pattern that consists of double, roughly equal peaks and the
//  decline from the second peak. So, let's begin.
//
//  AmiBroker Formula Language provides built in functions for detecting peaks
//  and troughs. These functions are based on Zig( array, thresh ) function
//  which you can test by trying the follwing formula:
//
//  graph0 = close;
//
//  graph1= zig( close, 5 );
//
//  As you can see Zig() function determines major peaks and troughs using
//  percentage threshold given as a second parameter. The bigger threshold you
//  specify the more significant peaks and troughs are detected. The line
//  generated by Zig() function indentifies the main trend. There is one caveat
//  however: please take special attention using Zig() function in trading
//  systems because it looks in the future so you will get unrealistic system
//  test results. Zig() function and all function using it (Peak, Trough,
//  PeakBars, TroughBars) are intended for pattern recognition formulas only.
//
//  We will start wrting the formula for detecting double top pattern with
//  detecting peaks:
//
//  percdiff = 10; /* this defines percentage threshold for detecting peaks */
//
//  PK = Peak( H, percdiff, 1 ) == HIGH;
//
//  Now PK array will hold "1" for all bars when peaks occur and "0" elsewhere
//  because high price is equal to the peak value only on the day when this
//  peak occurs.
//
//  Now we want to know if two subsequent peaks are more or less the same:
//
//  peakdiff = ValueWhen( PK, H, 1 )/ValueWhen( PK, H, 2 );
//
//  The peakdiff variable holds now the high price of the most recent peak
//  divided by the high price of the second recent peak. Ideally this value
//  should be 1 - peaks are exactly the same but we will allow slight
//  differences. Let's say we allow the difference of one fourth of the
//  percentage threshold used for detecting peaks:
//
//  validdiff = percdiff/400;
//
//  doubletop = PK AND abs( peakdiff - 1 ) < validdiff;
//
//  Now doubletop variable is "1" if double peak occurred and the difference
//  between the peaks is less than one fourth of the threshold. In our example
//  the threshold in 10% and validdiff is 0.025 (2.5%).
//
//  Everything is fine but soon you find out that this formula is not too good.
//  It detects double tops much too often especially tops that are located to
//  close. For that reason we will add a check for distance between peaks:
//
//  percdiff = 10;
//
//  validdiff = percdiff/400;
//
//  mindistance = 10;
//
//  PK= Peak( H, percdiff, 1 ) == HIGH;
//
//  x = Cum( 1 );
//
//  XPK1 = ValueWhen( PK, x, 1 );
//
//  XPK2 = ValueWhen( PK, x, 2 );
//
//  peakdiff = ValueWhen( PK, H, 1 )/ValueWhen( PK, H, 2 );
//
//  doubletop = PK AND abs( peakdiff - 1 ) < validdiff AND (XPK1 - XPK2)>
//  mindistance;
//
//  The mindistance variable defines minimum number of bars between peaks
//  needed for valid double top formation. XPK1 and XPK2 variables hold the bar
//  number of the first and the second peak.
//
//  Now our formula does not detect peaks located to close but still generates
//  too much signals because it does not check for the validity of the second
//  peak. It just assumes too soon that the peak is valid. To be more sure we
//  need to wait for some days to find out that the second peak is important.
//  One idea is just to check for a couple of days if the price does not return
//  above the level of the peak. So our formula now will look like this:
//
//  percdiff = 10; /* peak detection threshold */
//
//  validdiff = percdiff/400;
//
//  fwdcheck = 4; /* how many days forward to check for valid second peak*/
//
//  mindistance = 10;
//
//  PK= Peak( H, percdiff, 1 ) == HIGH;
//
//  x = Cum( 1 );
//
//  XPK1 = ValueWhen( PK, x, 1 );
//
//  XPK2 = ValueWhen( PK, x, 2 );
//
//  peakdiff = ValueWhen( PK, H, 1 )/ValueWhen( PK, H, 2 );
//
//  doubletop = PK AND abs( peakdiff - 1 ) < validdiff
//
//  AND (XPK1 - XPK2) > mindistance
//
//  AND HIGH > HHV( Ref( H, fwdcheck ), fwdcheck - 1 );
//
//  Note that this formula now references the future ( look at the following
//  statement: Ref( H, fwdcheck ) ) - this means that it will not generate ANY
//  signals until fwdcheck bars pass after the second peak. So, for example, if
//  double top pattern has occured on Monday you will know about that on Friday
//  because four days (future quotes) are needed to confirm the pattern.
//
//  Now our formula for detecting double tops is much better, but still is
//  missing one imporant point - detecting prior trend. That is why it
//  sometimes generates false signals. I will not, however, go any further in
//  this article just to keep things simple. You can use the double top formula
//  in Automatic analysis/Scan function for screening the stocks for potential
//  reversal patterns or in Commentary. By adding buy=doubletop; sell=0;
//  statement to the formula you will be able to screen stocks for the
//  potential reversal patterns and see the arrows when the formula is used in
//  Commentary window.
//
//------------------------------------------------------------------------------

/* Detecting double tops */
percdiff = 5; /* peak detection threshold */
fwdcheck = 5; /* forward validity check */
mindistance = 10;
validdiff = percdiff/400;
PK= Peak( H, percdiff, 1 ) == HIGH;

x = Cum( 1 );
XPK1 =  ValueWhen( PK, x, 1 ); 
XPK2 = ValueWhen( PK, x, 2 ); 

peakdiff = ValueWhen( PK, H, 1 )/ValueWhen( PK, H, 2 );
doubletop = PK AND abs( peakdiff - 1 ) < validdiff AND (XPK1 - XPK2)>mindistance
AND HIGH > HHV( Ref( H, fwdcheck ), fwdcheck - 1 );
buy = doubletop;
sell = 0;

writeif( highest( doubletop ) == 1, "AmiBroker has detected some possible double top patterns for " + name() + "\nLook for green arrows on the price chart.", "There are no double top patterns for " + name() );