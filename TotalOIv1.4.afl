// Expiry Day
_SECTION_BEGIN( "Total OI" );
#include <Common.afl>
#include <Options.afl>
// #include <AlgoFoxAuto/AlgoFoxButton.afl>

SetChartBkColor( colorWhite );
SetOption( "DisableRuinStop", True );

tn = TimeNum();
// pp = Optimize( "pp", 093000, 091600, 095800, 200 );
TradingZone		= ( tn >= 091600 AND tn <= 093000 ) OR ( tn >= 100200 AND tn <= 141400 );
SquareOffTime	= 150000;
DTSquareOff 	= ( TimeNum() >= SquareOffTime );

equityAtRisk = 3000;

MAV = MA( V1, 15 );
HV = HHV( Ref( V, -1 ), 15 );

DayVolMul = round( HighestSince( firstBarOfTheDay, HV ) / HV );
Liquid = V / RLS > 1000;

vol_up 	 = V > MAV AND DayVolMul < 8 AND Liquid;
vol_2_up = ( V + V1 ) > MAV AND DayVolMul < 8 AND Liquid;

vol_up_1 = Ref( vol_up, -1 );

vol_hi = V > HV AND DayVolMul < 8 AND Liquid;
vol_2_hi = Max( V, V1 ) > HV AND DayVolMul < 8 AND Liquid;

vol_hi_1 = Ref( vol_hi, -1 );

SetForeign( OtherSymbol );

price_increasing_3_flip = C > Ref( C, -1 ) AND Ref( Ref( C, -1 ) > O, -1 );
price_increasing_4_flip = C > Ref( C, -2 ) AND price_increasing_3_flip AND Ref( Ref( C, -1 ) > O, -2 );
price_increasing_5_flip = C > Ref( C, -3 ) AND price_increasing_4_flip AND Ref( Ref( C, -1 ) > O, -3 );
price_increasing_6_flip = C > Ref( C, -4 ) AND price_increasing_5_flip AND Ref( Ref( C, -1 ) > O, -4 );

price_decreasing_3_flip = C < Ref( C, -1 ) AND Ref( Ref( C, -1 ) < O, -1 );
price_decreasing_4_flip = C < Ref( C, -2 ) AND price_decreasing_3_flip AND Ref( Ref( C, -1 ) < O, -2 );
price_decreasing_5_flip = C < Ref( C, -3 ) AND price_decreasing_4_flip AND Ref( Ref( C, -1 ) < O, -3 );
price_decreasing_6_flip = C < Ref( C, -4 ) AND price_decreasing_5_flip AND Ref( Ref( C, -1 ) < O, -4 );

total_price_decreasing_flip = price_decreasing_3_flip + price_decreasing_4_flip + price_decreasing_5_flip + price_decreasing_6_flip;
total_price_increasing_flip = price_increasing_3_flip + price_increasing_4_flip + price_increasing_5_flip + price_increasing_6_flip;

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = OptimizeNot( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn_flip = Flip( oi_Below, oi_Above );
oi_up_flip = Flip( oi_Above, oi_Below );
ROC_price = ROC( C, 4 );

new_condition_flip = (
                         ( oi_up_flip ) AND( total_price_increasing_flip >= 2 )
                         OR
                         ( total_price_increasing_flip == 4 AND oi_dn_flip )
                     ) AND ROC_price > 9;
reverse_condition_flip = oi_dn_flip AND( total_price_decreasing_flip >= 2 );
total_reverse_condition_flip = reverse_condition_flip + Ref( reverse_condition_flip, -1 ) + Ref( reverse_condition_flip, -2 ) + Ref( reverse_condition_flip, -3 );
new_condition_flip = (
                         oi_up_flip AND( total_price_increasing_flip >= 2 )
                         OR
                         ( total_price_increasing_flip == 4 AND oi_dn_flip )
                     ) AND ROC_price > 9;

reverse_condition = ( oi_dn_flip AND total_price_decreasing_flip >= 2 AND ROC_price < -9 );

RestorePriceArrays();

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = OptimizeNot( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn = Flip( oi_Below, oi_Above );
oi_up = Flip( oi_Above, oi_Below );
HHV10 = HHV( H1, 30 );
LLV10 = LLV( L1, 30 );

ACDUp 	= myboxC( fcl + Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );
ACDDown	= myboxF( fch - Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );

CP_IH	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( H - C ) / ( .001 + H - L ) > 0.6 ) AND( ( H - O ) / ( .001 + H - L ) > 0.6 ) );
CP_2_IH	= ( ( ( Max(H, H1) - Min(L, L1) ) > 3 * ( O1 - C ) ) AND( ( Max(H, H1) - C ) / ( .001 + Max(H, H1) - Min(L, L1) ) > 0.6 ) AND( ( Max(H, H1) - O ) / ( .001 + Max(H, H1) - Min(L, L1) ) > 0.6 ) );

CP_H	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( C - L ) / ( .001 + H - L ) > 0.6 ) AND( ( O - L ) / ( .001 + H - L ) > 0.6 ) );
CP_EH	= ( ( C1 > O1 ) AND( O > C ) AND( O <= C1 ) AND( O1 <= C ) AND( ( O - C ) < ( C1 - O1 ) ) );
CP_UH 	= ( ( O1 > C1 ) AND( C > O ) AND( C <= O1 ) AND( C1 <= O ) AND( ( C - O ) < ( O1 - C1 ) ) );
CP_LC	= ( abs( C - O ) / ( .001 + H - L ) > .6 );
CP_LC1	= Ref( CP_LC, -1 );

price_up 	= H > HHV10;
price_2_up	= H > HHV10 OR H1 > HHV10;

price_dn = L < LLV10;
price_up1 = Ref( price_up, -1 );

SPD12	= Sum( price_dn, 8 );
SPU12	= Sum( price_up, 8 );

BuyExit = myboxF( LLV10 - 1.5 * boxSize );
BE1 = Ref( BuyExit, -1 );
ShortExit = myboxC( HHV10 + boxSize );
SE1 = Ref( ShortExit, -1 );
ROC_price = ROC( C, 4 );
ROC1 = ROC( C, 1 );

total_oi_increasing = 0;
total_price_increasing = 0;

price_increasing_3 = C > Ref( C, -1 );
price_increasing_4 = C > Ref( C, -2 ) AND price_increasing_3;
price_increasing_5 = C > Ref( C, -3 ) AND price_increasing_4 AND Ref( WhiteBody, -3 );
price_increasing_6 = C > Ref( C, -4 ) AND price_increasing_5 AND Ref( WhiteBody, -4 );
total_price_increasing = price_increasing_3 + price_increasing_4 + price_increasing_5 + price_increasing_6;

price_decreasing_3 = C < Ref( C, -1 ) AND Ref( BlackBody, -1 );;
price_decreasing_4 = C < Ref( C, -2 ) AND price_decreasing_3 AND Ref( BlackBody, -2 );
price_decreasing_5 = C < Ref( C, -3 ) AND price_decreasing_4 AND Ref( BlackBody, -3 );
price_decreasing_6 = C < Ref( C, -4 ) AND price_decreasing_5 AND Ref( BlackBody, -4 );
total_price_decreasing = price_decreasing_3 + price_decreasing_4 + price_decreasing_5 + price_decreasing_6;

oi_increasing_3 = OI > Ref( OI, -1 );
oi_increasing_4 = OI > Ref( OI, -2 ) AND oi_increasing_3;
oi_increasing_5 = OI > Ref( OI, -3 ) AND oi_increasing_4;
oi_increasing_6 = OI > Ref( OI, -4 ) AND oi_increasing_5;

oi_decreasing_3 = OI < Ref( OI, -1 );
oi_decreasing_4 = OI < Ref( OI, -2 ) AND oi_decreasing_3;
oi_decreasing_5 = OI < Ref( OI, -3 ) AND oi_decreasing_4;
oi_decreasing_6 = OI < Ref( OI, -4 ) AND oi_increasing_5;
total_oi_decreasing = oi_decreasing_3 + oi_decreasing_4 + oi_decreasing_5 + oi_decreasing_6;

new_condition1 = (
                     oi_up AND( total_price_increasing >= 2 )
                     OR
                     ( total_price_increasing == 4 AND oi_dn )
                 ) AND ROC_price > 9;
new_condition2 = (
                     ( wick_dn >= 0.6 AND Big AND vol_hi )
                     OR
                     ( CP_H AND vol_hi )
                 ) AND H < stMA;
new_condition = new_condition1 OR new_condition2;

reverse_condition1 = ( oi_dn AND total_price_decreasing >= 2 AND ROC_price < -9 );
reverse_condition2 = (
                         ( wick_size >= 0.5 AND Big AND ( vol_hi OR vol_2_hi) )
                         OR
                         ( wick_size2 >= 0.7 AND Big AND Ref( Big, -1 ) AND( vol_2_up OR vol_2_hi ) )
                         OR
                         ( (CP_IH OR CP_2_IH) AND vol_hi AND price_up )
                     );
reverse_condition = reverse_condition1 OR reverse_condition2;

myVar += ", total_price_decreasing = " + total_price_decreasing;
myVar += ", reverse_condition1 = " + reverse_condition1 + ", reverse_condition2 = " + reverse_condition2;
myVar += ", wick_size = " + wick_size + ", wick_size2 = " + wick_size2 + ", Big " + Big;
/*
*/


tooFar = C > ( BuyExit + 2 * floor( equityAtRisk / RLS ) );
tooFarS = C < ( ShortExit - 2 * floor( equityAtRisk / RLS ) );

ShortCondition1 = reverse_condition1 AND NOT total_reverse_condition_flip AND NOT IsNull( fc[BarCount - 1] ) AND( price_up OR price_up1 );
BuyCondition1	= new_condition1 AND NOT new_condition_flip AND NOT IsNull( fc[BarCount - 1] );

ShortCondition = ( ShortCondition1 OR reverse_condition2 ) AND TradingZone AND L > 40 AND NOT tooFarS;
BuyCondition	= ( BuyCondition1 OR new_condition2 ) AND TradingZone AND L > 10 AND NOT tooFar AND WhiteBody;

BuyP = ShortP = 0;
InShort = InBuy = 0;
ShortI = BuyI = BuyL = ShortL = 0;
Short = Cover = 0 ;
Buy = Sell = 0;

BuyFlag = ShortFlag = False;
BF = SF = 0;
SPT = LPT = 0;
HatBC = LatSC = LatBC = HatSC = 0;
SellPrice = CoverPrice = C;
BTC = STC = 0;
SOI = BOI = 0;

for( i = 10; i < BarCount; i++ )
{
    if( InBuy )
    {
        if( O[i] > BuyExit[i] )
        {
            BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
        }

        if( reverse_condition[i] )
        {
            BuyExit[i] = Max( BuyExit[i], floor( ( L[i] - boxSize[i] ) / boxSize[i] ) * boxSize[i] );
        }

        if( H[i] > BuyP + 40 && BlackBody[i] )
        {
            BuyExit[i] = Max( BuyExit[i], floor( L[i] / boxSize[i] ) * boxSize[i] );
        }

        if( L[i] < BuyExit[i] )
        {
            Sell[i] = 1;
            InShort = 0;
            InBuy = 0;
            BuyFlag = False;
            ShortFlag = False;
            BuyI = i;
            SellPrice[i] = IIf( O[i] > BuyExit[i], BuyExit[i], C[i] );
        }

        if( BigBlack[i] AND C[i] < BuyP )
        {
            Sell[i] = 1;
            InShort = 0;
            InBuy = 0;
            BuyFlag = False;
            ShortFlag = False;
            BuyI = i;
        }
    }

    if( BuyFlag )
    {
        HatBC[i] = Min( HatBC[i - 1], H[i] );
    }

    if( BuyCondition[i] && !InBuy && !InShort )
    {
        if( !BuyFlag )
        {
            HatBC[i] = H[i];
        }

        LatBC = floor( ( L[i] - boxSize[i] ) / boxSize[i] ) * boxSize[i];

        ShortFlag = False;
        BuyFlag = True;
        BuyI = i;
        BuyL = Min( L[i - 1], L[i] );
    }

    if( !TradingZone[i] || i > BuyI + 8 || L[i] < LatBC )
    {
        BuyFlag = False;
    }

    // Execute Buy
    if( BuyFlag && !CP_IH[i] && wick_size[i] <= 0.4 && ( ( C[i] < stMA1[i] && vol_up[i] ) || ( m_Above[i] AND oi_up[i] ) ) )
    {
        Buy[i] = 1;
        BuyP = C[i];
        BuyI = i;
        BuyExit[i] = Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] );
        InBuy = 1;
        InShort = 0;
        BuyFlag = False;
        ShortFlag = False;
        LPT = C[i] + 0.7 * floor( equityAtRisk / RLS );
        BTC = False;
        BOI = OI[i];
    }

    // Logic for Short
    if( InShort )
    {
        if( O[i] < ShortExit[i] )
        {
            ShortExit[i] = Min( ShortExit[i], ShortExit[i - 1] );
        }

        if( new_condition[i] )
        {
            ShortExit[i] = Min( ShortExit[i], floor( ( H[i] + boxSize[i] ) / boxSize[i] ) * boxSize[i] );
        }

        if( ROC_price[i] > 13 OR ROC1[i] > 14 )
        {
            Cover[i] = 1;
            InBuy = 0;
            InShort = 0;
            ShortFlag = False;
            BuyFlag = False;
            ShortI = i;
        }

        if( H[i] > ShortExit[i] )
        {
            Cover[i] = 1;
            InBuy = 0;
            InShort = 0;
            ShortFlag = False;
            BuyFlag = False;
            ShortI = i;
            CoverPrice[i] = IIf( O[i] < ShortExit[i], ShortExit[i], C[i] );
        }
    }

    if( ShortFlag )
    {
        HatSC[i] = Max( HatSC[i - 1], L[i] );
    }

    if( ShortCondition[i] && !InShort )
    {
        if( !ShortFlag )
        {
            HatSC[i] = L[i];
        }

        LatSC = floor( ( H[i] + boxSize[i] ) / boxSize[i] ) * boxSize[i];

        BuyFlag = False;
        ShortFlag = True;
        ShortI = i;
        ShortL = Max( H[i - 1], H[i] );
    }

    if( !TradingZone[i] || i > ShortI + 8 || ( H[i] > LatSC AND NOT CP_IH[i] ) && vol_up[i] )
    {
        ShortFlag = False;
    }

    // Execute Short
    if( ShortFlag && C[i] <= HatSC[i] && BlackBody[i] )
    {
        Short[i] = 1;
        ShortP = C[i];
        ShortI = i;
        ShortExit[i] = floor( IIF( CP_IH[i], C[i] + floor( equityAtRisk / RLS ), Min( C[i] + floor( equityAtRisk / RLS ), ShortExit[i] ) ) );
        InShort = 1;
        InBuy = 0;
        ShortFlag = False;
        BuyFlag = False;
    }

    if( DTSquareOff[i] )
    {
        BuyP = 0;
        ShortP = 0;
        InBuy = 0;
        InShort = 0;
    }

    BF[i] = BuyFlag;
    SF[i] = ShortFlag;
}

Sell = Sell OR DTSquareOff OR Short;
Cover = Cover OR DTSquareOff OR C < 5 OR Buy;
Buy = Buy AND isITM AND( ITMRange == 2 OR ITMRange == 1 ) AND daysToExpiry < 7;
Short = Short AND isITM AND( ITMRange == 2 OR ITMRange == 1 ) AND daysToExpiry < 7;
Buy = Buy AND daysToExpiry < 7;
Short = Short AND daysToExpiry < 7;
InBuy = InShort = 0;
BuyPrice = ShortPrice = C;

SetChartBkColor( LastValue( IIf( isITM AND( ITMRange == 3 OR ITMRange == 2 ) , colorWhite, colorPink ) ) );

Plot( C, "", ParamColor( "Color", colorBlack ), styleCandle );
Plot( ShortExit, "SE", IIf( SF, colorRed, colorGrey40 ), styleDashed );
Plot( BuyExit, "BE", IIf( BF, colorLime, colorGrey40 ), styleDashed );
PlotShapes( BuyCondition*shapeSmallCircle, colorGreen, 0, L - 1 );
PlotShapes( ShortCondition*shapeSmallCircle, colorRed, 0, H + 20 );
Plot( stMA, "", IIf( m_Above, colorLime, IIf( m_Below, colorBrown, colorWhite ) ), styleLine | styleThick );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

bRisk = BuyPrice - BuyExit;
bLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) ) ) );

sRisk = ShortExit - ShortPrice;
sLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) ) ) );
myvar 	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", BuyExit, ShortExit );
myvar 	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
#include <Alert.afl>

_N( Title = StrFormat( EncodeColor( colorDarkGrey )
                       + "H %0.2f, "
                       + "L %0.2f, "
                       + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                       + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                       + EncodeColor( colorDarkGrey ) + myVar
                       , H, L, C, ROC( C, 1 )
                     ) );

_SECTION_END();

_SECTION_BEGIN( "Total OI" );

GfxSelectFont( "BOOK ANTIQUA", 10, 100 );
GfxSetBkMode( 1 );
GfxSetTextColor( colorWhite );
GfxSelectSolidBrush( colorDarkGrey );

GfxSelectPen( colorLightBlue, 1 ); // border color
pxHeight = Status( "pxchartheight" ) ;
xx = Status( "pxchartwidth" );
x = 20;
x2 = 105;
y = pxHeight;
GfxRoundRect( x, y - 45, x2, y - 10, 7, 7 ) ;
GfxTextOut( "CE: " + NumToStr( SelectedValue( nearestITM_CE ), 1.0 ), x + 5, y - 45 );
GfxTextOut( "PE: " + NumToStr( SelectedValue( nearestITM_PE ), 1.0 ), x + 5, y - 30 );

#include <AlgoFoxAuto/AlgoFoxAutoOpt.afl>
