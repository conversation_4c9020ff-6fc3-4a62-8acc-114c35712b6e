/***************************************
** Strategy: Simple Line Momentum v1.3
** Logic:    Enter long on 2 up-moves of
** BreakoutLine without a
** down-move in BreakdownLine.
***************************************/

OptimizerSetEngine( "cmae" );

_SECTION_BEGIN("Params");
LookbackPeriod = Optimize("Lookback Period", 17, 21, 22, 1);

// -- Custom Bar (Box) Parameters --
boxSizePercent = Optimize("Box %", 0.7, 0.1, 3, 0.1);
boxSizeORbFactor = Optimize("ORB ATR factor", 0.8, 0.2, 3, 0.2);
_SECTION_END();


_SECTION_BEGIN("Custom Bar OHLC");
firstBar = Day() != Ref(Day(), -1);
ORbHigh = ValueWhen(firstBar, High);
ORbLow = ValueWhen(firstBar, Low);
ORbCenter = ValueWhen(firstBar, (ORbHigh + ORbLow) / 2);
tr = ATR(5 * LookbackPeriod);
ORBAtr = ValueWhen(firstBar, tr);

boxSize = round(Max(boxSizePercent * ORbCenter / 100, boxSizeORbFactor * ORBAtr) / 5 ) * 5;

// Rounded Custom Bars
rH = round(H / boxSize);
myH = boxSize * IIf(rH < H / boxSize, rH + 1, rH);

rL = round(L / boxSize);
myL = boxSize * IIf(rL > L / boxSize, rL - 1, rL);

rO = round(O / boxSize);
myO = boxSize * IIf(rO > O / boxSize, rO - 1, rO);

rC = round(C / boxSize);
myC = boxSize * IIf(rC > C / boxSize, rC - 1, rC);

// Adjust myC to wick edges
myC = IIf(myH - C < boxSize / 3, myH, myC);
myC = IIf(C - myL < boxSize / 3, myL, myC);
_SECTION_END();


_SECTION_BEGIN("Trade Logic");
// --- Indicator Lines ---
BreakoutLine = HHV(myH, LookbackPeriod);
BreakdownLine = LLV(myL, LookbackPeriod);

// --- Entry and Exit Conditions ---
// -- Long Logic --
upMove = BreakoutLine > Ref(BreakoutLine, -1);
resetLongCount = BreakdownLine < Ref(BreakdownLine, -1);

barsSinceReset = BarsSince(resetLongCount);
upMovesSinceReset = Sum(upMove, barsSinceReset + 1);

Buy = upMovesSinceReset == 2;
Sell = BreakdownLine < Ref(BreakdownLine, -1);


// -- Short Logic --
downMove = BreakdownLine < Ref(BreakdownLine, -1);
resetShortCount = BreakoutLine > Ref(BreakoutLine, -1);

barsSinceShortReset = BarsSince(resetShortCount);
downMovesSinceReset = Sum(downMove, barsSinceShortReset + 1);

Short = downMovesSinceReset == 2;
Cover = BreakoutLine > Ref(BreakoutLine, -1);

Sell = Sell OR Short;
Cover = Cover OR Buy;

// --- Clean and manage signals ---
Buy = ExRem(Buy, Sell);
Sell = ExRem(Sell, Buy);

Short = ExRem(Short, Cover);
Cover = ExRem(Cover, Short);

BuyPrice = C;
SellPrice = C;
ShortPrice = C;
CoverPrice = C;
SetPositionSize(RoundLotSize, spsShares);
_SECTION_END();


_SECTION_BEGIN("Plotting");
SetChartOptions(0, chartShowArrows | chartShowDates);
PlotOHLC(myO, myH, myL, myC, "CustomBars", colorDefault, styleCandle);

Plot(BreakoutLine, "Breakout Line", colorBlue, styleThick);
Plot(BreakdownLine, "Breakdown Line", colorRed, styleThick);

// --- Plot Buy and Sell arrows ---
PlotShapes(IIf(Buy, shapeUpArrow, shapeNone), colorGreen, 0, myL, -20);
PlotShapes(IIf(Sell, shapeDownArrow, shapeNone), colorYellow, 0, myH, 20);
PlotShapes(IIf(Short, shapeDownArrow, shapeNone), colorRed, 0, myH, 20);
PlotShapes(IIf(Cover, shapeUpArrow, shapeNone), colorYellow, 0, myL, -20);

Title = StrFormat("{{NAME}} - {{INTERVAL}} | O: %g, H: %g, L: %g, C: %g (%.2f%%) | Vol: %g | box: %g",
    O, H, L, C, SelectedValue(ROC(C, 1)), V, boxSize);
_SECTION_END();
