function GetExpiryDate(symbol)
{
    // Extract year and month from symbol (e.g., "NIFTY25FEB21000CE" -> "25FEB")
    year = StrToNum(StrLeft(StrRight(Name(), 7), 2)); // Get year (23, 24, 25, etc.)
    if(year < 50) year = year + 2000; // Convert to full year

    // Get the expiry day and month
    monthStr = StrMid(symbol, 5, 3); // Extract month string (JAN, FEB, etc.)
    day = StrToNum(StrMid(symbol, 3, 2)); // Extract day

    // Convert month string to number
    month = 0;
    if(monthStr == "JAN") month = 1;
    if(monthStr == "FEB") month = 2;
    if(monthStr == "MAR") month = 3;
    if(monthStr == "APR") month = 4;
    if(monthStr == "MAY") month = 5;
    if(monthStr == "JUN") month = 6;
    if(monthStr == "JUL") month = 7;
    if(monthStr == "AUG") month = 8;
    if(monthStr == "SEP") month = 9;
    if(monthStr == "OCT") month = 10;
    if(monthStr == "NOV") month = 11;
    if(monthStr == "DEC") month = 12;

    // Format the date string (YYYY-MM-DD)
    dateStr = NumToStr(year, 1.0) + "-" + 
              WriteIf(month < 10, "0", "") + NumToStr(month, 1.0) + "-" + 
              WriteIf(day < 10, "0", "") + NumToStr(day, 1.0);
    
    // Format the time string (HH:MM:SS)
    timeStr = "15:30:00"; // Market close time

    // Convert to DateTime
    expiryDate = DateTimeConvert("YYYY-MM-DD", dateStr, timeStr);
    
    return expiryDate;
}

/* Example usage:
symbolName = Name(); // e.g., "NIFTY25FEB21000CE"
expiryDate = GetExpiryDate(symbolName);
daysToExpiry = DateTimeDiff(expiryDate, DateTime()) / (24 * 60 * 60); // in days
*/
