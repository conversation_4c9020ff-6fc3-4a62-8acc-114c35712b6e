TimeFrameSet(inDaily); 
WMA14_oi = WMA(OI, 14);
TimeFrameRestore(); 
OI_d     = TimeFrameGetPrice("I", inDaily, 0, expandLast); 
WMA14_oi = TimeFrameExpand(WMA14_oi, inDaily, expandFirst); 

lastBarOfTheDayTime = 150000; // Exit all trades
lastBarOfTheDay = (TimeNum() >= lastBarOfTheDayTime);
MarketOpenTime = 091500; // Exit all trades
FirstTradeOfTheDayTime = 102000; // Exit all trades
FirstTradeOfTheDay = (TimeNum() >= FirstTradeOfTheDayTime);
TradingHours = FirstTradeOfTheDay AND NOT lastBarOfTheDay;

_SECTION_BEGIN("Open Interest");
Plot( OI_d/RoundLotSize, "OI", colorYellow, styleLine | styleOwnScale );

RefBarTime = 100000; // Exit all trades
OI_d = ValueWhen(TimeNum() <= MarketOpenTime, OI);
rel_OI = (OI/OI_d - 1)*100;
stMA = rel_OI;

m_fill  = 0.02; 
m_PL    = stMA*(1 - m_fill); 
m_PH    = stMA*(1 + m_fill); 

m_AH    = LLV(m_PH, 1 + Nz(BarsSince(stMA < Ref(stMA, -1)), 1));
m_AL    = HHV(m_PL, 1 + Nz(BarsSince(stMA > Ref(stMA, -1)), 1));

m_Above = stMA > Ref(m_AH, -1);
m_Below = stMA < Ref(m_AL, -1);

m_Above = Flip(m_Above, m_Below);
m_Below = Flip(m_Below, m_Above);

Plot( rel_OI, "Rel Open", IIf(m_Above, colorLime, IIf(m_Below, colorRed, colorLavender)), styleLine );
//Plot( rel_OI, "Rel Open", colorWhite, styleLine );
//Plot( m_AH, "m_AL", colorRed, styleLine );
//Plot( m_AL, "m_AL", colorLime, styleLine );


_SECTION_END();
myVar = "OI_d = " + OI_d/RoundLotSize;
myVar += ", OI = " + OI/RoundLotSize;
Plot((OI/ValueWhen(TimeNum() <= RefBarTime, OI)-1)*100, "Rel 10AM", colorOrange,styleHistogram);

_N( Title   = StrFormat( EncodeColor( colorWhite ) + myVar + ", " + EncodeColor( -1 ) + "{{VALUES}}" ) );
