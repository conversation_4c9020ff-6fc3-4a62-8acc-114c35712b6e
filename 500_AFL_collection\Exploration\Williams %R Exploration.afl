//------------------------------------------------------------------------------
//
//  Formula Name:    Williams %R Exploration
//  Author/Uploader: <PERSON><PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2006-10-01 10:26:37
//  Origin:          Williams %R
//  Keywords:        
//  Level:           medium
//  Flags:           exploration
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=727
//  Details URL:     http://www.amibroker.com/library/detail.php?id=727
//
//------------------------------------------------------------------------------
//
//  This exploration may help identify stocks that one might like to enter a
//  position into.
//
//------------------------------------------------------------------------------

_SECTION_BEGIN("William's R");
function PercentR( periods )
{
 return 100 * ( HHV( H, periods ) - C )/( HHV( H, periods ) - LLV( L, periods ) ); 
}

Plot( PercentR( Param("Periods", 10, 2, 100 ) ),
		_DEFAULT_NAME(),
		ParamColor("Color", ColorCycle ) );
_SECTION_END();

P  =  ((H + L + C) / 3);
R1  =  ((2 * P) - L);
S1  =  ((2 * P)  - H);
R2  =  ((P - S1) + R1);
S2  =  (P - (R1 - S1));

Filter = PercentR( Param("Periods", 10, 2, 100 ) ) > 80;

AddColumn( V, "Volume",1 );
AddColumn( C, "Close" );
AddColumn( Ref (C, -1), "Last Close");
AddColumn( C - Ref( C, -1), "Change");
AddColumn( ROC( C, 1 ), "ROC" ); 
AddColumn (RSI(), "RSI",1.2);
AddColumn( r2, "R 2", 1.2);
AddColumn( r1, "R 1", 1.2);
AddColumn( P, "Pivot", 1.2);
AddColumn( S1, "S 1", 1.2);
AddColumn( S2, "S 2", 1.2);