//------------------------------------------------------------------------------
//
//  Formula Name:    <PERSON>hlers Dominant Cycle Period
//  Author/Uploader: Not Too Swift 
//  E-mail:          
//  Date/Time Added: 2005-04-23 00:31:49
//  Origin:          
//  Keywords:        
//  Level:           semi-advanced
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=460
//  Details URL:     http://www.amibroker.com/library/detail.php?id=460
//
//------------------------------------------------------------------------------
//
//  This is another calculation from <PERSON><PERSON><PERSON>'s "Cybernetic Analysis for Stocks
//  and Futures." It returns the dominant period length.
//
//  This code is not elegant. I would greatly appreciate help in making it more
//  transparent and elegant.
//
//------------------------------------------------------------------------------

SetBarsRequired(200, 0);

// Ehlers Dominant Cycle Period
// from Ehlers, John F. Cybernetic Analysis for Stocks and Futures. Wiley. 2004. 
// Chapter 9, p. 107. Code on p. 111.

function CyclePeriod(array, alpha)
// Figure 9.4 on p. 111
{
  smooth = (array + 2*Ref(array, -1) + 2*Ref(array, -2) + Ref(array, -3))/6;

//  for(i = 0; i < 7; i++) cycle[i]=array[i]; // Initialize early values and as array

  for(i = 0; i < 6; i++) 
  {
     InstPeriod[i] = 0; // Initialize early values and as array
     DeltaPhase[i] = 0;
     cycle[i]=0;
     Period[i]=0;
  }

  for(i = 6; i < BarCount; i++)
  {
     cycle[i] = (1 - .5*alpha)*(1 - .5*alpha)*(smooth[i] - 2*smooth[i-1] + smooth[i-2]) +
                2*(1 - alpha)*cycle[i-1] - (1 - alpha)*(1 - alpha)*cycle[i-2];
     Q1[i] = (.0962*cycle[i] + .5769*cycle[i-2] -.5769*cycle[i-4] - .0962*cycle[i-6])*(.5 + .08*InstPeriod[i-1]);
     I1[i] = cycle[i-3];

     if(Q1[i] != 0 AND Q1[i-1] != 0) 
        DeltaPhase[i] = (I1[i]/Q1[i] - I1[i-1]/Q1[i-1])/(1 + I1[i]*I1[i-1]/(Q1[i]*Q1[i-1]));
     if(DeltaPhase[i] < 0.1) DeltaPhase[i] = 0.1;
     if(DeltaPhase[i] > 1.1) DeltaPhase[i] = 1.1;

     //----- Speed up the median calculation by placing it inline

     mlen = 5;
     for(k = mlen - 1; k >= 0; k--) {temparray[k] = DeltaPhase[i + k - (mlen - 1)];}

     temp=0;
     for(k = mlen - 1; k > 0; k--)
     {for (j = mlen - 1; j > 0; j--)
       {if (temparray[j-1] > temparray[j])
         {
           temp = temparray[j-1];
           temparray[j-1] = temparray[j];
           temparray[j] = temp;
         }
       }
     }
     MedianDelta[i] = temparray[mlen - 1 - (mlen / 2)];

     //----- End median calculation

     if(MedianDelta[i] == 0) DC[i] = 15; 
     else DC[i] = 6.28318/MedianDelta[i] + .5;

     InstPeriod[i] = .33*DC[i] + .67*InstPeriod[i-1];
     Period[i] = .15*InstPeriod[i] + .85*Period[i-1];
  }
  return Period;
}

Med = (H+L)/2;

// CyclePeriod
CP = CyclePeriod(Med, .07);
Plot(CP, "CyclePeriod", colorRed, styleLine);