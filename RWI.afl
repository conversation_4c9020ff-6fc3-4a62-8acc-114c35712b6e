function JMA( array, per )
{
    TN1 = MA( array, per );
    s1 = 0;

    for( i = 0; i < per; i = i + 1 )
    {
        s1 = s1 + ( ( per - ( 2 * i ) - 1 ) / 2 ) * Ref( array, -i );
    }

    return TN1 + ( ( ( per / 2 ) + 1 ) * S1 ) / ( ( per + 1 ) * per );
}

JMAperiods = Param( "JMA Periods", 15, 5, 50, 10 );
minperiods = Optimize( "Min Periods", 55, 5, 100, 10 );
maxperiods = minperiods + Optimize( "Max Periods", 15, 5, 100, 10 );

Plot(0.85,"",colorBlack,styleLine);
_SECTION_BEGIN("RWILO");
varLo = RWILo( minperiods, maxperiods);
JMA_varLo = JMA(varLo ,JMAperiods);
Plot(JMA_varLo, _DEFAULT_NAME(), ParamColor( "Color", colorCycle ),
ParamStyle("Style") );
_SECTION_END();

_SECTION_BEGIN("RWIHI");
varHi = RWIHi( minperiods, maxperiods) ;
JMA_varHi = JMA(varHi ,JMAperiods);
Plot(JMA_varHi, _DEFAULT_NAME(), ParamColor( "Color", colorCycle
), ParamStyle("Style") );
_SECTION_END();

//Title = "Daily - RWI Custom";

mode = Flip(JMA_varHi > 0.85, JMA_varLo > 0.85);
Plot(mode,"",colorBlue,styleLine);
Buy = Cover = mode;
Short = Sell = NOT mode;

SetPositionSize(RoundLotSize, spsShares); 
