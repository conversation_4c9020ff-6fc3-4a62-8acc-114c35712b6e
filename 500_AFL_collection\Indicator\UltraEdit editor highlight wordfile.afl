//------------------------------------------------------------------------------
//
//  Formula Name:    UltraEdit editor highlight wordfile
//  Author/Uploader: <PERSON> 
//  E-mail:          
//  Date/Time Added: 2004-11-15 13:57:33
//  Origin:          Ported to UltraEdit from my update of <PERSON>'s original ConTEXT version in the library.
//  Keywords:        UltraEdit editor highlight wordfile
//  Level:           semi-advanced
//  Flags:           system,exploration,indicator,commentary,function
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=404
//  Details URL:     http://www.amibroker.com/library/detail.php?id=404
//
//------------------------------------------------------------------------------
//
//  UltraEdit is a full-featured shareware programmer's editor. This file
//  teaches it AFL's keywords, so it can color, format, captitalize, and
//  auto-complete them like AmiBroker does. I think it's up to date as of
//  AmiBroker version 4.63.1 Beta.
//
//  I often keep my standard AFL #include file open in it while working in AB.
//  Try showing the function list in UltraEdit with an AFL file open. This
//  wordfile should let UE find all your functions and procedures, so you can
//  see their parameters or navigate quickly to them.
//
//  UltraEdit is available at http://www.ultraedit.com.
//
//  HELP WANTED!
//
//  Since there doesn't appear to be any direct way to capture all the keywords
//  used by the current version of AB, maintenance of this file is dependant on
//  someone noticing missing items. So, any additions and corrections are most
//  welcome (:-). Please write directly to dmerrill at usa dot net, in case I
//  don't come back here right away.
//
//------------------------------------------------------------------------------

/L20"AFL" Line Comment = // Block Comment On = /* Block Comment Off = */ Escape Char = \ File Extensions = AFL
/Delimiters = ~!@%^&*()-+=|\/{}[]:;"'<> ,	.?
/Function String = "%function^([~^{]+)"
/Function String 1 = "%procedure^([~^{]+)"
/Indent Strings = "{"
/Unindent Strings = "}"

/C1"Logical"
AND
False
NOT
Null
OR
True

/C2"Flow of control, scope"
do
else
for
function
global
if
local
procedure
return
while

/C3"Preprocessor"
#include
#pragma

/C4"Built-in function"
ADLine
ADX
AMA
AMA2
ATR
AccDist
AddColumn
AddTextColumn
AddToComposite
AdvIssues
AdvVolume
AlertIf
ApplyStop
BBandBot
BBandTop
BarIndex
BarsSince
BeginValue
CCI
CategoryAddSymbol
CategoryGetName
CategoryGetSymbols
CategoryRemoveSymbol
Chaikin
ClipboardGet
ClipboardSet
Correlation
CreateObject
CreateStaticObject
Cross
Cum
DEMA
Date
DateNum
DateTime
Day
DayOfWeek
DayOfYear
DecIssues
DecVolume
Divergence
EMA
EnableRotationalTrading
EnableScript
EnableTextOutput
EncodeColor
EndValue
Equity
ExRem
ExRemSpan
Flip
Foreign
FullName
GapDown
GapUp
GetBaseIndex
GetCategorySymbols
GetChartID
GetDatabaseName
GetExtraData
GetOption
GetRTData
GetScriptObject
GetTradingInterface
GroupID
HHV
HHVBars
Highest
HighestBars
HighestSince
HighestSinceBars
Hold
Hour
IIf
InWatchList
IndustryID
Inside
Interval
IsContinuous
IsEmpty
IsFavorite
IsFinite
IsIndex
IsNan
IsNull
IsTrue
LLV
LLVBars
LastValue
LinRegIntercept
LinRegSlope
LineArray
LinearReg
Lowest
LowestBars
LowestSince
LowestSinceBars
MA
MACD
MDI
MFI
MarketID
Max
Median
Min
Minute
Month
NVI
Name
NoteGet
NoteSet
Now
NumToStr
Nz
OBV
Optimize
OscP
OscV
Outside
PDI
PVI
Param
ParamColor
ParamDate
ParamField
ParamList
ParamStr
ParamStyle
ParamTime
ParamToggle
ParamTrigger
Peak
PeakBars
Percentile
Plot
PlotForeign
PlotGrid
PlotOHLC
PlotShapes
PlotVAPOverlay
Prec
Prefs
RMI
ROC
RSI
RSIa
RWI
RWIHi
RWILo
Random
Ref
RelStrength
RestorePriceArrays
SAR
Second
SectorID
SelectedValue
SetBarsRequired
SetChartOptions
SetForeign
SetFormulaName
SetOption
SetTradeDelays
Signal
StDev
StaticVarGet
StaticVarGetText
StaticVarSet
StaticVarSetText
Status
StdErr
StochD
StochK
StrExtract
StrFind
StrFormat
StrLeft
StrLen
StrMid
StrRight
StrToNum
Study
Sum
TEMA
TSF
TimeFrameCompress
TimeFrameExpand
TimeFrameGetPrice
TimeFrameRestore
TimeFrameSet
TimeNum
Trin
Trix
Trough
TroughBars
Ultimate
UncIssues
UncVolume
ValueWhen
VarGet
VarSet
Version
WMA
Wilders
WriteIf
WriteVal
Year
Zig
_DEFAULT_NAME()
_N
_PARAM_VALUES 
_SECTION_BEGIN()
_SECTION_END()
_SECTION_NAME()
_TRACE
abs
acos
atan
ceil
cos
exp
fclose
fdelete
feof
fgets
floor
fmkdir
fopen
fputs
frac
frmdir
int
log
log10
printf
round
sign
sin
sqrt
tan

/C5"Reserved variable"
Avg
BarCount
Buy
BuyPrice
C
Close
Column0
Column0Format
Column0
Column0Format
Column0Name
Column1
Column1Format
Column1Name
Column2
Column2Format
Column2Name
Column3
Column3Format
Column3Name
Column4
Column4Format
Column4Name
Column5
Column5Format
Column5Name
Column6
Column6Format
Column6Name
Column7
Column7Format
Column7Name
Column8
Column8Format
Column8Name
Column9
Column9Format
Column9Name
Column10
Column10Format
Column10Name
Column11
Column11Format
Column11Name
Column12
Column12Format
Column12Name
Column13
Column13Format
Column13Name
Column14
Column14Format
Column14Name
Column15
Column15Format
Column15Name
Column16
Column16Format
Column16Name
Column17
Column17Format
Column17Name
Column18
Column18Format
Column18Name
Column19
Column19Format
Column19Name
Column20
Column20Format
Column20Name
Column21
Column21Format
Column21Name
Column22
Column22Format
Column22Name
Column23
Column23Format
Column23Name
Column24
Column24Format
Column24Name
Column25
Column25Format
Column25Name
Column26
Column26Format
Column26Name
Column27
Column27Format
Column27Name
Column28
Column28Format
Column28Name
Column29
Column29Format
Column29Name
Cover
CoverPrice
Exclude
Filter
Graph0
Graph0BarColor
Graph0Color
Graph0High
Graph0Low
Graph0Max
Graph0Min
Graph0Name
Graph0Open
Graph0Style
Graph1
Graph1BarColor
Graph1Color
Graph1High
Graph1Low
Graph1Max
Graph1Min
Graph1Name
Graph1Open
Graph1Style
Graph2
Graph2BarColor
Graph2Color
Graph2High
Graph2Low
Graph2Max
Graph2Min
Graph2Name
Graph2Open
Graph2Style
Graph3
Graph3BarColor
Graph3Color
Graph3High
Graph3Low
Graph3Max
Graph3Min
Graph3Name
Graph3Open
Graph3Style
Graph4
Graph4BarColor
Graph4Color
Graph4High
Graph4Low
Graph4Max
Graph4Min
Graph4Name
Graph4Open
Graph4Style
Graph5
Graph5BarColor
Graph5Color
Graph5High
Graph5Low
Graph5Max
Graph5Min
Graph5Name
Graph5Open
Graph5Style
Graph6
Graph6BarColor
Graph6Color
Graph6High
Graph6Low
Graph6Max
Graph6Min
Graph6Name
Graph6Open
Graph6Style
Graph7
Graph7BarColor
Graph7Color
Graph7High
Graph7Low
Graph7Max
Graph7Min
Graph7Name
Graph7Open
Graph7Style
Graph8
Graph8BarColor
Graph8Color
Graph8High
Graph8Low
Graph8Max
Graph8Min
Graph8Name
Graph8Open
Graph8Style
Graph9
Graph9BarColor
Graph9Color
Graph9High
Graph9Low
Graph9Max
Graph9Min
Graph9Name
Graph9Open
Graph9Style
Graph10
Graph10BarColor
Graph10Color
Graph10High
Graph10Low
Graph10Max
Graph10Min
Graph10Name
Graph10Open
Graph10Style
Graph11
Graph11BarColor
Graph11Color
Graph11High
Graph11Low
Graph11Max
Graph11Min
Graph11Name
Graph11Open
Graph11Style
Graph12
Graph12BarColor
Graph12Color
Graph12High
Graph12Low
Graph12Max
Graph12Min
Graph12Name
Graph12Open
Graph12Style
Graph13
Graph13BarColor
Graph13Color
Graph13High
Graph13Low
Graph13Max
Graph13Min
Graph13Name
Graph13Open
Graph13Style
Graph14
Graph14BarColor
Graph14Color
Graph14High
Graph14Low
Graph14Max
Graph14Min
Graph14Name
Graph14Open
Graph14Style
Graph15
Graph15BarColor
Graph15Color
Graph15High
Graph15Low
Graph15Max
Graph15Min
Graph15Name
Graph15Open
Graph15Style
Graph16
Graph16BarColor
Graph16Color
Graph16High
Graph16Low
Graph16Max
Graph16Min
Graph16Name
Graph16Open
Graph16Style
Graph17
Graph17BarColor
Graph17Color
Graph17High
Graph17Low
Graph17Max
Graph17Min
Graph17Name
Graph17Open
Graph17Style
Graph18
Graph18BarColor
Graph18Color
Graph18High
Graph18Low
Graph18Max
Graph18Min
Graph18Name
Graph18Open
Graph18Style
Graph19
Graph19BarColor
Graph19Color
Graph19High
Graph19Low
Graph19Max
Graph19Min
Graph19Name
Graph19Open
Graph19Style
GraphXSpace
GraphZOrder
H
High
L
Low
MarginDeposit
MaxGraph
NumColumns
O
OI
Open
OpenInt
PointValue
PositionScore
PositionSize
RoundLotSize
Sell
SellPrice
Short
ShortPrice
TickSize
Title
V
Volume

/C6"Built-in constant"
atcFlagCompositeGroup
atcFlagDefaults
atcFlagDeleteValues
atcFlagEnableInBacktest
atcFlagEnableInExplore
atcFlagResetValues
atcFlagTimeStamp
chartShowDates
categoryFavorite
categoryGroup
categoryIndex
categoryIndustry
categoryMarket
categorySector
categoryWatchlist
chartLogarithmic
chartShowArrows
colorAqua
colorBlack
colorBlue
colorBlueGrey
colorBrightGreen
colorBrown
colorCustom1
colorCustom10
colorCustom11
colorCustom12
colorCustom13
colorCustom14
colorCustom15
colorCustom16
colorCustom2
colorCustom3
colorCustom4
colorCustom5
colorCustom6
colorCustom7
colorCustom8
colorCustom9
colorCycle
colorDarkBlue
colorDarkGreen
colorDarkGrey
colorDarkOliveGreen
colorDarkRed
colorDarkTeal
colorDarkYellow
colorDefault
colorGold
colorGreen
colorGrey40
colorGrey50
colorIndigo
colorLavender
colorLightBlue
colorLightGrey
colorLightOrange
colorLightYellow
colorLime
colorOrange
colorPaleBlue
colorPaleGreen
colorPaleTurquoise
colorPink
colorPlum
colorRed
colorRose
colorSeaGreen
colorSkyblue
colorTan
colorTeal
colorTurquoise
colorViolet
colorWhite
colorYellow
compressHigh
compressLast
compressLow
compressOpen
compressVolume
expandFirst
expandLast
expandPoint
formatChar
formatDateTime
in15Minute
in1Minute
in5Minute
inDaily
inHourly
inMonthly
inWeekly
maskAll
maskDefault
maskHistogram
maskPrice
scoreNoRotate
shapeCircle
shapeDigit0
shapeDigit1
shapeDigit2
shapeDigit3
shapeDigit4
shapeDigit5
shapeDigit6
shapeDigit7
shapeDigit8
shapeDigit9
shapeDownArrow
shapeDownTriangle
shapeHollowCircle
shapeHollowDownArrow
shapeHollowDownTriangle
shapeHollowSmallCircle
shapeHollowSmallDownTriangle
shapeHollowSmallSquare
shapeHollowSmallUpTriangle
shapeHollowSquare
shapeHollowStar
shapeHollowUpArrow
shapeHollowUpTriangle
shapeNone
shapePositionAbove
shapeSmallCircle
shapeSmallDownTriangle
shapeSmallSquare
shapeSmallUpTriangle
shapeSquare
shapeStar
shapeUpArrow
shapeUpTriangle
styleArea
styleBar
styleCandle
styleDashed
styleDots
styleHidden
styleHistogram
styleLeftAxisScale
styleLine
styleNoDraw
styleNoLabel
styleNoLine
styleNoRescale
styleNoTitle
styleOwnScale
stylePointAndFigure
styleStaircase
styleSwingDots
styleThick