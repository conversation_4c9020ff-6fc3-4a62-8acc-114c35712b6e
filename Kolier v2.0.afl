//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi Arafat=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>

allsym = CategoryGetSymbols( categoryAll, 0 );
symList = "";
currentPrefix = StrLeft( Name(), 5 );

for( i = 0; ( sym = StrExtract( allsym, i ) ) != ""; i++ )
{
    if( StrFind( sym, currentPrefix ) )
    {
        symList += "|" + sym;
    }
}

//=============================DISPLAY PARAMS======================================
//OptimizerSetEngine( "cmae" );
selectedSymbol  = ParamList( "Symbol", symList );
atrMultiplier   = Param( "ATR_Multiplier", 4.3, 3, 10, 0.1 );
atrPeriod       = Param( "ATR_Period", 5, 5, 10, 1 );
vLots           = Param( "Vlots", 140, 10, 200, 10 ); // 0.5
equityAtRisk    = Param( "Risk", 40000, 20000, 100000, 5000 );
//=================================================================================

if( StrFind( selectedSymbol, currentPrefix ) )
{
    SetForeign( selectedSymbol );
}

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );

tr = ATR( atrPeriod );
CalcPrice = ( H + L ) / 2;
calcTrend_proc( atrPeriod, tr, atrMultiplier, CalcPrice );
SetChartOptions( 0, chartShowDates );

Plot( buffer_line_up, "tu", ColorRGB( 68, 134, 238 ), styleThick );
Plot( buffer_line_down, " // td", ColorRGB( 205, 51, 51 ), styleThick );

Short = Buy = Sell = Cover = 0;
CV = ( V / RoundLotSize ) >= Vlots;
Buy 	= !IsEmpty( Ref( buffer_line_up, -1 ) )   && Ref( L, -1 ) >  Ref( buffer_line_up, -1 )   AND H > HHV( Ref( H, -1 ), 12 ) && TradingZone AND C > O; // AND CV ;
Sell 	= !IsEmpty( Ref( buffer_line_down, -1 ) ) && C < Ref( buffer_line_down, -1 ) && !firstBarOfTheDay && C < Ref(C, -1);
SellPrice = C;
Short = Cover = 0;
myVar += ", C1 = " + ( !IsEmpty( Ref( buffer_line_down, -1 ) ) ) + ", C2 = " + ( C < Ref( buffer_line_down, -1 )  ) + ", C3 = " + (!firstBarOfTheDay ) + ", C4 = " + ( CL );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );

bRisk = IIf( buffer_line_up, ( C - buffer_line_up ), 0 );
bLots	= Max( 0, floor( equityAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
max_lots = Min(floor(500000/RoundLotSize*C), 10);
bLots	= Min( max_lots, bLots );
myvar  	+= ", lots = " + bLots;
myvar	+= ", risk = " + round( RoundLotSize*bRisk );

SetPositionSize( bLots*RoundLotSize, spsShares );

#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );

_SECTION_BEGIN( "Volume" );
Plot( Volume / RoundLotSize, "", ParamColor( "Color", colorBlueGrey ), ParamStyle( "Style", styleHistogram | styleOwnScale | styleThick, maskHistogram ) );
_SECTION_END();