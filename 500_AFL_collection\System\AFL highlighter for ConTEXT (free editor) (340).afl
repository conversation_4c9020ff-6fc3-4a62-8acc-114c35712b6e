//------------------------------------------------------------------------------
//
//  Formula Name:    AFL highlighter for ConTEXT (free editor)
//  Author/Uploader: <PERSON> 
//  E-mail:          
//  Date/Time Added: 2004-02-14 08:19:59
//  Origin:          Based on <PERSON>'s original version in the library, updated to AB 4.5 and tweaked since.
//  Keywords:        ConTEXT editor highlight
//  Level:           semi-advanced
//  Flags:           system,exploration,indicator,commentary,function
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=340
//  Details URL:     http://www.amibroker.com/library/detail.php?id=340
//
//------------------------------------------------------------------------------
//
//  ConTEXT is a really good freeware programmer's editor, available at
//  http://www.fixedsys.com/context. This file teaches it AFL's keywords, so it
//  can color and format them like AmiBroker does. I often keep my standard AFL
//  #include file open in it while working in AB.
//
//  Since there doesn't appear to be any direct way to capture all the keywords
//  in use in the current version of AB, maintenance of this file is dependant
//  on me or someone else noticing missing items. So, any additions and
//  corrections are most welcome (:-). Please write directly to dmerrill at usa
//  dot net, in case I don't come back here right away.
//
//  Thanks to Graham Kavanagh and Jack Fagen for their feedback.
//
//  NOTE: This is the last version of this that I'll be posting, since I'm no
//  longer using ConTEXT and have no way to test it. (That's why I'm not
//  deleting the previous version here; this is untested, but it'll probably be
//  fine.) I've moved to UltraEdit, and for now anyway, it looks like I'll be
//  maintaining a highlighter for it instead. The highlighter file formats
//  aren't quite the same, but they're close enough that you can pull keyword
//  updates out of there with very little work, if you want.
//
//------------------------------------------------------------------------------

//////////////////////////////////////////////////////////////////////////////
//
// Amibroker Formula Language 
//
//////////////////////////////////////////////////////////////////////////////

//////////////////////////////////////////////////////////////////////////////
// language name

Language:               AFL


//////////////////////////////////////////////////////////////////////////////
// default file filter
// note: if more than one extension is associated, eg:
// C/C++ files (*.c,*.cpp,*.h,*.hpp)|*.c;*.cpp;*.h;*.hpp

Filter:                 AFL files (*.afl)|*.afl


//////////////////////////////////////////////////////////////////////////////
// help file which will be invokend when F1 is pressed

HelpFile:				C:\Program Files\AmiBroker\Broker.chm


//////////////////////////////////////////////////////////////////////////////
// language case sensitivity
//                      0  - no
//                      1  - yes

CaseSensitive:          0


//////////////////////////////////////////////////////////////////////////////
// comment type: LineComment - comment to the end of line
// BlockCommentBeg - block comment begin, it could be
// multiline
// BlockCommentEnd - block comment end

LineComment:            //
BlockCommentBeg:        /*
BlockCommentEnd:        */


//////////////////////////////////////////////////////////////////////////////
// identifier characters
// note: characters shouldn't be delimited, except arrays
// array of chars could be defined as from_char..to_char

IdentifierBegChars:     a..z A..Z _
IdentifierChars:        a..z A..Z _ 0..9 

//////////////////////////////////////////////////////////////////////////////
// numeric constants begin characters
// note: characters shouldn't be delimited, except arrays
// array of chars could be defined as from_char..to_char
// number always starts with 0..9 except when NumConstBeg
// defines other

NumConstBegChars:       0..9 


//////////////////////////////////////////////////////////////////////////////
// numeric constants characters
// note: characters shouldn't be delimited, except arrays
// array of chars could be defined as from_char..to_char
// number always starts with 0..9 except when NumConstBeg
// defines other

NumConstChars:          0..9 A..Z . -


//////////////////////////////////////////////////////////////////////////////
// escape character

EscapeChar:


//////////////////////////////////////////////////////////////////////////////
// keyword table
// note: delimited with spaces, lines could be wrapped
// you may divide keywords into five groups which can be
// highlighted differently


// reserved words and variable names
KeyWords1:

// reserved words
AND
False
NOT
Null
OR
True

// reserved variable names
Avg
BarCount
Buy
BuyPrice
C
Close
Column0
Column0Format
Column0Name
Column1
Column1Format
Column1Name
Column2
Column2Format
Column2Name
Column3
Column3Format
Column3Name
Column4
Column4Format
Column4Name
Column5
Column5Format
Column5Name
Column6
Column6Format
Column6Name
Column7
Column7Format
Column7Name
Column8
Column8Format
Column8Name
Column9
Column9Format
Column9Name
Cover
CoverPrice
Exclude
Filter
Graph0
Graph0BarColor
Graph0Color
Graph0High
Graph0Low
Graph0Name
Graph0Open
Graph0Style
Graph1
Graph1BarColor
Graph1Color
Graph1High
Graph1Low
Graph1Name
Graph1Open
Graph1Style
Graph2
Graph2BarColor
Graph2Color
Graph2High
Graph2Low
Graph2Name
Graph2Open
Graph2Style
Graph3
Graph3BarColor
Graph3Color
Graph3High
Graph3Low
Graph3Name
Graph3Open
Graph3Style
Graph4
Graph4BarColor
Graph4Color
Graph4High
Graph4Low
Graph4Name
Graph4Open
Graph4Style
Graph5
Graph5BarColor
Graph5Color
Graph5High
Graph5Low
Graph5Name
Graph5Open
Graph5Style
Graph6
Graph6BarColor
Graph6Color
Graph6High
Graph6Low
Graph6Name
Graph6Open
Graph6Style
Graph7
Graph7BarColor
Graph7Color
Graph7High
Graph7Low
Graph7Name
Graph7Open
Graph7Style
Graph8
Graph8BarColor
Graph8Color
Graph8High
Graph8Low
Graph8Name
Graph8Open
Graph8Style
Graph9
Graph9BarColor
Graph9Color
Graph9High
Graph9Low
Graph9Name
Graph9Open
Graph9Style
GraphXSpace
GraphZOrder
H
High
L
Low
MarginDeposit
MaxGraph
NumColumns
O
OI
Open
OpenInt
PointValue
PositionScore
PositionSize
RoundLotSize
Sell
SellPrice
Short
ShortPrice
TickSize
Title
V
Volume

// constants
colorAqua
colorBlack
colorBlue
colorBlueGrey
colorBrightGreen
colorBrown
colorCustom1
colorCustom10
colorCustom11
colorCustom12
colorCustom13
colorCustom14
colorCustom15
colorCustom16
colorCustom2
colorCustom3
colorCustom4
colorCustom5
colorCustom6
colorCustom7
colorCustom8
colorCustom9
colorDarkBlue
colorDarkGreen
colorDarkGrey
colorDarkOliveGreen
colorDarkRed
colorDarkTeal
colorDarkYellow
colorDefault
colorGold
colorGreen
colorGrey40
colorGrey50
colorIndigo
colorLavender
colorLightBlue
colorLightGrey
colorLightOrange
colorLightYellow
colorLime
colorOrange
colorPaleBlue
colorPaleGreen
colorPaleTurquoise
colorPink
colorPlum
colorRed
colorRose
colorSeaGreen
colorSkyblue
colorTan
colorTeal
colorTurquoise
colorViolet
colorWhite
colorYellow
compressHigh
compressLast
compressLow
compressOpen
compressVolume
expandFirst
expandLast
expandPoint
in15Minute
in1Minute
in5Minute
inDaily
inHourly
inMonthly
inWeekly
scoreNoRotate
shapeCircle
shapeDigit0
shapeDigit1
shapeDigit2
shapeDigit3
shapeDigit4
shapeDigit5
shapeDigit6
shapeDigit7
shapeDigit8
shapeDigit9
shapeDownArrow
shapeDownTriangle
shapeHollowCircle
shapeHollowDownArrow
shapeHollowDownTriangle
shapeHollowSmallCircle
shapeHollowSmallDownTriangle
shapeHollowSmallSquare
shapeHollowSmallUpTriangle
shapeHollowSquare
shapeHollowStar
shapeHollowUpArrow
shapeHollowUpTriangle
shapeNone
shapePositionAbove
shapeSmallCircle
shapeSmallDownTriangle
shapeSmallSquare
shapeSmallUpTriangle
shapeSquare
shapeStar
shapeUpArrow
shapeUpTriangle
styleArea
styleBar
styleCandle
styleDots
styleHistogram
styleLeftAxisScale
styleLine
styleNoDraw
styleNoLabel
styleNoLine
styleNoRescale
styleOwnScale
stylePointAndFigure
styleStaircase
styleSwingDots
styleThick


// functions
KeyWords2:

ADLine
ADX
AMA
AMA2
ATR
AccDist
AddColumn
AddTextColumn
AddToComposite
AdvIssues
AdvVolume
AlertIf
ApplyStop
BBandBot
BBandTop
BarIndex
BarsSince
BeginValue
CCI
CategoryAddSymbol
CategoryGetName
CategoryGetSymbols
CategoryRemoveSymbol
Chaikin
Correlation
CreateObject
CreateStaticObject
Cross
Cum
DEMA
Date
DateNum
DateTime
Day
DayOfWeek
DayOfYear
DecIssues
DecVolume
EMA
EnableRotationalTrading
EnableScript
EnableTextOutput
EncodeColor
EndValue
Equity
ExRem
ExRemSpan
Flip
Foreign
FullName
GapDown
GapUp
GetBaseIndex
GetCategorySymbols
GetChartID
GetDatabaseName
GetExtraData
GetScriptObject
GroupID
HHV
HHVBars
Highest
HighestBars
HighestSince
HighestSinceBars
Hold
Hour
IIf
InWatchList
IndustryID
Inside
Interval
IsEmpty
IsFavorite
IsFinite
IsIndex
IsNan
IsNull
IsTrue
LLV
LLVBars
LastValue
LinRegIntercept
LinRegSlope
LineArray
LinearReg
Lowest
LowestBars
LowestSince
LowestSinceBars
MA
MACD
MDI
MFI
MarketID
Max
Median
Min
Minute
Month
NVI
Name
Now
NumToStr
Nz
OBV
Optimize
OscP
OscV
Outside
PDI
PVI
Param
ParamColor
ParamStr
Peak
PeakBars
Percentile
Plot
PlotForeign
PlotGrid
PlotOHLC
PlotShapes
PlotVAPOverlay
Prec
Prefs
RMI
ROC
RSI
RSIa
RWI
RWIHi
RWILo
Random
Ref
RelStrength
RestorePriceArrays
SAR
Second
SectorID
SelectedValue
SetBarsRequired
SetForeign
SetFormulaName
SetOption
SetTradeDelays
Signal
StDev
Status
StdErr
StochD
StochK
StrExtract
StrFind
StrFormat
StrLeft
StrLen
StrMid
StrRight
StrToNum
Study
Sum
TEMA
TSF
TimeFrameCompress
TimeFrameExpand
TimeFrameGetPrice
TimeFrameRestore
TimeFrameSet
TimeNum
Trin
Trix
Trough
TroughBars
Ultimate
UncIssues
UncVolume
ValueWhen
Version
WMA
Wilders
WriteIf
WriteVal
Year
Zig
_N
_TRACE
abs
acos
atan
ceil
cos
exp
fclose
feof
fgets
floor
fopen
fputs
frac
int
log
log10
printf
round
sign
sin
sqrt
tan


// flow of control statements, variable scope, preprocessor
KeyWords3:
do
else
for
function
global
if
local
procedure
return
while


//////////////////////////////////////////////////////////////////////////////
// string delimiter: StringBegChar - string begin char
// StringEndChar - string end char
// MultilineStrings - enables multiline strings, as perl
// has it

StringBegChar:          "
StringEndChar:          "
MultilineStrings:       0


//////////////////////////////////////////////////////////////////////////////
// use preprocessor: 0 - no
// 1 - yes
// note: if yes, '#' and statements after it will be
// highlighted with Preprocessor defined colors

UsePreprocessor:        1


//////////////////////////////////////////////////////////////////////////////
// highlight line: 0 - no
// 1 - yes
// note: if yes, current line will be highlighted

CurrLineHighlighted:    0


//////////////////////////////////////////////////////////////////////////////
// colors
// note:                first value is foreground, second is background color
//                        and third (optional) represents font attribute:
//                        B - bold
//                        I - italic
//                        U - underline
//                        S - strike out
//                        attributes can be combined: eg. B or BI
//                      as value, it could be used any standard windows color:
//                        clBlack, clMaroon, clGreen, clOlive, clNavy,
//                        clPurple, clTeal, clGray, clSilver, clRed, clLime,
//                        clYellow, clBlue, clFuchsia, clAqua, clLtGray,
//                        clDkGray, clWhite, clScrollBar, clBackground,
//                        clActiveCaption, clInactiveCaption, clMenu, clWindow,
//                        clWindowFrame, clMenuText, clWindowText, clCaptionText,
//                        clActiveBorder, clInactiveBorder, clAppWorkSpace,
//                        clHighlight, clHighlightText, clBtnFace, clBtnShadow,
//                        clGrayText, clBtnText, clInactiveCaptionText,
//                        clBtnHighlight, cl3DDkShadow, cl3DLight, clInfoText,
//                        clInfoBk
//                      as value, it could be used hex numeric constant too:
//                        $BBGGRR - BB: blue, GG: green, RR: red, eg: $FF6A00

SpaceCol:               clWindowText clWindow
Keyword1Col:            clBlack clWindow B
Keyword2Col:            clBlue clWindow
Keyword3Col:            $000080 clWindow B
Keyword4Col:            clBlue clWindow
Keyword5Col:            clBlue clWindow
IdentifierCol:          clWindowText clWindow
CommentCol:             clGreen clWindow
NumberCol:              clFuchsia clWindow
StringCol:              clFuchsia clWindow
SymbolCol:              clBlack clWindow
PreprocessorCol:        $000080 clWindow B
SelectionCol:           clWhite clNavy
CurrentLineCol:         clBlack clYellow
