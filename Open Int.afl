// Downloaded From https://www.WiseStockTrader.com
_SECTION_BEGIN( "oi" );
//Plot( OI, _DEFAULT_NAME(), ParamColor("Color", colorBlueGrey ), ParamStyle( "Style", styleHistogram | styleOwnScale | styleThick, maskHistogram ), 2 );
_SECTION_END();
//written by <PERSON>
//Date: 15-07-02
//<EMAIL>
#include <Common.afl>

pds = 5;
OI1 = Ref( OI, -1 );
V1 = OI / MA( OI1, 5 );
V2 = OI / MA( OI1, 20 );
V3 = OI / MA( OI1, 50 );
x = OI;
MAI = MA( OI1, 15 );
MAI3 = MA( OI, 3 );
period = 10;
MAI3 	= Min( LinearReg( OI , period ), LinearReg( OI , ceil( period / 2 ) ) );
MAI3 = mybox( LWMA( OI1, 10 ) );
MAI6 = MA( OI1, 6 );
HI = HHV( IIf( OI1 == OI, 0, OI1 ), 5 );

y = C / MA( Ref( C, -1 ), pds );
x1 = x > Ref( x, -1 );
y1 = y > Ref( y, -1 );
myOI = ( OI - ValueWhen( firstBarOfTheDay, OI ) ) / ValueWhen( firstBarOfTheDay, OI );
m_fill = Optimize( "fill", 5, 1, 50, 1 ) / 100;
m_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
m_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
m_AH = LLV( m_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
m_AL = HHV( m_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
m_Above = MAI3 > m_AH;
m_Below = MAI3 < m_AL;

barcolor = IIF( MAI3 > MAI6 AND OI > HI, colorGreen, colorBlack );
Plot( OI, " OI", barcolor, 2 + 4, Null, Null, 0, -2 );
// Plot( HI, "HV", colorWhite, styleDashed );
Plot( MAI3, "MAV", IIf( m_Above, colorLime, IIf( m_Below, colorBrown, colorBlack) ), styleLine | styleThick );
// Plot(m_PL, "m_AH", colorBlack, styleDashed);
// Plot( 100, " AVG(10)", 1, 1 );

unsure = V1 < 1 AND V2 < 1 AND V3 < 1;
sure = V1 > 1 AND V2 > 1 AND V3 > 1;
