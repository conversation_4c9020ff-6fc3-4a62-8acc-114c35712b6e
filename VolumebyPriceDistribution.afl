/*
Author: <PERSON>
based on PriceVolDistribution demo written by <PERSON><PERSON>

Volume-by-Price is an indicator that shows the amount of volume for a particular price range,
The Volume-by-Price bars are horizontal and shown on the left side of the chart to correspond
with these price ranges. Bar colors indicate bear/bull volume

Use F12 and SHIFT+F12 to define range
*/

// Set chart options
SetChartOptions( 0, chartShowArrows | chartShowDates );

// Define variables
bi = BarIndex();
fvb = BeginValue( bi );
lvb = EndValue( bi );

// Update fvb and lvb values for visible bars
if( fvb == 0 && lvb == LastValue( bi ) )
{
    fvb = FirstVisibleValue( bi );
    lvb = LastVisibleValue( bi );
}

fvb = Max( 0, fvb );
lvb = Max( 0, lvb );

// Define input parameters
bins = Param( "Bins", 100, 3, 100, 1 );
pRecHeight = Param( "Rectangle Height", 0.90, 0.10, 1, 0.05 );

// Calculate bull/bear bar zones
BullBearZone = ( High - Low ) / 3;
bullbar = C > ( High - BullBearZone );
bearbar = C < ( Low + BullBearZone );

myVar  = ", BullBearZone: " + BullBearZone + ", (High - BullBearZone):" + ( High - BullBearZone ) + ", bullbar: " + bullbar;
myVar += ", ( Low + BullBearZone ): " + ( Low + BullBearZone ) + ", bearbar: " + bearbar;

// Calculate PriceVolDistribution for bull, bear, and total volume
mx = PriceVolDistribution( H, L, V, bins, True, fvb, lvb );
mx1 = PriceVolDistribution( H, L, IIf( bullbar, V, 0 ), bins, True, fvb, lvb );
mx2 = PriceVolDistribution( H, L, IIf( bearbar, V, 0 ), bins, True, fvb, lvb );

// Get size of price volume distributions
bins = MxGetSize( mx, 0 );
bins1 = MxGetSize( mx1, 0 );
bins2 = MxGetSize( mx2, 0 );

// Set graphics overlay mode and coordinates mode
GfxSetOverlayMode( 1 );
GfxSetCoordsMode( 1 );

// Check if all price volume distributions have the same size
if( bins > 1 && bins == bins1 && bins == bins2 )
{

    MaxVolume = mx[ 0 ][ 1 ];

    // Find max volume
    for( i = 1; i < bins; i++ )
    {
        if( mx[ i ][ 1 ] > MaxVolume )
            MaxVolume = mx[ i ][ 1 ];
    }

    // Calculate rectangle height
    RecHeight = ( mx[ 1 ][ 0 ] - mx[ 0 ][ 0 ] ) / 2 * pRecHeight;

    for( i = 0; i < bins; i++ )
    {
        price = mx1[ i ][ 0 ]; // price level

        absVolume = mx1[ i ][ 1 ];
        VolAcum = absVolume;
        relvolume = absVolume / MaxVolume;
        relbar = relvolume * ( lvb - fvb + 1 );

        // upper left corner of the rectangle.
        x1 = fvb;
        y1 = price + RecHeight;
        // lower right corner of the rectangle.
        x2 = fvb + relbar;
        y2 = price - RecHeight;

        GfxFillSolidRect( x1, y1, x2, y2, colorGreen );

        absVolume = mx2[ i ][ 1 ];
        VolAcum += absVolume;
        relvolume = absVolume / MaxVolume;
        relbar2 = relvolume * ( lvb - fvb + 1 );

        x1 = fvb;
        x2 = x1 + relbar2;
        GfxFillSolidRect( x1, y1, x2, y2, colorPink );

        absVolume = mx[ i ][ 1 ];
        relvolume = ( absVolume - VolAcum ) / MaxVolume;
        relbar3 = relvolume * ( lvb - fvb + 1 );

        x1 = fvb;
        x2 = x1 + relbar3;
        // GfxFillSolidRect( x1, y1, x2, y2, colorLightBlue );
    }
}

SetBarFillColor(IIf(bullbar, colorGreen, IIf(bearbar, colorRed, colorBlue)));
Plot( C, "Price", colorDefault, styleCandle );

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "Open %0.2f, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 )
                       ) );
