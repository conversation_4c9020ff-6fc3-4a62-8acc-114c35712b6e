// Expiry Day
_SECTION_BEGIN( "Total OI" );
#include <Common.afl>
#include <Options.afl>
// #include <AlgoFoxAuto/AlgoFoxButton.afl>

SetChartBkColor( colorWhite ); // color of outer border
SetOption( "DisableRuinStop", True );

tn = TimeNum();
TradingZone = ( tn >= 100400 AND tn <= 151000 );

equityAtRisk = 3000;

V1 = Ref( V, -1 );
MAV = MA( V1, 15 );
HV = HHV( Ref( V, -1 ), 15 );
C1 = Ref( C, -1 );
H1 = Ref( H, -1 );
L1 = Ref( L, -1 );
O1 = Ref( O, -1 );

// DayVolMul = round( TimeFrameGetPrice( "V", inDaily, 0, expandLast ) / HV );
DayVolMul = round( HighestSince( firstBarOfTheDay, HV ) / HV );

myVar += ", Day_V " + DayVolMul;
Liquid = V / RLS > 10000;

pp = Optimize( "pp", 10, 20, 30, 1 );
vol_up = V > MAV AND DayVolMul < pp AND Liquid;
vol_up_1 = Ref( vol_up, -1 );

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = OptimizeNot( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn = Flip( oi_Below, oi_Above );
oi_up = Flip( oi_Above, oi_Below );

HHV10 = HHV( H1, 18 );
LLV10 = LLV( L1, 22 );

ACDUp 	= myboxC( fcl + Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );
ACDDown	= myboxF( fch - Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );

CP_IH	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( H - C ) / ( .001 + H - L ) > 0.6 ) AND( ( H - O ) / ( .001 + H - L ) > 0.6 ) );
CP_H	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( C - L ) / ( .001 + H - L ) > 0.6 ) AND( ( O - L ) / ( .001 + H - L ) > 0.6 ) );
CP_EH	= ( ( C1 > O1 ) AND( O > C ) AND( O <= C1 ) AND( O1 <= C ) AND( ( O - C ) < ( C1 - O1 ) ) );
CP_UH 	= ( ( O1 > C1 ) AND( C > O ) AND( C <= O1 ) AND( C1 <= O ) AND( ( C - O ) < ( O1 - C1 ) ) );
CP_LC	= ( abs( C - O ) / ( .001 + H - L ) > .6 );
CP_LC1	= Ref( CP_LC, -1 );

price_up = H > HHV10;
price_dn = L < LLV10;
SPD12	= Sum( price_dn, 8 );
SPU12	= Sum( price_up, 8 );

long_buildup = price_up AND vol_up AND oi_dn;
short_buildup = price_dn AND vol_up AND oi_up;

BuyExit = myboxF( LLV10 - boxSize );
BE1 = Ref( BuyExit, -1 );
// ShortExit = Min( C + floor( equityAtRisk / RLS ), myboxC( HHV10 + 2.5 * boxSize ));
ShortExit = myboxC( HHV10 + boxSize );
SE1 = Ref( ShortExit, -1 );
ROC_price = ROC( C, 3 );
ROC1 = ROC( C, 1 );

ShortCondition	= ( ( oi_up AND L < LLV( L1, 10 ) ) OR( oi_dn AND SPU12 > 0 AND( ( CP_IH AND SPD12 > 0 ) OR( SPD12 == 0 AND H < H1 + boxSize ) ) AND C > L1) ) AND C < C1 AND L > 5 AND( vol_up OR vol_up_1 ) AND TradingZone;
ShortCondition	= ( ( oi_dn AND SPU12 > 0 AND( ( CP_IH AND SPD12 > 0 ) OR( SPD12 == 0 AND H < H1 + boxSize ) ) AND C > L1) ) AND C < C1 AND L > 5 AND( vol_up OR vol_up_1 ) AND TradingZone;
BuyCondition	= ( ( oi_dn AND H > HHV( H1, 10 ) ) OR( oi_up AND SPD12 > 0 AND SPU12 == 0 ) ) AND C > C1 AND L > 5 AND( vol_up OR vol_up_1 ) AND TradingZone AND NOT CP_IH;

myVar += ", ShortCondition = " + ( ( oi_up AND L < LLV( L1, 10 ) ) OR( SPU12 > 0 AND( ( CP_IH AND SPD12 > 0 ) OR( SPD12 == 0 AND H < H1 + boxSize ) ) AND C > L1) );
myVar += ", SPU12 " + SPU12 + ", SPD12 = " + SPD12;

BuyP = ShortP = 0;
InShort = InBuy = 0;
ShortI = BuyI = BuyL = ShortL = 0;
Short = Cover = 0 ;
Buy = Sell = 0;

BuyFlag = ShortFlag = False;
BF = SF = 0;
SPT = LPT = 0;
HatBC = LatSC = LatBC = 0;
SellPrice = CoverPrice = C;
BTC = STC = 0;
SOI = BOI = 0;

for( i = 10; i < BarCount; i++ )
{
    if( InShort )
    {
        if( i - ShortI > 5 AND C[i] > ShortP AND ShortExit[i] - boxSize[i] > O[i] )
        {
            ShortExit[i] = ShortExit[i] - boxSize[i];
        }

        ShortExit[i] = Min( ShortExit[i], ShortExit[i - 1] );
    }

    if( InShort AND OI[i] > SOI )
    {
        STC = True;
    }
	SOI = Min(SOI, OI[i]);

    if( H[i] > ShortExit[i] AND InShort )
    {
        Cover[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
        CoverPrice[i] = ShortExit[i];
    }

    if( ( !TradingZone[i]
            OR C[i] < 5
            OR( vol_up[i] AND ROC1[i] > 10 )
            OR( i > ShortI + 6 AND NOT STC )
            OR( i > BuyI + 6 AND ROC1[i - 1] < -5 AND ROC1[i] > 2.5 AND ( vol_up[i] OR vol_up[i - 1]) )
            OR( L1[i] < ShortP * 0.5 AND C[i] > C1[i] AND i > ShortI + 5 )
        ) AND InShort AND NOT CP_IH[i] )
    {
        Cover[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
    }

    if( InBuy AND O[i] > Max( BuyExit[i], BuyExit[i - 1] ) )
    {

        if( C[i] > LPT AND O[i] > Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] ) )
        {
            BuyExit[i] = Max( C[i] - 2 * floor( equityAtRisk / RLS ), BuyExit[i] );
        }

        BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
    }

    if( InBuy AND OI[i] < BOI )
    {
        BTC = True;
    }
    BOI = Max(BOI, OI[i]);

    if( L[i] < BuyExit[i] AND InBuy )
    {
        Sell[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
        BuyI = i;
        SellPrice[i] = BuyExit[i];
    }

    if( ( !TradingZone[i]
            OR( V[i - 1] > HV[i - 1] AND SPU12[i] > 1 AND H[i] < H1[i] AND BlackBody[i] )
            OR( vol_up[i] AND ROC_price[i] < -6 )
            OR( i > BuyI + 12 AND NOT BTC )
            OR( i > BuyI + 6 AND ROC1[i - 1] > 5 AND ROC1[i] < -2.5 AND ( vol_up[i] OR vol_up[i - 1]) )
            OR( vol_up[i] AND C[i] < C1[i] AND i > BuyI + 6 AND LongWick[i] )
        ) AND InBuy )
    {
        Sell[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
        BuyI = i;
    }

    if( ShortFlag )
        LatSC[i] = Max( LatSC[i - 1], L[i] );

    if( ShortCondition[i] AND NOT InShort AND NOT InBuy )
    {
        if( NOT ShortFlag )
            LatSC[i] = L[i];

        ShortFlag = True;
        ShortI = i;
        BuyFlag = False;

    }
    SF[i] = ShortFlag;

    if( ShortFlag AND TradingZone[i] AND L[i] > 5 AND NOT BuyFlag AND C[i] < LatSC[i] AND ROC1[i] > -20 AND ROC_price[i] > -12 AND BlackBody[i] )
    {
        Short[i] = 1;
        ShortP = C[i];
        ShortI = i;
        ShortExit[i] = Min( C[i] + floor( equityAtRisk / RLS ), ShortExit[i] );
        InBuy = 0;
        InShort = 1;
        BuyFlag = False;
        ShortFlag = False;
        STC = False;
        SOI = OI[i];
    }

    if( BuyFlag )
        HatBC[i] = Min( HatBC[i - 1], H[i] );

    if( BuyCondition[i] AND NOT InBuy AND NOT InShort )
    {
        if( NOT BuyFlag )
        {
            HatBC[i] = H[i];
            LatBC = floor( ( L[i] - boxSize[i] ) / boxSize[i] ) * boxSize[i];
        }

        ShortFlag = False;
        BuyFlag = True;
        BuyI = i;
        BuyL = Min( L[i - 1], L[i] );
    }

    if( NOT TradingZone[i] OR i > BuyI + 12 OR L[i] < LatBC )
    {
        BuyFlag = False;
    }

    if( BuyFlag AND L[i] > 5 AND NOT CP_IH[i] AND C[i] > HatBC[i] AND WhiteBody[i] AND ROC1[i] < 20 )
    {
        Buy[i] = 1;
        BuyP = C[i];
        BuyI = i;
        BuyExit[i] = Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] );
        InBuy = 1;
        InShort = 0;
        BuyFlag = False;
        ShortFlag = False;
        LPT = C[i] + 0.7 * floor( equityAtRisk / RLS );
        BTC = False;
        BOI = OI[i];
    }

    BF[i] = BuyFlag;

    if( NOT TradingZone[i]	OR i > ShortI + 5 )
    {
        ShortFlag = False;
    }
}

myVar += ", HatBC " + HatBC + ", LatSC = " + LatSC;
myVar += ", BF = " + BF + ", BC = " + BuyCondition;
myVar += ", SF = " + SF + ", SC = " + ShortCondition;
myVar += ", ROC_price = " + ROC_price;

Sell = Sell OR NOT TradingZone OR Short;
Cover = Cover OR NOT TradingZone OR C < 5 OR Buy;
Buy = Buy AND isITM AND ITMRange == 0 AND daysToExpiry < 7;
Short = Short AND isITM AND ITMRange == 0 AND daysToExpiry < 7;
// Buy = Buy AND isITM AND daysToExpiry < 7;
// Short = Short AND isITM AND daysToExpiry < 7;
InBuy = InShort = 0;
BuyPrice = ShortPrice = C;

Plot( C, "", ParamColor( "Color", colorBlack ), styleCandle );
Plot( ShortExit, "SE", IIf( SF, colorRed, colorGrey40 ), styleDashed );
Plot( BuyExit, "BE", IIf( BF, colorLime, colorGrey40 ), styleDashed );
PlotShapes( BF*shapeSmallCircle, colorGreen, 0, L - 1 );
PlotShapes( SF*shapeSmallCircle, colorRed, 0, H + 10 );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

bRisk = BuyPrice - BuyExit;
bLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) ) ) );

sRisk = ShortExit - ShortPrice;
sLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) ) ) );
myvar 	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", BuyExit, ShortExit );
myvar 	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
#include <Alert.afl>

/*
_N( Title = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{DATE}}, "
       + "O %0.2f, "
       + "H %0.2f, "
       + "L %0.2f, "
       + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
       + "Vol %0.1f, "
       + "OI %0.1f, "
       + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
       + EncodeColor( colorDarkGrey ) + myVar
       , Open, High, Low, Close, ROC( C, 1 ), V, OI
      ) );
*/
_N( Title = StrFormat( EncodeColor( colorDarkGrey )
                       + "H %0.2f, "
                       + "L %0.2f, "
                       + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                       + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                       + EncodeColor( colorDarkGrey ) + myVar
                       , H, L, C, ROC( C, 1 )
                     ) );

_SECTION_END();
