//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi Arafat=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>

SetOption( "DisableRuinStop", True );

//=============================DISPLAY PARAMS======================================
atrMultiplier   = OptimizeNot( "ATR_Multiplier", 4, 2, 5, 0.1 ); // 4.3
atrPeriod       = Param( "ATR_Period", 9, 5, 20, 1 );
vLots           = Param( "Vlots", 140, 10, 200, 10 ); // 0.5
equityAtRisk    = Param( "Risk", 10000, 20000, 100000, 5000 );
//=================================================================================

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;
tt1 = OptimizeNot( "T1", 4, 0, 50, 2 );
T2 = TA[tt1];
StartBar = tn == T2;
TradingZone = ( tn > T2 AND tn <= 150000 );
boxSize = Min( Max( floor( MA( C, 15 ) / 20 ), 1 ), 5 );

//=================================================================================
// Call
//=================================================================================
tr = ValueWhen(StartBar, mybox( Max( 10, ATR( atrPeriod ) ) ));
CalcPrice = ( H + L ) / 2;
calcTrend_proc( atrPeriod, tr, atrMultiplier, CalcPrice );

vlots = V / RLS;
vlotsMA = MA( Ref( vlots, -1 ), OptimizeNot( "vp", 12, 1, 50, 1 ) );
vCheck = vlots / vlotsMA;

buffer_line = Max( buffer_line_up, buffer_line_down );
buffer_line_1 = Ref( buffer_line, -1 );
trend = 0;
for( i = 10; i < BarCount; i++ )
{

    if( buffer_line[i] > buffer_line[i - 1] )
    {
        trend[i] = 1;
    }
    else
        if( buffer_line[i] < buffer_line[i - 1] )
        {
            trend[i] = -1;
        }
        else
            if( !TradingZone[i] )
            {
                trend[i] = 0;
            }
            else
            {
                trend[i] = trend[i - 1];
            }
}

HHST = HighestSince( buffer_line < buffer_line_1, Ref( H, -1 ) );
LLST = LowestSince( buffer_line > buffer_line_1, Ref( L, -1 ) );

pp = Optimize("pp", 4, 3, 10, 0.1);
UPT = Max(1, buffer_line + 5 * tr);
SPT = Max(1, buffer_line - 5 * tr);

BuyExit = ( buffer_line - 2 * tr );
ShortExit = ( buffer_line + 2.1 * tr );
Buy = trend == 1 AND C > buffer_line AND C < ShortExit AND LLST > BuyExit AND WhiteBody AND H > Ref(H, -1);
Buy = Buy AND TradingZone AND vlotsMA > 100 AND vCheck > 0.3 AND C > 20 AND ROC( C, 1 ) < 50;
// Buy = Buy AND Sum( Buy, BarsSince( newDay ) + 1 ) <= 1;
Sell = tn >= 151400 OR trend == -1 OR C < BuyExit;
BuyPrice = C;
SellPrice = C;

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
// PT
InTrade = Flip( Buy, Sell );
DoScaleOut = ExRem( InTrade AND !Buy AND H > UPT, Sell );
Buy = Buy + sigScaleOut * DoScaleOut;

Short = trend == -1 AND C < buffer_line AND (BlackBody OR (WhiteBody AND LongWick AND H > buffer_line)) AND C > BuyExit AND HHST < ShortExit AND REf(WhiteBody, -1) ;
Short = Short AND TradingZone AND vlotsMA > 100 AND vCheck > 0.5 AND vCheck  < 3 AND ROC( C, 1 ) > -50;
// Short = Short AND Sum( Short, BarsSince( newDay ) + 1 ) <= 2;
Cover = tn >= 151400 OR trend == 1 OR H > ShortExit OR( tn >= 150000 AND C > buffer_line );
ShortPrice = C;
CoverPrice = IIf( H > ShortExit, ShortExit, C );

Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | GetPriceStyle() );
Plot( buffer_line, "", IIf( trend == 1, ColorRGB( 68, 134, 238 ), IIf(trend == -1, ColorRGB( 205, 51, 51 ), colorGrey40) ), styleThick );
PlotOHLC( buffer_line, BuyExit, ShortExit, buffer_line, "", colorGrey40, styleCloud );
Plot( UPT, "UPT", colorYellow, styleDashed );
Plot( SPT, "SPT", colorYellow, styleDashed );

vlots = V / RLS;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -10 );

bRisk	= BuyPrice - BuyExit;
bLots	= Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) );

sRisk	= ShortExit - ShortPrice;
sLots	= Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) );

max_lots = 20;
bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );
SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
SetPositionSize( 50, spsPercentOfPosition * ( Buy == sigScaleOut OR Short == sigScaleOut) ); // scale out 50% of position
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );


#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), vCheck
                       ) );