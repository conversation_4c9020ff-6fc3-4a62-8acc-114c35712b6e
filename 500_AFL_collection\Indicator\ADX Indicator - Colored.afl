//------------------------------------------------------------------------------
//
//  Formula Name:    ADX Indicator - Colored
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2005-01-25 00:12:58
//  Origin:          
//  Keywords:        ADX  Indicator  DMI  Signal  Timing
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=420
//  Details URL:     http://www.amibroker.com/library/detail.php?id=420
//
//------------------------------------------------------------------------------
//
//  Easily see rise or fall of the ADX with this colored ADX Line. The period
//  default is 10 but easily adjusted using the parameter feature.
//
//------------------------------------------------------------------------------

//Trend Following Systems DO NOT work when ADX is Below 20 - Tech Analysis A-Z; page 120

P1 = Param("Period",10,0,100,1);

MyPDI= PDI(P1);//Positive Directional Indicator

MyMDI= MDI(P1);//Negative Directional Indicator (Minus)

MyADX= ADX(P1);//Average Directional Movement Index

//Green ADX Line=Rising; Red ADX Line=Falling

col = IIf( MyADX > Ref( MyADX, -1 ), colorGreen, colorRed );

Plot( MyPDI,"+DI",colorWhite, styleLine);

Plot( MyMDI,"-DI",colorBlue, styleLine);

Plot( MyADX,"ADX",col, styleLine);

Title=Name()+ " " + Date() + " Price: " + C + EncodeColor(colorIndigo) +"  ADX" + WriteVal( MyADX )+ EncodeColor(colorWhite) + "  +DMI" + WriteVal( MyPDI )+ EncodeColor(colorBlue) + "  -DMI" + WriteVal( MyMDI );