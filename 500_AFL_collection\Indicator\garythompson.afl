//------------------------------------------------------------------------------
//
//  Formula Name:    garythompson
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2005-04-07 06:29:05
//  Origin:          
//  Keywords:        
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=454
//  Details URL:     http://www.amibroker.com/library/detail.php?id=454
//
//------------------------------------------------------------------------------
//
//  <PERSON> Awesome Oscillator ( a momentum Oscillatr) based on the
//  profitunity trading approach. See "Trading Chaos" Bill williams for full
//  description
//
//------------------------------------------------------------------------------

/* Awesome Oscillator code for Amibroker
	Written By Gary Thompson 
	07th April 2005  */


SlowMA=MA( Avg ,34);
FastMA=MA( Avg,5);

barcolor= IIf((SlowMA - FastMA) <= Ref(SlowMA - FastMA, -1),colorGreen,colorRed); 
               
Graph0=FastMA-SlowMA;
Graph0Style=2+5;
Graph0BarColor=barcolor;

/* the following is to put the 5 period signal line on the oscillator if you choose*/

/*Graph1=Wilders(SlowMA-FastMA,5);
Graph1Style=4+1;*/
