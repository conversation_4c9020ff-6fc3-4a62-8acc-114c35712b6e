#include <Common.afl>

SetChartBkColor( colorWhite );

TimeFrameSet( inDaily );
V_D = V;
MV_D = MA( Ref(V, -1), 5 );
TimeFrameRestore();
V_D = TimeFrameExpand( V_D, inDaily );
MV_D = TimeFrameExpand( MV_D, inDaily );
Y_lots = V_D / RoundLotSize;

lots = V / RoundLotSize;
lots_50 = round( WMA( Ref( lots, -1 ), 50 ) );
multi = V / V_D;
multi_50 = 2 * WMA( Ref( multi, -1 ), 50 );

_SECTION_BEGIN( "Volume" );
color = IIf( lots / Y_lots > 0.1 , colorRed, colorWhite );
Plot( IIf( multi / multi_50 > 1.5, multi / multi_50, IIF( lots / Y_lots > 0.1, lots / Y_lots, Null ) ), "", IIf( multi / multi_50 > 1.5, colorBlue, color ), styleHistogram );
//Plot( multi_50 AND !firstBarOfTheDay, "", color, styleHistogram );
myVar = "V = " + lots + ", Yest Vol = " +  Y_lots + ", lots_50 = " + lots_50 + ", multi/multi_50 = " + round( ( multi / multi_50 ) * 100 ) / 100 + ", V_D/MV_D = " + round( ( V_D / MV_D ) * 100 ) / 100;

_N( Title   = StrFormat( EncodeColor( colorBlack ) + myVar ) );

_SECTION_END();
