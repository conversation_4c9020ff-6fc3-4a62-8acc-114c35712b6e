// Enhanced Autocorrelation and Stochastic Mean Reversion Trading
// Original code by <PERSON><PERSON><PERSON> R, www.marketcalls.in
// Enhanced with RSI, Bollinger Bands, IBS, and dynamic stop losses

_SECTION_BEGIN("Enhanced Stochastic Mean Reversion Trading");

// Chart Styling
OptimizerSetEngine( "cmae" );

SetBarFillColor(IIf(C > O, ParamColor("Candle UP Color", colorGreen), IIf(C <= O, ParamColor("Candle Down Color", colorRed), colorLightGrey)));
Plot(C, "Price", IIf(C > O, ParamColor("Wick UP Color", colorDarkGreen), IIf(C <= O, ParamColor("Wick Down Color", colorDarkRed), colorLightGrey)), 64, 0, 0, 0, 0);

// Parameters
AC = Optimize("Autocorrelation Period", 14, 1, 20, 1);
StochKPeriod = Optimize("Stochastic %K Period", 2, 1, 20, 1);
StochDPeriod = StochKPeriod + Optimize("Stochastic %D Period", 3, 1, 20, 1);
RSIPeriod = Param("RSI Period", 14, 5, 30, 1);
RSIOverbought = Param("RSI Overbought", 70, 50, 90, 1);
RSIOversold = Param("RSI Oversold", 30, 10, 50, 1);
BBPeriods = Param("Bollinger Periods", 20, 10	, 50, 1);
BBStdDev = Param("Bollinger StdDev", 2, 1, 3, 0.1);
IBSPeriod = Param("IBS MA Period", 3, 1, 10, 1);
IBSBuyThreshold = Param("IBS Buy Threshold", 0.3, 0.1, 0.5, 0.01);
IBSSellThreshold = Param("IBS Sell Threshold", 0.75, 0.5, 0.9, 0.01);
StopLookback = Param("Stop Lookback Period", 5, 3, 10, 1);

// Indicator Calculations
Dayreturn = ROC(C, 1);
AutoCor = Correlation(Dayreturn, Ref(Dayreturn, -1), AC);
StochKVal = StochK(StochKPeriod, StochDPeriod);
StochDVal = StochD(StochKPeriod, StochDPeriod, StochDPeriod);
RSIValue = RSI(RSIPeriod);
BBTop = BBandTop(C, BBPeriods, BBStdDev);
BBBottom = BBandBot(C, BBPeriods, BBStdDev);
BBMid = MA(C, BBPeriods);
IBS = (C - L) / Max((H - L), 1);
maIBS = MA(IBS, IBSPeriod);

// Trading Conditions
crossBuy = Cross(StochKVal, StochDVal);
crossSell = Cross(StochDVal, StochKVal);
Buy = AutoCor < 0 AND crossBuy AND RSIValue < RSIOversold AND C > O;
Sell = AutoCor > 0 OR crossSell AND RSIValue > RSIOverbought;

// Remove Excessive Signals
Buy = ExRem(Buy, Sell);
Sell = ExRem(Sell, Buy);

// Dynamic Stop Losses
BuyStop = ValueWhen(Buy, LLV(C, -1 * StopLookback));

eqAtRisk = 100000;
bRisk = round(IIf( BuyStop < C, C - BuyStop, C - L ));
bLots	= Min(5, Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) ));
printf("bLots = %g", bLots);

ApplyStop(stopTypeLoss, stopModePoint, bRisk, 1);

SetPositionSize( bLots*RoundLotSize, spsShares );

// Prices
BuyPrice = ValueWhen(Buy, C);
SellPrice = ValueWhen(Sell, C);

// Plot Indicators
Plot(BBTop, "BB Top", colorBlue, styleLine);
Plot(BBBottom, "BB Bottom", colorBlue, styleLine);
Plot(BBMid, "BB Mid", colorRed, styleLine);
Plot(RSIValue, "RSI", colorYellow, styleOwnScale | styleLine, 0, 100);
Plot(30, "RSI Oversold", colorGreen, styleDashed | styleOwnScale, 0, 100);
Plot(70, "RSI Overbought", colorRed, styleDashed | styleOwnScale, 0, 100);
Plot(maIBS, "maIBS", colorOrange, styleOwnScale | styleLine, 0, 1);
Plot(IBSBuyThreshold, "IBS Buy", colorGreen, styleDashed | styleOwnScale, 0, 1);
Plot(IBSSellThreshold, "IBS Sell", colorRed, styleDashed | styleOwnScale, 0, 1);

// Plot Signals
PlotShapes(IIf(Buy, shapeSquare, shapeNone), colorGreen, 0, L, Offset = -40);
PlotShapes(IIf(Buy, shapeSquare, shapeNone), colorLime, 0, L, Offset = -50);
PlotShapes(IIf(Buy, shapeUpArrow, shapeNone), colorWhite, 0, L, Offset = -45);
PlotShapes(IIf(Sell, shapeSquare, shapeNone), colorRed, 0, H, Offset = 40);
PlotShapes(IIf(Sell, shapeSquare, shapeNone), colorOrange, 0, H, Offset = 50);
PlotShapes(IIf(Sell, shapeDownArrow, shapeNone), colorWhite, 0, H, Offset = -45);

// Exploration for Scanning
AddColumn(C, "Close", 1.2);
AddColumn(RSIValue, "RSI", 1.2);
AddColumn(maIBS, "maIBS", 1.3);
AddColumn(IIf(Buy, 66, IIf(Sell, 83, 32)), "Signal", formatChar);

_SECTION_END();