#include <Kolier.afl>
#include <Common.afl>
#include<KolierSignals.afl>


_SECTION_BEGIN( "Price" );
SetChartOptions( 0, chartShowArrows | chartShowDates );
Plot( C, "Close", ParamColor( "Color", colorDefault ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );
_SECTION_END();

myVar = "";

//ORB

_SECTION_BEGIN( "ORB" );

newday = Day() != Ref( Day(), -1 ) ; //Identifies the beginning of new day

VMA = V / MA( Ref( V, -1 ), 16 );
vCheck = VMA > 3 AND C < O;
vCheckD = ( Cum( vCheck ) - ValueWhen( lastBarOfTheDay, Cum( vCheck ) ) );

SLprc = Param( "SLprc", 0.42, 0.05, 1, 0.01 );
IBL = myBoxF( Min( ValueWhen( vCheck , L ), LLV( Ref( C, -1 ), 5 ) ) );

// Plot( IBL, "IBL", colorGreen );

//Timing functions

//Entry time should be greater than 10:15 and 2:30 PM
Starttime = TimeNum() > 101500 ;
endtime = TimeNum() < 150000 ;
EODsquareoff = TimeNum() > 151500 ;

//Define the strategy rules
Short = 0;
ShortPrice = 0;
Cover = 0;
CoverPrice = 0;
Buyflag = 0;
Buy = 0;
Sell = 0;
PT = 0;
singleT = True;

myVar += ", boxSize = " + boxSize;


BuySL			= Max( IBL, myboxF( LLV( Ref( L, -1 ), 10 ) ) - round( boxSize ) );
BuyCondition	= BuyCondition AND starttime AND endtime AND vCheckD > 1;

SellCondition 	= !IsEmpty( Ref( buffer_line_down, -1 ) ) AND C < Ref( buffer_line_down, -1 );
SellCondition	= SellCondition OR ( ( Ref( ROC( C, 3 ), -1 ) > 11 OR Ref( ROC( C, 2 ), -1 ) > 11 OR Ref( ROC( C, 1 ), -1 ) > 11 ) AND ROC( C, 1 ) < -4 AND NOT Ref( Buy, -1 ) )
                  OR( ( HHV( Ref( H, -1 ), BarsSince( Buy ) + 1 ) - C ) / C > 0.2 ) ;

myVar += ", ( " + Ref( L, -2 ) + ", " + IBL + ")";

for( i = 1; i < BarCount; i++ )
{
    if( NOT newday[i] AND Buyflag )
    {
        BuySL[i] = Max( BuySL[i - 1], BuySL[i] );
        PT[i]  = PT[i - 1];
    }

    if( newday[i] )
    {
        singleT = False;
    }

    if( ( EODsquareoff[i] OR L[i] < BuySL[i] ) AND Buyflag )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( EODsquareoff[i], C[i], BuySL[i] );
    }

    if( BuyCondition[i] AND NOT Buyflag )
    {
        Buy[i] = 1;
        Buyflag = 1;
        BuyPrice[i] = H[i - 1];
        singleT = True;
        PT[i] = BuyPrice[i] * 2;
    }

    if( H[i] > PT[i] AND Buyflag )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = PT[i];
    }

}

myVar += ", PT = " + PT;
Plot( Zig(L, 15), "BuySL", colorWhite );

bRisk = BuyPrice - BuySL;

max_lots = 20;
bLots	= Min( max_lots, floor( 20000 / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots = 0;
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H ), myboxF( L ) );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );

PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorBlue, 0, L, Offset = -40 );
PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorBlue, 0, L, Offset = -50 );
PlotShapes( IIf( Buy, shapeHollowUpArrow, shapeNone ), colorWhite, 0, L, Offset = -45 );
PlotShapes( shapeSmallUpTriangle * Sell   , colorDarkGrey, 0, Low );
PlotShapes( IIf( vCheck, shapeStar, shapeNone ), colorBlue, 0, L, -20 );
vlots = V / RoundLotSize;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -10 );


_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + ", Vol %0.f"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 ), VMA
                       ) );




myvar  	+= ", lots = " + bLots;
myvar	+= ", risk = " + round( RoundLotSize * bRisk );

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );