//------------------------------------------------------------------------------
//
//  Formula Name:    Trend Detection
//  Author/Uploader: <PERSON> 
//  E-mail:          g<PERSON><PERSON><EMAIL>
//  Date/Time Added: 2005-01-10 18:55:44
//  Origin:          
//  Keywords:        Trends
//  Level:           medium
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=418
//  Details URL:     http://www.amibroker.com/library/detail.php?id=418
//
//------------------------------------------------------------------------------
//
//  Plots coloured bars for direction of current trend, green for rising, red
//  for falling. Exploration included to find first bar of new direction.
//
//  Param included to vary the number of periods for detecting small to long
//  term trends.
//
//  I am using beta version 4.66.2, but believe this will work with last
//  offical version.
//
//------------------------------------------------------------------------------

// Trend Detection
// Graham Kavanagh 11 Jan 05
// I am using version 4.66.2, but believe this will work with last offical version.

function Rise( Pd, perd, Pl, perl )
{
 MAD = DEMA(Pd,perd);
 MAL = LinearReg(Pl,perl);
 CondR = ROC(MAD,1)>0 AND ROC(MAL,1)>0;
 CondF = ROC(MAD,1)<0 AND ROC(MAL,1)<0; 
 R[0] = C[0]>(H[0]+L[0])/2;

 for(i=1;i<BarCount;i++)
 {
  if( CondR[i] )
  {
   R[i] = 1;
  }
  else
  {
   if( CondF[i] )
   {
    R[i] = 0;
   }
   else
   {
    R[i] = R[i-1];
   }
  }
 }
 return R;
} 

PrD = C;
PrL = H/2+L/2;
PrdD = PrdL = PrdM = Param("Prd",12,2,40,1);

permax = Max(prdd,prdl);

Rs = IIf( BarIndex()<permax, 0, Rise(PrD, PrdD, PrL, PrdL) );
Fs = IIf( BarIndex()<permax, 0, 1-Rs );

Confirm = MA(C,prdm);

function DirBar( dr, df )
{
B[0] = 0;

for(i=1;i<BarCount;i++)
{
 if( dr[i-1] && df[i]  )
 {
  B[i] = 1;
 }
 else
 {
  if( df[i-1] && dr[i] )
  {
   B[i] = 1;
  }
  else
  {
   B[i] = B[i-1] + 1;
  }
 }
}
return B;
}

Bs = DirBar( Rs, Fs );
Direction = ROC(Confirm,1) > 0 AND ROC(Confirm,5) > 0;
Downward = ROC(Confirm,1) < 0 AND ROC(Confirm,5) < 0;

Select = Rs AND Ref(Fs,-1);
Caution = Fs AND Ref(Rs,-1);

Change = IIf( Rs, H/ValueWhen(Fs,L)*100, L/ValueWhen(Rs,H)*100 );

Plot( C, "close", IIf( Rs, colorGreen, IIf( Fs, colorRed, colorBlack )), styleBar);

PlotShapes( shapeSmallCircle* select, colorDarkGreen, 0, H, 5 );
PlotShapes( shapeSmallCircle* Caution, colorDarkRed, 0, L, -5 );

GraphXSpace=10;
_N( Title = "{{NAME}} - {{INTERVAL}} {{DATE}} Trend Plot - "+prdd+" Day" );


Filter = Select OR Caution;
AddColumn( Select, "UpTurn", 1 );
AddColumn( Caution, "DownTurn", 1 );

// ---indicator end---
"Rise = " + Rs;
"Fall = " + Fs;
"Current Trend Bars = " + Bs;
"Trend Move = " + Change + " %";