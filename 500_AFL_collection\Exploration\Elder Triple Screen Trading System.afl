//------------------------------------------------------------------------------
//
//  Formula Name:    Elder Triple Screen Trading System
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2005-07-04 19:06:13
//  Origin:          As derrived from "Trading For A Living" and "Come Into My Trading Room" by <PERSON>.
//  Keywords:        Triple Screen, <PERSON>,Pullbacks
//  Level:           medium
//  Flags:           system,exploration
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=485
//  Details URL:     http://www.amibroker.com/library/detail.php?id=485
//
//------------------------------------------------------------------------------
//
//  Elder Triple Screen Trading System. As derrived from "Trading For A Living"
//  and "Come Into My Trading Room" by <PERSON>. This scan finds long
//  candidates by the weekly MACD rising and the weekly 13 period Force Index
//  above it's zero line. Plot a weekly EMA (26 period)...it should be rising
//  to go long. The daily 2 period Force Index must be below it's zero line.
//  Look for the stock on a pullback around it's daily 13 period ema. Also use
//  the daily 22 period ema to determine the direction of the daily trend. Do
//  the opposite for shorts.
//
//------------------------------------------------------------------------------

// Elder Triple Screen Trading System.
// Coded by Dennis Skoblar 7/04/2005.
// As derrived from "Trading For A Living" and "Come Into My Trading Room" by Alexander Elder.
// 
// This scan finds long candidates by the weekly MACD rising and the weekly 13 period Force Index above it's zero line.
// Plot a weekly EMA (26 period)...it should be rising to go long.
// The daily 2 period Force Index must be below it's zero line. Look for the stock on a pullback around it's daily 13 period ema.
// Also use the daily 22 period ema to determine the direction of the daily trend.
// Do the opposite for shorts.

TimeFrameSet( inWeekly );
WeeklyMACD = MACD( 12, 26 ) - Signal( 12, 26, 9 );
WeekHistRising = IsTrue ( Ref(WeeklyMACD, -1) < Ref(WeeklyMACD, 0) );
WeekHistFalling = IsTrue ( Ref(WeeklyMACD, -1) > Ref(WeeklyMACD, 0) );
FIWeekly = EMA(((C-Ref(C,-1))*V),13);
WeeklyForceIndexLong = IsTrue ( FIWeekly > 0 );
WeeklyForceIndexShort = IsTrue ( FIWeekly < 0);
TimeFrameRestore();

// Weekly criteria
MACDLongW = TimeFrameExpand( WeekHistRising, inWeekly );
MACDShortW= TimeFrameExpand( WeekHistFalling, inWeekly );
FILongW = TimeFrameExpand( WeeklyForceIndexLong, inWeekly );
FIShortW = TimeFrameExpand( WeeklyForceIndexShort, inWeekly );

// Daily criteria
FIDaily = EMA(((C-Ref(C,-1))*V),2);
FILongD = IsTrue ( FIDaily < 0 );
FIShortD = IsTrue ( FIDaily > 0 );
PVFilter = (C>15) AND Ref(MA(V,50),-1)>100000;
TenTwentyFilter = HHV(H,20)-LLV(L,20);
FiftyDayHVFilter = round(StDev(log(C/Ref(C,-1)),50)*100*sqrt(256));

// Scan criteria
ElderLong = MACDLongW AND FILongW AND FILongD;
ElderShort = MACDShortW AND FIShortW AND FIShortD;

// Columns for exploration

NumColumns = 7;

Column0 = FullName();     
Column0Name = "Ticker name";

Column1 = ElderLong;
Column1Name = "Long";

Column2 = ElderShort;
Column2Name = "Short";

Column3 = FiftyDayHVFilter;
Column3Name = "50 Day HV";

Column4 = TenTwentyFilter;
Column4Name = "10/20 Filter";

Column5 = TimeFrameExpand( FIWeekly, inWeekly );
Column5Name = "Weekly Force Index";

Column6 = FIDaily;
Column6Name = "Daily Force Index";

AddTextColumn( IndustryID(1), "Industry" );

AddTextColumn( MarketID(1), "Market" );

// Filters
Filter = TenTwentyFilter > 5 AND PVFilter AND (ElderLong OR ElderShort);
Buy= ElderLong;
Sell = ElderShort;

// Indicators

// 2 Period Force Index (Daily Graph)
//_SECTION_BEGIN("Force Index 2 Day");
//FI=EMA(((C-Ref(C,-1))*V),2);
//Plot(FI,"FI",colorCustom11,styleLine);
//Plot(0,"ZERO LINE",colorWhite,styleThick);
//_SECTION_END();

// 13 Period Force Index (Weekly Graph)
//_SECTION_BEGIN("Force Index 13 Day");
//FI=EMA(((C-Ref(C,-1))*V),13);
//Plot(FI,"FI",colorCustom11,styleLine);
//Plot(0,"ZERO LINE",colorWhite,styleThick);
//_SECTION_END();

// Elder Ray (Daily Graph)
// Bull Power (plot as seperate graph)
//bullpower= High - EMA(Close,13); 
//Plot(bullpower,"Elder Ray -- Bull Power",colorCustom11,styleHistogram);
// Bear Power (plot as seperate graph)
//bearpower= Low - EMA(Close,13); 
//Plot(bearpower,"Elder Ray -- Bear Power",colorRed,styleHistogram);




