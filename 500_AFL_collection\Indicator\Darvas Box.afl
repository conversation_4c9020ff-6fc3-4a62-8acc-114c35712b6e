//------------------------------------------------------------------------------
//
//  Formula Name:    Darvas Box
//  Author/Uploader: Stephane 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2004-12-06 14:06:18
//  Origin:          
//  Keywords:        
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=414
//  Details URL:     http://www.amibroker.com/library/detail.php?id=414
//
//------------------------------------------------------------------------------
//
//  An explanation of the construction of the Darvas Box is available at:
//
//  1) <http://www.gerryco.com/tech/darvas.html>
//
//  2) <http://www.wealth-lab.com/cgi-bin/WealthLab.DLL/editsystem?id=2814>
//
//------------------------------------------------------------------------------

//////////begin/////////

box1=0;
box2=0;
SetBarsRequired(10000,10000);
procedure fillDarvas(start,end,swap,top, bottom )
{
   for ( j = start; j < end; j++)
   {
       if( box1[j] == swap)
  box1[j]= top ;
else
box1[j]= bottom;

       if(box2[j] == swap)
  box2[j]= bottom ;
else
box2[j]= top;
   }
}

BoxArr1 = 0;
BoxArr2 = 0;
StateArray = 0;
DBuy = 0;
DSell = 0;
TopArray = 0;
BotArray = 0;
tick=0;

BoxTop = High[0];
BoxBot = Low[0];
swap=0;
state = 0;
BoxStart = 0;

for (i=0; i<BarCount; i++)
{
 if (state==5)
 {
  TopArray[i]=BoxTop;
  BotArray[i]=BoxBot;
  if (Low[i]<(BoxBot*(1-tick/100)) || High[i]>(BoxTop*(1+tick/100)))
  {
fillDarvas(BoxStart,i,swap,BoxTop,BoxBot);

  state = 1;
  swap =  !swap;
  BoxTop = High[i];
  BoxStart = i;
  }
 }
 else
 {
  if (High[i]<BoxTop)
  {
      if ((state<3) || (Low[i]>BoxBot))
      {
   state++;
   }
      else
   {
      state=3;
   }
      if (state==3)
      BoxBot=Low[i];
  }
  else
  {
      state=1;
      BoxTop=High[i];
  }
 }
 StateArray[i] = state;
}

fillDarvas(BoxStart,BarCount,swap,BoxTop,BoxBot);

Plot(C,"",1,64);
Plot( box1, "" , 1 + statearray, styleLine );
Plot( box2, "" , 1 + statearray , styleLine );