//------------------------------------------------------------------------------
//
//  Formula Name:    Candle Stick Demo
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2005-01-28 16:27:56
//  Origin:          
//  Keywords:        Candle Pattern Demo
//  Level:           medium
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=421
//  Details URL:     http://www.amibroker.com/library/detail.php?id=421
//
//------------------------------------------------------------------------------
//
//  This program takes a segment from your price chart and substitutes the last
//  bar visible with a representative Candle stick for the period selected. It
//  allows you to observe how candles are formed and how loss of
//  synchronization of the Candle period, or using different time frames, can
//  result in totally different chart patterns for the same price array.
//
//------------------------------------------------------------------------------

_SECTION_BEGIN("Candle Stick Demo");
// Candlestick demo by herman van den Bergen
BI				= BarIndex();
barvisible 	= Status("barvisible");
FirstBarIndx= LastValue( Lowest(ValueWhen(BarVisible,BI)));
LastBarIndx	= LastValue( Highest(ValueWhen(BarVisible,BI)));
FirstBarVis = FirstBarIndx == BI; 
LastBarVis 	= LastBarIndx == BI; 

Width=Param("Bar Width",5,1,30,1);
EndCandle = SelectedValue(BarIndex())+int(Width/2)+1; 
StartCandle = Endcandle-Width;
CandleData = Flip(StartCandle == BI,EndCandle == BI);

MAsk2 = IIf(BI>=StartCandle,1,Null);
MAsk1 = IIf(CandleData OR LastBarVis, 1, Null);

Oc = LastValue(ValueWhen(StartCandle == BI, O));
Hc = LastValue(ValueWhen(CandleData , HHV(H,Width)));
Lc = LastValue(ValueWhen(CandleData , LLV(L,Width)));
CC = LastValue(ValueWhen(CandleData , C));

O = IIf( LastBarVis, Oc, O);
H = IIf( LastBarVis, HC, H);
L = IIf( LastBarVis, LC, L);
C = IIf( LastBarVis, CC, C);

if( Param("Style: Candle/Bar",0,0,1,1) == 0 ) BarStyle = 64; else BarStyle = 128; 
Plot(C,"",1,BarStyle |styleNoLabel|styleThick);
Plot(Mask2*Oc,"",7,styleStaircase);
Plot(Mask2*CC,"",2,styleStaircase);
Plot(Mask2*HC,"",8,styleStaircase);
Plot(Mask2*LC,"",4,styleStaircase);

Co1 = Param("BackGroundColor",47,0,64,1);
Plot(Mask1*LC,"",Co1,styleArea);
Co2 = Param("CandleAreaColor",39,0,64,1);
Plot(Mask1*HC,"",Co2,styleArea);

Title="\nCandlestick Demo\n"+
"Candle Date:   "+Date()+"\n"+
"Candle Width:   "+NumToStr(Width,1.0)+" bars\n";

GraphXSpace = 5;
 
_SECTION_END();