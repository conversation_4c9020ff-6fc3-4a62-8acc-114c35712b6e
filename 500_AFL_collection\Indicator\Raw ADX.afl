//------------------------------------------------------------------------------
//
//  Formula Name:    Raw ADX
//  Author/Uploader: <PERSON> 
//  E-mail:          
//  Date/Time Added: 2006-09-19 08:18:17
//  Origin:          MS formula conversion - Source: TASC Oct 1999 issue
//  Keywords:        Trend Trading System
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=711
//  Details URL:     http://www.amibroker.com/library/detail.php?id=711
//
//------------------------------------------------------------------------------
//
//  This ADX formula uses input variable for the periods and
//
//  uses <PERSON><PERSON> and <PERSON><PERSON> in its calculation.
//
//------------------------------------------------------------------------------

/* ADX Raw  */
//Afl code - Prakash Shenoi //

TP= Param("Time Periods",14,1,50);
PlusDM= IIf(High>Ref(High,-1) AND
Low>=Ref(Low,-1), High-Ref(High,-1),
IIf(High>Ref(High,-1) AND Low<Ref(Low,-1)
AND High-Ref(High,-1)>Ref(Low,-1)-Low,
High-Ref(High,-1), 0));
DIPlus= 100 * Wilders(PlusDM,TP) /
ATR(TP);

MinusDM= IIf(Low<Ref(Low,-1) AND
High<=Ref(High,-1), Ref(Low,-1)-Low,
IIf(High>Ref(High,-1) AND Low<Ref(Low,-1)
AND High-Ref(High,-1)<Ref(Low,-1)-Low,
Ref(Low,-1)-Low, 0));
DIMinus = 100 * Wilders(MinusDM,TP) /
ATR(TP);

Diff= abs(DIPlus - DIMinus);
DISum= DIPlus + DIMinus;
ADXRaw= 100 * Wilders(Diff/DISum, TP);

Plot (ADXRaw,"ADXRaw",1,styleThick);
//Plot (DiPlus,"DI+", 3,1);
//Plot (DiMinus,"DI-",4,1);
GraphXSpace=4;