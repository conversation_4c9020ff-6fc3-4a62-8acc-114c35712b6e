//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi Arafat=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>
#include <Options.afl>

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

//=================================================================================
// Put
//=================================================================================
SetForeign( SymbolPE );
#include<KolierSignals.afl>

myvar  	+= ", lots = " + bLots;
myvar	+= ", risk = " + round( RoundLotSize * bRisk );
myvar	+= ", ROC = " + ROC( C, 3 );

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );

vlots = V / RoundLotSize;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -10 );

RestorePriceArrays();

GfxSelectFont( "BOOK ANTIQUA", 10, 100 );
GfxSetBkMode( 1 );
GfxSetTextColor( colorWhite );
GfxSelectSolidBrush( colorDarkGrey );

GfxSelectPen( colorLightBlue, 1 ); // border color
pxHeight = Status( "pxchartheight" ) ;
xx = Status( "pxchartwidth" );
x = 20;
x2 = 110;
y = pxHeight;
GfxRoundRect( x, y - 25, x2, y - 5, 7, 7 ) ;
