//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Tata Motors, Reliance
//-----------------------------------------------------

#include <Kolier.afl>
#include <ReactionTrendSystem.afl>

SetChartBkColor( colorWhite ) ;
SetChartOptions( 0, chartShowArrows | chartShowDates );
// OptimizerSetEngine( "cmae" );

Periods		= Param( "Periods", 55, 40, 100, 5 ); //50
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 6.0 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
equityAtRisk	= Param( "Risk", 25000, 15000, 35000, 5000 );
atrMultiplier   = Param( "ATR_Multiplier", 4, 2, 9, 0.5 ); // 4
atrPeriod       = Param( "ATR_Period", 5, 5, 10, 1 );

Vw 		= myround( VWMA2( C, Periods ) );
Lr 		= mybox( ( LinearReg( C, Periods ) + H + L ) / 3 );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( Lr > Vw + boxSize, Lr < Vw - boxSize );
Sd = StDev( C, Periods );

lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

ATR_mul = 2;
f = OptimizeNot( "F", 2.4, 1.0, 6, 0.1 );
ATR_f = ATR( Periods );

up   	= Lr + f * Max( ATR_f * ATR_mul, Sd );
down 	= Lr - f * Min( ATR_f * ATR_mul, Sd );
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

L_5 = myboxF( LLV( L, 15 ) );
H_5 = myboxC( HHV( H, 15 ) );
H_5_1 = Ref( H_5, -1 );
L_5_1	= Ref( L_5, -1 );

pp = Optimize( "L", 40, 5, 50, 5);
L_25 = myboxF( LLV( L, pp ) );
H_25 = myboxC( HHV( H, pp ) );
H_25_1 = Ref( H_25, -1 );
L_25_1	= Ref( L_25, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;

InBuy = 0;
InShort = 0;

BuyCondition   =     mode AND L > Lr_1 AND H > H_25_1 AND TradingZone;
ShortCondition = NOT mode AND H < Lr_1 AND L < L_25_1 AND TradingZone AND C < O;

p1 = OptimizeNot( "p1", 0.4, 0.1, 2.0, 0.1 );
p2 = OptimizeNot( "p2", 0.3, 0.1, 2.0, 0.1 );

Buy   = TradingZone AND Ref( L, -1 ) > Lr_1 AND C > Ref( C, -1 ) AND Ref( C, -1 ) > Ref( C, -2 ) AND Ref( C, -2 ) > Ref( C, -3 ) AND C > O AND Ref( C, -1 ) > Ref( O, -1 ) AND Ref( C, -2 ) > Ref( O, -2 )
AND(
              ( StrFind( Name(), "JUN" ) AND DateNum() >= 1240520 AND DateNum() <= 1240615 ) OR
              ( StrFind( Name(), "JUL" ) AND DateNum() >= 1240618 AND DateNum() <= 1240712 ) OR
              ( StrFind( Name(), "AUG" ) AND DateNum() > 1240712 )
          );
Short = 0;
exit = ValueWhen(Buy, Min(Ref( L, -1 ), Lr_1));
exit = Max(Lr_1, Ref(exit, -1));

// PlotShapes( ( mode AND Ref( ROC( C, 1 ), -1 ) > p1 ) * shapeSmallCircle, colorBlue, 0, H );

myVar = ", C1 = " + ( Ref( ROC( C, 1 ), -1 ) ) + ", C2 = " + ( Ref( L, -1 ) > Ref( Lr_1, -1 ) ) + ", C3 = " + ( ( C - L_5 ) / ( Ref( C, -2 ) - L_5 ) ) + ", C4 = " + ( Lr_1 > Vw_1 )
        + ", C5 = " + ( abs( Ref( H, -1 ) - H_25 ) < 2 * boxSize );

Sell = C < Lr_1;
Cover = H > H_25_1 OR C < L_25_1;

CoverPrice = IIf(H > H_25_1, H_25_1, C);
BuyPrice   = IIf(L < L_25_1, L_25_1, C);

sRisk   = H_25_1 - C;
bRisk 	= C - Vw_1;
max_sl	= myRound( equityAtRisk / RoundLotSize );

exit = myRound( exit );
PT	 = myRound( PT );

max_lots = Min( floor( 750000 / ( RoundLotSize * C * 0.2 ) ), 20 );
bLots	= Min( max_lots, floor( equityAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Min( max_lots, floor( equityAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.1f, %0.1f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
myVar 	+= ", boxSize = " + boxSize;

bkcolor = LastValue( IIf( mode, ColorBlend( colorCustom9, colorWhite ), IIf( mode, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

Sell = ExRem( Sell, Buy );
Cover = ExRem( Cover, Short );
Buy = ExRem(Buy, Sell);

Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, colorLightGrey ), styleStaircase | styleDashed |  styleNoLabel * IIf( LastValue( exit ), 0, 1 ), Null, Null, Null, -1 );

Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( Vw , "Vw", IIf( mode, colorGreen, colorPink ), ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( Lr_1, "", colorGrey40, ParamStyle( "Style" ) | styleThick | styleNoLabel, Null, Null, Null, 2 );

#include <AudioAlert.afl>

_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );


