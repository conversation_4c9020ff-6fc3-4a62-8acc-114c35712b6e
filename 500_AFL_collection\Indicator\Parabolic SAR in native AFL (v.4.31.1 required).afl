//------------------------------------------------------------------------------
//
//  Formula Name:    Parabolic SAR in native AFL (v.4.31.1 required)
//  Author/Uploader: <PERSON><PERSON> 
//  E-mail:          
//  Date/Time Added: 2003-04-13 10:42:45
//  Origin:          <PERSON>
//  Keywords:        
//  Level:           semi-advanced
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=268
//  Details URL:     http://www.amibroker.com/library/detail.php?id=268
//
//------------------------------------------------------------------------------
//
//  Here is a sample code that shows how to use for/if/else control statements
//  introduced in AmiBroker 4.31.x BETA.
//
//  The code re-implements Parabolic Stop-And-Reverse system/indicator.
//
//  The code is quite similar to JScript version.
//
//------------------------------------------------------------------------------

/////////////////////////////////
// Parabolic SAR re-implemented in
// native AFL.
//
// Example of for/if/else control statements
//
// Requires:
// AmiBroker 4.31.1 
// 
// Written by: Tomasz Janeczko

IAF = 0.02;       // acceleration factor
MaxAF = 0.2;     // max acceleration

psar = Close;		// initialize
long = 1;        // assume long for initial conditions
af = IAF;         // init acelleration factor
ep = Low[ 0 ];   // init extreme point
hp = High [ 0 ];
lp = Low [ 0 ];

for( i = 2; i < BarCount; i++ )
{
	if ( long )
	{
		psar [ i ] = psar [ i-1 ] + af * ( hp - psar [ i-1 ] );
	}
	else
	{
		psar [ i ] = psar [ i-1 ] + af * ( lp - psar [ i-1 ] );
	}

	reverse =  0;
	//check for reversal
	if ( long )
	{
		if ( Low [ i ] < psar [ i ]  )
		{
			long = 0; reverse = 1; // reverse position to Short
			psar [ i ] =  hp;       // SAR is High point in prev trade
			lp = Low [ i ];
			af = IAF;
		}
	}
	else
	{
		if ( High [ i ] > psar [ i ]  )
		{
			long = 1; reverse = 1;        //reverse position to long
			psar [ i ] =  lp;
			hp = High [ i ];
			af = IAF;
		}
	}

	if ( reverse == 0 )
	{
		if ( long )
		{
			if ( High [ i ] > hp ) 
			{
				hp = High [ i ]; 
				af = af + IAF; 
				if( af > MaxAF ) af = MaxAF; 
			}
             
			if( Low[ i - 1 ] < psar[ i ] ) psar[ i ] = Low[ i - 1 ];
			if( Low[ i - 2 ] < psar[ i ] ) psar[ i ] = Low[ i - 2 ];
		}
       else
		{
			if ( Low [ i ] < lp )  
			{ 
				lp = Low [ i ]; 
				af = af + IAF; 
				if( af > MaxAF ) af = MaxAF; 
			}	
				
			if( High[ i - 1 ] > psar[ i ] ) psar[ i ] = High[ i - 1 ];
			if( High[ i - 2 ] > psar[ i ] ) psar[ i ] = High[ i - 2 ];

		}
	}
}

Plot( Close, "Price", colorBlack, styleCandle );
Plot( psar, "SAR", colorRed, styleDots | styleNoLine | styleThick );

