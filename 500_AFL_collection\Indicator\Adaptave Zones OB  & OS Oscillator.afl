//------------------------------------------------------------------------------
//
//  Formula Name:    Adaptave Zones O/B  & O/S Oscillator
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-12-27 10:22:54
//  Origin:          
//  Keywords:        
//  Level:           medium
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=144
//  Details URL:     http://www.amibroker.com/library/detail.php?id=144
//
//------------------------------------------------------------------------------
//
//  Automatically Adjusts the Overbought and Oversold levels based on past
//  performance.( Lookback ).
//
//  Set up to be used with AmiBroker PrePackaged
//  indicators,RSI,CCI,STOCHK,STOCHD,MFI,ULTIMATE,ROC,
//
//  Also, Added RSI of MACD indicator.
//
//  User Friendly, Set with Vairable Identifiers and information in the title
//  as to what indicator is being studied.
//
//  A1=RSIt; B2=RSI(pds); C3=CCI(pds); D4=StochK(pds); E5=StochD(pds);
//
//  F6=MFI(pds); G7=Ultimate(pds); H8=ROC(C,pds);
//
//  Osc=C3; /*insert variable by Identifier*/
//
//  Further Improvements will be Added to Enhance.
//
//------------------------------------------------------------------------------

/*Adaptive Zones OB/OS OSCILLATOR*/
/*Automatically Adjusts the overbought and oversold levels based on past performance*/
/*Interpreted and coded by Anthony Faragasso*/
/*Can be used with PrePackaged indicators,RSI,CCI,STOCHK,STOCHD,MFI,ULTIMATE,*/
/*For ROC add the Close. ex.ROC(C,pds);*/

MaxGraph=10;
/*Input */
Lookback=60;
PerCent=95;
Pds =14;

/****Dimitri Code***********/
/*14-Day RSI of MACD()*/ 
//t=14; Replaced with pds= statement
Var=MACD(); 
Up=IIf(Var>Ref(Var,-1),abs(Var-Ref(Var,-1)),0); 
Dn=IIf(Var<Ref(Var,-1),abs(Var-Ref(Var,-1)),0); 
Ut=Wilders(Up,Pds); 
Dt=Wilders(Dn,Pds);
RSIt=100*(Ut/(Ut+Dt)); 
/************End Code*************************/

/*******Variables******************/

A1=RSIt; B2=RSI(pds); C3=CCI(pds); D4=StochK(pds); E5=StochD(pds);
F6=MFI(pds); G7=Ultimate(pds); H8=ROC(C,pds);


Osc=C3; /*insert variable by Identifier*/ 

/*Value of Osc*/
Value1 = Osc;

/*Highest AND Lowest Values of Osc during Lookback Period*/
Value2 = HHV(Value1,Lookback);
Value3 = LLV(Value1,Lookback);

/*Range of Osc during Lookback Period*/
Value4 = Value2 - Value3;

/*Define PerCent of Range to determine OB AND OS levels*/
Value5 = Value4 * (PerCent / 100);

/*Calculate OB AND OS levels*/
Value6 = Value3 + Value5;
Value7 = Value2 - Value5;

baseline=IIf( Osc < 100 AND Osc > 10 ,50 ,IIf( Osc < 0 ,0,0));

Header=WriteIf(Osc==A1," RSI Of MACD",WriteIf(Osc==b2," RSI",WriteIf(Osc==C3," CCI",WriteIf(Osc==D4,"STOCHK",WriteIf(Osc==E5,"STOCHD",WriteIf(Osc==F6," MONEY FLOW INDEX",WriteIf(Osc==G7," ULTIMATE",WriteIf(Osc==H8," ROC(CLOSE)",""))))))));

Plot(Value1, Header,6,1+4); /*BLUE*/
Plot(Value6," O/B",4,1+4);  /*RED -TOP(SELL)*/
Plot(Value7," O/S",5,1+4);  /*GREEN-BOT(BUY)*/
Plot(Baseline," Baseline",7,1+4); /* yellow*/
