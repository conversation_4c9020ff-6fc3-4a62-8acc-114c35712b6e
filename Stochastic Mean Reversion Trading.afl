//code by <PERSON><PERSON><PERSON> R
//Author - www.marketcalls.in

_SECTION_BEGIN( "Stochastic Mean Reversion Trading" );

OptimizerSetEngine( "cmae" );

SetBarFillColor( IIf( C > O, ParamColor( "Candle UP Color", colorGreen ), IIf( C <= O, ParamColor( "Candle Down Color", colorRed ), colorLightGrey ) ) );
Plot( C, "Price", IIf( C > O, ParamColor( "Wick UP Color", colorDarkGreen ), IIf( C <= O, ParamColor( "Wick Down Color", colorDarkRed ), colorLightGrey ) ), 64, 0, 0, 0, 0 );

Dayreturn = ROC( C, 1 );
AC = Optimize( "AC", 14, 1, 20, 1 );
par1 = Optimize( "Par1", 2, 1, 20, 1 );
par2 = Optimize( "Par2", 3, 1, 20, 1 );

AutoCor = Correlation( Dayreturn, Ref( Dayreturn, -1 ), AC );

crossBuy = Cross( StochK( par1, par2 ), StochD( par1, par2, par2 ) );
crossSell = Cross( StochD( par1, par2, par2 ), StochK( par1, par2 ) );

Buy = AutoCor < 0 AND crossBuy;
Sell = AutoCor > 0 OR crossSell;

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );

BuyPrice = ValueWhen( Buy, C );
SellPrice = ValueWhen( Sell, C );


// Dynamic Stop Losses
BuyStop = ValueWhen(Buy, LLV(C, -1 * 5));

eqAtRisk = 100000;
bRisk = round(IIf( BuyStop < C, C - BuyStop, C - L ));
bLots	= Min(5, Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) ));
printf("bLots = %g", bLots);

ApplyStop(stopTypeLoss, stopModePoint, bRisk, 1);

SetPositionSize( bLots*RoundLotSize, spsShares );

PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorGreen, 0, L, Offset = -40 );
PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorLime, 0, L, Offset = -50 );
PlotShapes( IIf( Buy, shapeUpArrow, shapeNone ), colorWhite, 0, L, Offset = -45 );

PlotShapes( IIf( Sell, shapeSquare, shapeNone ), colorRed, 0, H, Offset = 40 );
PlotShapes( IIf( Sell, shapeSquare, shapeNone ), colorOrange, 0, H, Offset = 50 );
PlotShapes( IIf( Sell, shapeDownArrow, shapeNone ), colorWhite, 0, H, Offset = -45 );


_SECTION_END();