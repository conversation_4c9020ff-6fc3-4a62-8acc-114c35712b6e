//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Tata Motors, Reliance
//-----------------------------------------------------

#include <ReactionTrendSystem.afl>
SetChartBkColor( colorWhite ) ;
OptimizerSetEngine( "cmae" );

Periods		= Param( "RP", 125, 10, 500, 5 ); //470
StopLoss	= Param( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= Param( "Target", 5, 4, 7, 0.5 ) / 100;
eqAtRisk	= Param( "Risk", 25000, 20000, 100000, 5000 );

Vw 		= myround( VWMA2( C, Periods ) );
Lr 		= mybox( LinearReg( C, 130 ) );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( Lr > Vw + boxSize, Lr < Vw - boxSize );
Sd = StDev( C, Periods );

lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

ATR_mul = 1;
f = OptimizeNot( "F", 2.4, 1.0, 6, 0.1 );
ATR_f = ATR( Periods );

down 	= Lr - f * Min( ATR_f * ATR_mul, Sd );
down_1 	= Ref( down, -1 );

pp = Optimize( "L", 24, 1, 50, 1 );
L_4 = myboxF( LLV( L, 4 ) );
H_15 = myboxC( HHV( H, 15 ) );
H_15_1 = Ref( H_15, -1 );
L_4_1	= Ref( L_4, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
sRisk 	= Max(0, Lr_1 - C);
bRisk 	= Max(0, C - Lr_1);
max_sl	= myRound( eqAtRisk / RoundLotSize );
curr_sl	= 0;

//------------------------------------------------------
// Entry and exit conditions
//------------------------------------------------------
BuyCondition   =     mode AND L > Lr_1 AND H > H_15_1 AND TradingZone AND WhiteBody;
ShortCondition = NOT mode AND H < Lr_1 AND L < L_4_1 AND TradingZone AND BlackBody;
InBuy = InShort = 0;

for( i = 1; i < BarCount; i++ )
{
    if( Buyflag )
    {
        InBuy[i] = 1;
        t_exit =  IIf(down_1[i] < Vw_1[i], Lr_1[i], Min( Lr_1[i], down_1[i] ));

        if( H[i] >= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = C[i - 1] * ( 1 - StopLoss );
        }

        if( O[i] > t_exit )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        PT[i]   = PT[i - 1];
        exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Shortflag )
    {
        InShort[i] = 1;
        t_exit = Lr_1[i];

        if( L[i] <= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = C[i - 1] * ( 1 + StopLoss );
        }

        if( O[i] < t_exit )
        {
            exit[i] = Min( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        PT[i]   = PT[i - 1];
        exit[i] = ceil( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Buyflag AND L[i] < exit[i] AND O[i] > exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Buyflag AND C[i - 1] < exit[i - 1] AND O[i - 1] < exit[i - 1] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = O[i];
    }

    if( Shortflag AND H[i] > exit[i] AND O[i] < exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( ( O[i] < exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Shortflag AND C[i - 1] > exit[i - 1] AND O[i - 1] > exit[i - 1] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = O[i];
    }

    if( NOT Buyflag AND BuyCondition[i] )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= Lr_1[i];
        Buyflag 	= 1;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i] 	= BuyP - exit[i];
        curr_sl		= bRisk[i];
        PT[i]		= BuyP * ( 1 + Target );
        PThit		= False;
    }

    if( NOT Shortflag AND ShortCondition[i] )
    {
        ShortP			= C[i];
        ShortPrice[i]	= ShortP;
        exit[i]			= Lr_1[i];
        Buyflag   		= 0;
        Shortflag 		= 1;
        Short[i]  		= 1;
        sRisk[i] 		= ( exit[i] - ShortP );
        curr_sl			= sRisk[i];
        PT[i]			= ShortP * ( 1 - Target );
        PThit			= False;
    }
}

exit = myRound( exit );
PT	 = myRound( PT );
bRisk = IIf( bRisk == 0 AND exit < C, C - exit, brisk );
sRisk = IIf( sRisk == 0 AND exit > C, exit - C, sRisk );

bLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

max_lots = 6;
bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.1f, %0.1f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

//------------------------------------------------------
// Plotting and Alerts
// draw levels from Reaction Trend System
//------------------------------------------------------
if( ParamToggle( "Show RT", "0|1" ) )
{
    Plot( B1, "", colorBrightGreen, styleStaircase | styleNoLabel );
    Plot( S1, "", colorRed, styleStaircase | styleNoLabel );
    // Plot( HBOP, "", colorOrange, styleStaircase | styleNoLabel );
    // Plot( LBOP, "", colorGreen, styleStaircase | styleNoLabel );
}

vlots = V / RoundLotSize;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );

Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, colorLightGrey ), styleStaircase | styleDashed |  styleNoLabel * IIf( LastValue( exit ), 0, 1 ), Null, Null, Null, -1 );
Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( IIf( TradingZone AND mode AND NOT InBuy, Ref( H_15, -1 ), null ), "", colorBlue, styleDashed, Null, Null, Null, 1 );
Plot( IIf( TradingZone AND NOT mode AND NOT InShort, Ref( L_4, -1 ), null ), "", colorRed, styleDashed, Null, Null, Null, 1 );
Plot( Vw , "", IIf( mode, colorGreen, colorPink ), ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( Lr_1, "", colorGrey40, ParamStyle( "Style" ) | styleThick | styleNoLabel, Null, Null, Null, 2 );
Plot( MA(Vw, 50) , "", colorBlack, ParamStyle( "Style" ) | styleThick | styleNoLabel );

#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );
_SECTION_END();

//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTSTK";
SYS  = "FUT";
// #include<AlgoFoxAuto/AlgoFoxAuto.afl>
