//------------------------------------------------------------------------------
//
//  Formula Name:    Second-order Infinite impulse response filter
//  Author/Uploader: <PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2003-04-30 15:43:03
//  Origin:          
//  Keywords:        moving average functions
//  Level:           semi-advanced
//  Flags:           indicator,function
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=274
//  Details URL:     http://www.amibroker.com/library/detail.php?id=274
//
//------------------------------------------------------------------------------
//
//  This code demonstrates new looping and user-definable functions.
//
//------------------------------------------------------------------------------

// the following function is 2nd order smoother
// Input is the data array
// f0, f1, f2 are filter coefficients
// you can try 0.2, 1.4, -0.6 

function IIR2( input, f0, f1, f2 )
{
  result[ 0 ] = input[ 0 ];
  result[ 1 ] = input[ 1 ]; 

  for( i = 2; i < BarCount; i++ )
  {
    result[ i ] = f0 * input[ i ] + 
    f1 * result[ i - 1 ] + 
    f2 * result[ i - 2 ]; 
  }

  return result;
}

Plot( Close, "Price", colorBlack, styleCandle );
Plot( IIR2( Close, 0.2, 1.4, -0.6 ), "function example", colorRed ); 
