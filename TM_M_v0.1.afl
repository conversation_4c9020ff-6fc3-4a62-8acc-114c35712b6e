//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Tata Motors
//-----------------------------------------------------

#include <ReactionTrendSystem.afl>
OptimizerSetEngine( "cmae" );

SetChartOptions( 0, chartShowArrows | chartShowDates );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

//----------------------------------------------------------------
// SECTION: PARAMETERS
//----------------------------------------------------------------
_SECTION_BEGIN( "Parameters" );
Periods		= Param( "Periods", 55, 50, 500, 5 ); //470
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 6.0 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
eqAtRisk	= Param( "Risk", 25000, 20000, 100000, 5000 );

// -- Short Strategy Params --
Periods_Short = Param( "Periods (Short)", 62, 20, 200, 2 );  // 50 - 70
boxSizePercent = Param( "Box %", 0.4, 0.1, 2, 0.1 );
boxSizeORbFactor = Param( "ORB ATR factor", 0.9, 0.2, 3, 0.1 );
p_no_above = Param( "No Above MA (Short)", 85, 5, 200, 5 );
_SECTION_END();

//----------------------------------------------------------------
// SECTION: CUSTOM BAR & INDICATORS
//----------------------------------------------------------------
_SECTION_BEGIN( "Calculations" );

// -- Custom Bar OHLC (for Short Logic) --
firstBar = Day() != Ref( Day(), -1 );
ORbHigh = ValueWhen( firstBar, High );
ORbLow = ValueWhen( firstBar, Low );
ORbCenter = ValueWhen( firstBar, ( ORbHigh + ORbLow ) / 2 );
tr = ATR( 5 * Periods );
ORBAtr = ValueWhen( firstBar, tr );
ORBAtr = IIf( ORBAtr == 0, ATR( 36 ), ORBAtr );
s_box_size = round( Max( 1, Max( boxSizePercent * ORbCenter / 100, boxSizeORbFactor * ORBAtr ) / 0.5 ) ) * 0.5;
myVar += ", box = " + ( s_box_size );

rH = round( H / s_box_size );
myH = s_box_size * IIf( rH < H / s_box_size, rH + 1, rH );
rL = round( L / s_box_size );
myL = s_box_size * IIf( rL > L / s_box_size, rL - 1, rL );
rO = round( O / s_box_size );
myO = s_box_size * IIf( rO > O / s_box_size, rO - 1, rO );
rC = round( C / s_box_size );
myC = s_box_size * IIf( rC > C / s_box_size, rC - 1, rC );

myC = IIf( myH - C < s_box_size / 3, myH, myC );
myC = IIf( C - myL < s_box_size / 3, myL, myC );

// -- Short Logic Indicators --
Vw_Short 		= KAMA( C, Periods_Short );
Vw_Short_1 		= Ref( Vw_Short, -1 );
top_Short = HMA( myH, ceil( Periods_Short / 2 ) );
bottom_Short = HMA( myL, ceil( Periods_Short / 2 ) );
Lr_Short = ( top_Short + bottom_Short ) / 2;
buffer_line_up	= ( round( Vw_Short_1 / s_box_size ) + 5 ) * s_box_size;
buffer_line_down = ( round( Vw_Short_1 / s_box_size ) - 5 ) * s_box_size;
No_Above_MA 	= BarsSince( C < Vw_Short_1 );
candle_size 	= ( myH - myL ) / s_box_size;

// -- Long Logic Indicators --
Vw_H 		= myround( VWMA2( myH, Periods ) );
Vw_L 		= myround( VWMA2( myL, Periods ) );
Vw_Long 	= ( Vw_H + Vw_L ) / 2;
top 		= Max( LinearReg( High, Periods ), LinearReg( High, ceil( Periods / 2 ) ) );
bottom 		= Min( LinearReg( Low , Periods ), LinearReg( Low , ceil( Periods / 2 ) ) );
Lr_Long 	= mybox( ( LinearReg( C, Periods ) + H + L ) / 3 );
Vw_Long_1 	= Ref( Vw_Long, -1 );
Lr_Long_1	= Ref( Lr_Long, -1 );

mode = Flip( Lr_Long > Vw_Long + boxSize, Lr_Long < Vw_Long - boxSize );
mode_s = Flip( Vw_Long > Vw_Short + s_box_size, Vw_Long < Vw_Short - s_box_size );
Sd = StDev( C, floor( Periods / 2 ) );

ATR_mul = 1.4;
ATR_mul = 1.4;
f = OptimizeNot( "F", 2.4, 1.0, 6, 0.1 );
ATR_f = ATR( floor( Periods / 2 ) );

up   	= Lr_Long + f * Max( ATR_f * ATR_mul, Sd );
down 	= Lr_Long - f * Min( ATR_f * ATR_mul, Sd );
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

// pp = Optimize( "pp", 15, 2, 30, 1 );
L_5 = myboxF( LLV( myL, 5 ) );
H_5 = myboxC( HHV( myH, 22 ) );
H_3 = myboxC( HHV( H, 13 ) );
H_3_1 = Ref( H_3, -1 );
H_5_1 = Ref( H_5, -1 );
L_5_1	= Ref( L_5, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
state =  0;
s_exit = ceil( Max( Vw_Short_1, H_3_1 ) / s_box_size ) * s_box_size;
b_exit = floor( Min(Vw_Long_1 - s_box_size, L_5) / s_box_size ) * s_box_size;
max_sl	= myRound( eqAtRisk / RoundLotSize );
short_state = 0; // State machine for short logic
long_state = 0; // State machine for buy logic
CapturedBreakoutHigh = CapturedBreakoutLow = BreakoutRange = CalculatedTriggerPrice = CalculatedStopLoss = 0;


_SECTION_END();

//----------------------------------------------------------------
// SECTION: TRADING LOGIC
//----------------------------------------------------------------
_SECTION_BEGIN( "Trade Execution" );

BuyCondition   =     Ref( mode, - 1 ) AND L > Lr_Long AND H > H_5_1 AND TradingZone AND WhiteBody;
ShortCondition = NOT Ref( mode, - 1 ) AND H < Lr_Long AND L < L_5_1 AND TradingZone;
InBuy = 0;
InShort = 0;

myVar += ", candlesize = " + candle_size;

for( i = 1; i < BarCount; i++ )
{
    // --- Ratchet buffer lines for short logic ---
    buffer_line_down[i] = IIf( C[i] > buffer_line_down[i - 1]   AND buffer_line_down[i] < buffer_line_down[i - 1], buffer_line_down[i - 1], buffer_line_down[i] );
    buffer_line_up[i]   = IIf( C[i] < buffer_line_up[i - 1] AND buffer_line_up[i]   > buffer_line_up[i - 1]  , buffer_line_up[i - 1]  , buffer_line_up[i] );

    // --- Long POSITION MANAGEMENT ---
    if( Buyflag )
    {
        exit[i] = exit[i - 1];

        if( O[i] > b_exit[i] )
        {
            exit[i] = Max( b_exit[i], exit[i - 1] );
        }
    }

    // Sell
    if( Buyflag AND C[i] < exit[i] )
    {
        Sell[i] = True;
        Buyflag = 0;
        long_state = 0;
    }

    // Sell
    if( Buyflag AND C[i] < exit[i] )
    {
        Sell[i] = True;
        Buyflag = 0;
        long_state = 0;
    }

    // --- LONG ENTRY LOGIC ---
    if( C[i] < buffer_line_down[i] OR C[i] < CalculatedStopLoss ) long_state = 0;

    if( long_state == 3 AND mode[i - 1] AND myL[i] > Vw_Long_1[i] AND candle_size[i] < 6 AND myC[i] > myO[i] AND NOT Buyflag AND H[i] > H_5_1[i] )
    {
        Buy[i] = 1;
        Buyflag = True;
        long_state = 4;
        exit[i] = b_exit[i] ;
    }
    else
        if( long_state == 2 AND( L[i] < CalculatedTriggerPrice OR L[i] < Vw_long_1[i] ) )
        {
            long_state = 3; // Low Toched
        }
        else
			if( long_state == 1 AND myH[i] > CapturedBreakoutHigh )
			{
				long_state = 2; // HIGH_TOUCHED
			}
			else
            if( long_state == 0 AND H[i] > Vw_long_1[i] AND candle_size[i] < 6 )
            {
                long_state = 1; // Armed state
                CapturedBreakoutHigh = myH[i];
                CapturedBreakoutLow = myL[i];
                BreakoutRange = CapturedBreakoutHigh - CapturedBreakoutLow;
                CalculatedTriggerPrice = CapturedBreakoutHigh - BreakoutRange;
                CalculatedStopLoss = CapturedBreakoutLow - BreakoutRange;
            }

    // --- SHORT POSITION MANAGEMENT ---
    if( Shortflag )
    {
        exit[i] = exit[i - 1];

        if( O[i] < Vw_Short_1[i] )
        {
            exit[i] = Min( s_exit[i], exit[i - 1] );
        }
    }

    // COVER (Exit Short Position)
    if( Shortflag AND H[i] > exit[i] )
    {
        Cover[i] = True;
        Shortflag = 0;
        short_state = 0; // Re-arm short state
        CoverPrice[i] = exit[i];
    }

    // --- SHORT ENTRY LOGIC ---
    if( C[i] > buffer_line_up[i] ) short_state = 0;

    if( short_state == 2 AND myC[i] < Vw_Short_1[i] AND candle_size[i] < 6 AND myC[i] < myO[i] AND NOT Shortflag AND NOT mode[i] )
    {
        Short[i] = 1;
        Shortflag = True;
        short_state = 3;
        exit[i] = s_exit[i];
    }
    else
        if( short_state == 1 AND Lr_Short[i] > Vw_Short_1[i] )
        {
            short_state = 2; // Pull back
        }
        else
            if( short_state == 0 AND myC[i] < buffer_line_down[i - 1] AND( Lr_Short[i] < Vw_Short_1[i] OR candle_size[i] > 5 ) )
            {
                short_state = 1; // Armed state
            }


    state[i] = long_state;
}
Cover = Cover OR Buy;

srisk = s_exit - C;
bRisk 	= C - exit;

exit = myRound( exit );
PT	 = myRound( PT );
max_lots = Min( floor( 500000 / ( RoundLotSize * C * 0.2 ) ), 20 );
bLots	= Min( max_lots, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Min( max_lots, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

myVar += ", CalculatedTriggerPrice = " + CalculatedTriggerPrice;

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

myVar += ", state = " + state;

_SECTION_END();

//----------------------------------------------------------------
// SECTION: PLOTTING
//----------------------------------------------------------------
_SECTION_BEGIN( "Chart Display" );

// -- Price and Exit Levels --
PlotOHLC( myO, myH, myL, myC, "", colorDefault, styleCandle );
Plot( IIf( exit, exit, Null ), "exit", colorBrown, styleStaircase | styleDashed, Null, Null, Null, -1 );

// -- Indicators from Short Logic --
Plot( buffer_line_up, "", ColorRGB( 68, 134, 238 ), styleThick );
Plot( buffer_line_down, "", ColorRGB( 205, 51, 51 ), styleThick );
Plot( Vw_Short, "VW (Short)", colorBlue, styleThick | styleNoLabel );
Plot( Lr_Short, "LR (Short)", colorGrey40, styleThick, Null, Null, Null, 2 );

Plot( Vw_Long , "", IIf(mode, colorGreen, colorPink ), ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( Lr_Long_1, "", colorGrey40, ParamStyle( "Style" ) | styleThick, Null, Null, Null, 2 );

// -- Trade Shapes --
PlotShapes( shapeSmallUpTriangle * Buy, colorGreen, 0, myH, -10 );
PlotShapes( shapeSmallDownTriangle * Sell, colorPaleGreen, 0, myH, 10 );
PlotShapes( shapeSmallDownTriangle * Short, colorRed, 0, myL, 10 );
PlotShapes( shapeSmallUpTriangle * Cover, colorPink, 0, myL, -10 );
_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );

_SECTION_END();
//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTSTK";
SYS  = "FUT";
// #include<AlgoFoxAuto/AlgoFoxAuto.afl>
