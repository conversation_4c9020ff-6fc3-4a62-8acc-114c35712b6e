//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi <PERSON>t=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>
// OptimizerSetEngine( "cmae" );

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

//=============================DISPLAY PARAMS======================================
atrMultiplier   = Param( "ATR_Multiplier", 2.5, 1, 10, 0.1 ); // BNF - 4.7
atrMultiplier 	= IIf( StrFind( Name(), "BANKNIFTY" ), Param( "ATR_Multiplier2", 1.3, 1, 10, 0.1 ), atrMultiplier ); // 2.5
atrPeriod       = Param( "ATR_Period", 5, 5, 10, 1 );
equityAtRisk    = Param( "Risk", 25000, 20000, 100000, 5000 );
//=================================================================================

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

vlots = V / RoundLotSize;
vCheck = vlots / MA( Ref( vlots, -1 ), OptimizeNot( "vp", 23, 1, 50, 1 ) );

BuyP 	= myboxC( IIf( firstBarOfTheDay, fch, Max( fch, HHV( Ref( H, -1 ), Max( BarsSince( firstBarOfTheDay ) - 1, 1 ) ) ) ) + boxSize );
ShortP	= myboxF( IIf( firstBarOfTheDay, fcl, Min( fcl, LLV( Ref( L, -1 ), Max( BarsSince( firstBarOfTheDay ) - 1, 1 ) ) ) ) - boxSize );
Short	= C < ShortP AND C < O AND !firstBarOfTheDay AND TradingZone AND Ref( C, -1 ) >= Ref( ShortP, -1 ) AND vCheck > 2.5;
Cover 	= H > myboxC( fch + 2 * boxSize ) OR !TradingZone OR( Ref( ThreeWhiteSoldiers, -1 ) AND !Ref( firstBarOfTheDay, -1 ) );
Short	= ExRem( Short, Cover );
Cover	= ExRem( Cover, Short );

Buy		= C > BuyP AND !LongWick AND C > O AND LLV( L, BarsSince( firstBarOfTheDay ) + 1 ) >= ValueWhen(firstBarOfTheDay ,ShortP) AND !firstBarOfTheDay AND TradingZone AND Ref( C, -1 ) <= Ref( BuyP, -1 ) AND vCheck > 2;
Sell 	= L < myboxF( fcl -  boxSize ) OR !TradingZone OR( Ref( ThreeBlackCrows, -1 ) AND !Ref( firstBarOfTheDay, -1 ) );
Buy		= ExRem( Buy, Sell );
Sell	= ExRem( Sell, Buy );

ShortPrice	= C; // myboxF( Min( fcl, LLV( Ref( L, -1 ), Max( BarsSince( firstBarOfTheDay ) - 1, 1 ) ) ) - boxSize );
CoverPrice	= IIf( !TradingZone OR Ref( ThreeWhiteSoldiers, -1 ), O, myboxC( fch + 2 * boxSize ) );
BuyPrice	= C; // myboxC( Max( fch, HHV( Ref( H, -1 ), Max( BarsSince( firstBarOfTheDay ) - 1, 1 ) ) ) + boxSize );
SellPrice	= IIf( !TradingZone OR Ref( ThreeBlackCrows, -1 ) , O, myboxF( fcl -  boxSize ) );

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -25 );
Plot( IIf( TradingZone, BuyP, Null ), "CP", colorBlue, styleLine | styleDashed );
Plot( IIf( TradingZone, ShortP, Null ), " SP", colorBlue, styleLine | styleDashed );

#include <Alert.afl>

bRisk = BuyPrice - myboxF( fcl -  boxSize );
bLots	= floor( Max( 0, floor( equityAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) ) );

sRisk = myboxC( fch + 2 * boxSize ) - ShortPrice;
sLots	= floor( Max( 0, floor( equityAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) ) );

max_lots = 10;
bLots	= round( Min( max_lots, bLots ) );
sLots	= round( Min( max_lots, sLots ) );
SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );

myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", SellPrice, CoverPrice );

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), vCheck
                       ) );

_SECTION_END();

//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTSTK";

if( StrFind( Name(), "NIFTY" ) )
{
    INSTR  = "FUTIDX";
}

