//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi Arafat=====================
//########################################################################
#include <Common.afl>
// OptimizerSetEngine( "cmae" );
SetOption( "DisableRuinStop", True );

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

//=============================DISPLAY PARAMS======================================
equityAtRisk    = 3000; // Param( "Risk", 10000, 20000, 100000, 5000 );
Target		= 3.5 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
//=================================================================================

tn = TimeNum();
dn = DateNum();
newDay = dn != Ref( dn, -1 );

tt1 = OptimizeNot( "T1", 8, 2, 54, 1 );
tt2 = tt1 + Optimize( "T1", 24, 2, 45, 1 );

T1 = TA[tt1]; // 101400;
T2 = TA[tt2]; //110000;

TradingZone = ( tn > T2 AND tn <= 150000 );
boxSize = ceil( MA( C, 100 ) / 100 / 10);

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;
Buyflag = 0;

vlots = V / RLS;
vlotsMA = round(MA( Ref( vlots, -1 ), Param( "vp", 15, 1, 50, 1 ) ));
vCheck = vlots / vlotsMA;

ShortP = BuyP = brisk = srisk = 0;
// define start/end hours in TimeNum format
StartTime = T1;
Endtime = T2;
StartBar = tn == StartTime;
EndBar = tn == Endtime;
Hat12 = ValueWhen( EndBar, HighestSince( StartBar, High ) );
Lat12 = ValueWhen( EndBar, LowestSince( StartBar, Low ) );

hBar = lBar = StartBar;
Trend = IIf( BarsSinceCompare( H, "==", Hat12 ) > BarsSinceCompare( L, "==", Lat12 ), -1, 1 );
Mid = myBox( ( ( Hat12 + Lat12 ) ) / 2 );
diff = ( ( Hat12 - Lat12 ) * Optimize( "D", 1.2, 0.5, 1.5, 0.1 ) ) / 2;

myVar += ", check = " + Valuewhen(EndBar, round(diff / ATR(tt1) * 10)/10);

LB = Max( 0.1, myboxF( Mid - diff ) );

myVar += ", vlotsMA = " + vlotsMA;
RAWS = LowestSince( EndBar, C ) < LB;

CP = TimeFrameGetPrice( "C", inDaily, 1, expandFirst );

LVC = ValueWhen(vCheck > 2, L);

lotsCheck = vlotsMA >= IIf( StrFind( Name(), "NIFTY" ), 50, 10 );

myVar += ", bs = " + boxSize;

Short = L <= LB AND H > LB AND Ref( L, -1 ) > LB;
Short = Short AND TradingZone AND lotsCheck AND vCheck >= 0.4 AND L > 6;
//Short = Short AND Sum( Short, BarsSince( newDay ) + 1 ) <= 1;
Cover = tn >= 151800 OR H > Mid + boxSize  OR L < 6;
ShortPrice = LB;
CoverPrice = IIf( !TradingZone, C, IIf( H > Mid + boxSize, Mid + boxSize, IIf(L < 6, 6, C )) );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

bkcolor = LastValue( IIf( Trend > 0, ColorBlend( colorCustom9, colorWhite ), IIf( Trend < 0, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );
Plot( IIf( TradingZone, Hat12,  Null ), "Hat12", colorGreen, styleDashed );
Plot( IIf( TradingZone, Mid, Null ), "Mid"  , colorBlack, styleDashed );
Plot( IIf( TradingZone, LB,  Null ), "LB", colorRed, styleDashed );
PlotShapes( IIf( vCheck > 3, shapeDigit3, IIf( vCheck > 2, shapeDigit2, shapeNone ) ), colorBlack, 0, L, -25 );

#include <Alert.afl>

sRisk = Mid - LB;
sLots	= Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) );

max_lots = 10;
bLots	= 0;
sLots	= Min( max_lots, sLots );
SetPositionSize( sLots*RLS, spsShares );

myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", Mid, Mid );

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{DATE}}, "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), vCheck
                       ) );

_SECTION_END();