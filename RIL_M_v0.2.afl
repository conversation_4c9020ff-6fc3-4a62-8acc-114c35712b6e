/*****************************************************************
** Strategy: Merged Simple Line Momentum & Linear Regression
** Version: 1.0
**
** --- LOGIC ---
** Long:  From "Linear Regression" by Trading Tuitions.
** Enters long based on a filter using Linear Regression,
** VWMA, and various price action conditions.
**
** Short: From "Simple Line Momentum v1.3".
** Enters short on two down-moves of a buffer line
** without a corresponding up-move in the opposite buffer.
******************************************************************/

//----------------------------------------------------------------
// SECTION: INCLUDES & SETTINGS
//----------------------------------------------------------------
#include <Common.afl>
// OptimizerSetEngine( "cmae" );

//----------------------------------------------------------------
// SECTION: PARAMETERS
//----------------------------------------------------------------
_SECTION_BEGIN( "Parameters" );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );
ShortTradingZone        = ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= 150000 );

// -- General Params --
eqAtRisk = Param( "Risk", 25000, 20000, 45000, 5000 );

// -- Long Strategy Params --
Periods = Param( "Periods (Long)", 122, 100, 150, 1 );
f_Stdev = Param( "F StDev (Long)", 1, 1, 3, 0.1 );

// -- Short Strategy Params --
boxSizePercent = Param( "Box %", 0.2, 0.1, 1, 0.1 );
boxSizeORbFactor = Param( "ORB ATR factor", 1.3, 0.2, 3, 0.1 );
p_no_above = Param( "No Above MA (Short)", 85, 5, 200, 5 );

_SECTION_END();

//----------------------------------------------------------------
// SECTION: CUSTOM BAR & INDICATORS
//----------------------------------------------------------------
_SECTION_BEGIN( "Calculations" );

// -- Custom Bar OHLC (for Short Logic) --
firstBar = Day() != Ref( Day(), -1 );
ORbHigh = ValueWhen( firstBar, High );
ORbLow = ValueWhen( firstBar, Low );
ORbCenter = ValueWhen( firstBar, ( ORbHigh + ORbLow ) / 2 );
tr = ATR( 5 * Periods );
ORBAtr = ValueWhen( firstBar, tr );
ORBAtr = IIf( ORBAtr == 0, ATR( 36 ), ORBAtr );
s_box_size = round( Max( 1, Max( boxSizePercent * ORbCenter / 100, boxSizeORbFactor * ORBAtr ) / 3 ) ) * 3;
myVar += ", box = " + ( s_box_size );

rH = round( H / s_box_size );
myH = s_box_size * IIf( rH < H / s_box_size, rH + 1, rH );
rL = round( L / s_box_size );
myL = s_box_size * IIf( rL > L / s_box_size, rL - 1, rL );
rO = round( O / s_box_size );
myO = s_box_size * IIf( rO > O / s_box_size, rO - 1, rO );
rC = round( C / s_box_size );
myC = s_box_size * IIf( rC > C / s_box_size, rC - 1, rC );

myC = IIf( myH - C < s_box_size / 3, myH, myC );
myC = IIf( C - myL < s_box_size / 3, myL, myC );

// -- Short Logic Indicators --
Vw_Short 		= KAMA( myC, Periods );
Vw_Short_1 		= Ref( Vw_Short, -1 );
buffer_line_up	= ( round( Vw_Short_1 / s_box_size ) + 5 ) * s_box_size;
buffer_line_down = ( round( Vw_Short_1 / s_box_size ) - 5 ) * s_box_size;
No_Above_MA 	= BarsSince( C < Vw_Short_1 );
candle_size 	= ( myH - myL ) / s_box_size;

// -- Long Logic Indicators --
Vw_Long 	= myround( VWMA2( C, Periods ) );
Lr_Long 	= mybox( LinearReg( C, 140 ) );
Vw_Long_1 	= Ref( Vw_Long, -1 );
Lr_Long_1 	= Ref( Lr_Long, -1 );
mode 		= Flip( Lr_Long > Vw_Long + boxSize, Lr_Long < Vw_Long - boxSize );
H_15 		= myboxC( HHV( myH, 20 ) );
H_15_1 		= Ref( H_15, -1 );
Sd 			= myBox( StDev( C, Periods ) );
four_sd_down	= Lr_Long - f_Stdev * Min( tr, Sd );
four_sd_down_1	= Ref( four_sd_down, -1 );

BuyCondition   =     mode AND L > Lr_Long_1 AND H > H_15_1 AND TradingZone AND WhiteBody;
_SECTION_END();

//----------------------------------------------------------------
// SECTION: TRADING LOGIC
//----------------------------------------------------------------
_SECTION_BEGIN( "Trade Execution" );

Buy = Sell = Short = Cover = 0;
Buyflag = 0;
Shortflag = 0;
short_state = 0; // State machine for short logic
exit = Null;

// --- POSITION SIZING ---
buyExit = IIf( four_sd_down_1 < Vw_Long_1, Vw_Short_1, Max( Vw_Short_1, four_sd_down_1 ) );
bRisk = C - Lr_Long_1;
srisk = floor( Vw_Short_1 / s_box_size ) * s_box_size - C;
bLots = Min( floor( eqAtRisk / ( RoundLotSize * bRisk ) ), 6 );
sLots = Min( floor( eqAtRisk / ( RoundLotSize * sRisk ) ), 6 );

for( i = 2; i < BarCount; i++ )
{
    // --- Ratchet buffer lines for short logic ---
    buffer_line_down[i] = IIf( C[i] > buffer_line_down[i - 1]   AND buffer_line_down[i] < buffer_line_down[i - 1], buffer_line_down[i - 1], buffer_line_down[i] );
    buffer_line_up[i]   = IIf( C[i] < buffer_line_up[i - 1] AND buffer_line_up[i]   > buffer_line_up[i - 1]  , buffer_line_up[i - 1]  , buffer_line_up[i] );

    // --- LONG POSITION MANAGEMENT ---
    if( Buyflag )
    {
        InBuy[i] = 1;
        t_exit =  buyExit[i];

        if( O[i] > t_exit )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        exit[i] = ceil( exit[i] / boxSize[i] ) * boxSize[i];
    }

    // --- SHORT POSITION MANAGEMENT ---
    if( Shortflag )
    {
        exit[i] = exit[i - 1];

        if( O[i] < Vw_Short_1[i] )
        {
            exit[i] = Min( Vw_Short_1[i], exit[i - 1] );
        }

        exit[i] = ceil( exit[i] / s_box_size[i] ) * s_box_size[i];
    }

    // --- EXIT TRIGGERS ---
    // SELL (Exit Long Position)
    if( Buyflag AND L[i] < exit[i] AND O[i] > exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Buyflag AND C[i - 1] < exit[i - 1] AND O[i - 1] < exit[i - 1] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = O[i];
    }

    // COVER (Exit Short Position)
    if( Shortflag AND C[i] > exit[i] )
    {
        Cover[i] = True;
        Shortflag = 0;
        short_state = 1; // Re-arm short state
    }

    // --- ENTRY TRIGGERS
    if( NOT Buyflag AND NOT Shortflag AND BuyCondition[i - 1] AND H[i] > H[i - 1] AND bLots[i] > 2 )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= buyExit[i];
        Buyflag 	= 1;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i] 	= BuyP - exit[i];
    }

    // --- SHORT ENTRY LOGIC ---
    if( C[i] > buffer_line_up[i] ) short_state = 0;

    if( short_state == 1 AND myC[i] < buffer_line_down[i - 1] AND myH[i] < Vw_Short_1[i] AND candle_size[i] < 6 AND myC[i] < myO[i] AND NOT Shortflag  AND sLots[i] > 1 AND ShortTradingZone[i])
    {
        Short[i] = 1;
        Shortflag = True;
        short_state = 3;
        exit[i] = ceil( Vw_Short_1[i] / s_box_size[i] ) * s_box_size[i];
    }
    else
        if( short_state == 0 AND No_Above_MA[i - 1] > p_no_above AND myC[i] < Vw_Short_1[i] )
        {
            short_state = 1; // Armed state
        }

    state[i] = short_state;
}

PositionSize = IIf( Buy, bLots, IIf( Short, sLots, 0 ) ) * RoundLotSize;
SetPositionSize( PositionSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );

_SECTION_END();

//----------------------------------------------------------------
// SECTION: PLOTTING
//----------------------------------------------------------------
_SECTION_BEGIN( "Chart Display" );

// -- Background Color --
bkcolor = LastValue( IIf( mode, ColorBlend( colorCustom9, colorWhite ), ColorBlend( colorCustom12, colorWhite ) ) );
SetChartBkColor( bkcolor );

// -- Price and Exit Levels --
PlotOHLC( myO, myH, myL, myC, "", colorDefault, styleCandle );
Plot( IIf( exit, exit, Null ), "exit", colorBrown, styleStaircase | styleDashed, Null, Null, Null, -1 );

// -- Indicators from Short Logic --
Plot( buffer_line_up, "", ColorRGB( 68, 134, 238 ) );
Plot( buffer_line_down, "", ColorRGB( 205, 51, 51 ) );
Plot( Lr_Long_1, "", colorGrey40 );
Plot( Vw_Short, "VWMA (Short)", IIf( mode, colorGreen, colorPink ), styleThick | styleNoLabel );

// -- Trade Shapes --
PlotShapes( shapeSmallUpTriangle * Buy, colorGreen, 0, myL, -10 );
PlotShapes( shapeSmallDownTriangle * Sell, colorDarkGreen, 0, myH, 10 );
PlotShapes( shapeSmallDownTriangle * Short, colorRed, 0, myH, 10 );
PlotShapes( shapeSmallUpTriangle * Cover, colorDarkRed, 0, myL, -10 );

// -- Title --
_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "Open %0.2f, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 )
                       ) );

_SECTION_END();