_SECTION_BEGIN( "Price" );
SetChartOptions( 0, chartShowArrows | chartShowDates );
Plot( C, "Close", ParamColor( "Color", colorDefault ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );
_SECTION_END();

myVar = "";

//ORB

_SECTION_BEGIN( "ORB" );

newday = Day() != Ref( Day(), -1 ) ; //Identifies the beginning of new day

DH = HighestSince( newday, H ); // returns the highest High of the day till new day
DL = LowestSince( newday, L ); //returns the lowest low of the day till newday

vCheck = V / MA( Ref( V, -1 ), 35 ) > 3;

IBH = ValueWhen( ( TimeNum() == 101500 ), TimeFrameGetPrice( "H", inHourly, -1 ) ) ;
IBL = ValueWhen( ( TimeNum() == 101500 ), TimeFrameGetPrice( "L", inHourly, -1 ) ) ;

Plot( IBL, "IBL,", colorRed );
Plot( IBH, "IBH", colorGreen );

//Timing functions

//Entry time should be greater than 10:15 and 2:30 PM
Starttime = TimeNum() > 101500 ;
endtime = TimeNum() < 143000 ;
EODsquareoff = TimeNum() == 151500 ;

//Define the strategy rules

wickpercent = Optimize( "WickPercent", 0.15, 0.05, 0.4, 0.05 );
SmallBuyWick = False OR( ( C - L ) < ( ( H - L ) * wickpercent ) );
SmallSellWick = False OR( ( H - C ) < ( ( H - L ) * wickpercent ) );
Buy = Ref( C, -1 ) > Ref( IBH, -1 ) AND starttime AND endtime AND Ref( SmallSellWick, -1 );
Short = Ref( C, -1 ) < Ref( IBL, -1 ) AND starttime AND endtime AND Ref( SmallBuyWick, -1 );
Buy = ExRem( Buy, newday );
Short = ExRem( Short, newday );

BuyPrice = ValueWhen( Buy, Ref( H, -1 ) );
ShortPrice = ValueWhen( Short, Ref( L, -1 ) );

//BuySL = ValueWhen(Buy,Ref(L,-1));
//ShortSL = ValueWhen(Short,Ref(H,-1));
SLprc = Optimize( "SLprc", 0.01, 0.001, 0.02, 0.001 );
BuySL = BuyPrice * ( 1 - SLprc );
ShortSL = ShortPrice * ( 1 + SLprc );

Sell = ( L < BuySL ) OR EODsquareoff ;
Sell = ExRem( Sell, Buy );
Cover = ( H > ShortSL ) OR EODsquareoff ;
Cover = ExRem( Cover, Short );

//Buy = ExRem(Buy,sell);
//Short = ExRem(Short,Cover);
//Sell = ExRem(Sell,Buy);
//Cover = ExRem(Cover,Short);

Plot( Ref( BuySL, 1 ), "BuySL", colorWhite );
Plot( Ref( ShortSL, 1 ), "ShortSL", colorYellow );

SellPrice = IIf( EODsquareoff, C, BuySL );
CoverPrice = IIf( EODsquareoff, C, ShortSL );

SetPositionSize( 25, spsShares );

PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorBlue, 0, L, Offset = -40 );
PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorBlue, 0, L, Offset = -50 );
PlotShapes( IIf( Buy, shapeHollowUpArrow, shapeNone ), colorWhite, 0, L, Offset = -45 );
PlotShapes( IIf( Short, shapeSquare, shapeNone ), colorYellow, 0, H, Offset = 40 );
PlotShapes( IIf( Short, shapeSquare, shapeNone ), colorYellow, 0, H, Offset = 50 );
PlotShapes( IIf( Short, shapeHollowDownArrow, shapeNone ), colorRed, 0, H, Offset = -45 );

PlotShapes( IIf( sell, shapeStar, shapeNone ), colorred, 0, H, 20 );
PlotShapes( IIf( Cover, shapeStar, shapeNone ), colorBlue, 0, L, -20 );


_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );
