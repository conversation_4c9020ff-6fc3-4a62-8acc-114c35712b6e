RefBarTime             = 092600; // Exit all trades
OL = OI/RoundLotSize;
DN              = DateNum();
firstBarOfTheDay = DN != Ref(DN, -1);

TimeFrameSet(inDaily); 
WMA14_oi = WMA(OI, 14);
TimeFrameRestore(); 
OI_d0    = TimeFrameGetPrice("I", inDaily)/RoundLotSize; 
OI_d     = TimeFrameGetPrice("I", inDaily, -1)/RoundLotSize; 
OI_d1    = TimeFrameGetPrice("I", inDaily, -2)/RoundLotSize; 
WMA14_oi = TimeFrameExpand(WMA14_oi, inDaily, 0); 

RefBarTime             = 091400; // Exit all trades 

_SECTION_BEGIN("Open Interest");
//Plot( OI_d/OI_d1, "Rel D1", colorYellow, styleLine );
//Plot( OI_d/WMA14_oi, "Rel 14d", colorWhite, styleLine );
//Plot( OI, "OI", colorYellow, styleLine);
rel_OI   = (OI/WMA14_oi - 1)*100;
rel_OI_O = (OL/OI_d - 1)*100;
rel_O_OI = OL/ValueWhen(TimeNum()<=RefBarTime, OL) - 1;
//Plot(rel_O_OI*100, "Rel Open", IIf(rel_O_OI > 0.02, colorLime, IIf(rel_O_OI < -0.02, colorRed, colorWhite)), styleHistogram );

//myVar = "Ref OI = " + OL/OI_d + ", OI_d= " + OI_d;
Plot( C/ValueWhen(firstBarOfTheDay, C), "C", colorWhite, styleLine );
Plot( OL/ValueWhen(firstBarOfTheDay, OL), "OL", IIf(OL/ValueWhen(firstBarOfTheDay, OL) > 1.02, colorBlue, colorOrange), styleLine );
//Plot( OL/OI_d, "Ref OI", IIf(OL/OI_d > 2, colorBlue, colorOrange), styleLine | styleOwnScale );

m_fill  = 0.01; 
stMA = round(WMA(Ref(OL, -1), 7));

m_PL    = stMA*(1 - m_fill); 
m_PH    = stMA*(1 + m_fill); 

m_AH    = LLV(m_PH, 1 + Nz(BarsSince(stMA < Ref(stMA, -1)), 1));
m_AL    = HHV(m_PL, 1 + Nz(BarsSince(stMA > Ref(stMA, -1)), 1));

m_Above = stMA > Ref(m_AH, -1);
m_Below = stMA < Ref(m_AL, -1);

m_Above = Flip(m_Above, m_Below);
m_Below = Flip(m_Below, m_Above);


stMA = WMA(Ref(C, -1), 7);

m_PL    = stMA*(1 - m_fill); 
m_PH    = stMA*(1 + m_fill); 

m_AH    = LLV(m_PH, 1 + Nz(BarsSince(stMA < Ref(stMA, -1)), 1));
m_AL    = HHV(m_PL, 1 + Nz(BarsSince(stMA > Ref(stMA, -1)), 1));

m_Above = stMA > Ref(m_AH, -1);
m_Below = stMA < Ref(m_AL, -1);

m_Above = Flip(m_Above, m_Below);
m_Below = Flip(m_Below, m_Above);

//Plot( OI_d0, "OI_d0", colorWhite, styleLine );
//Plot( m_AH, "m_AL", colorOrange, styleLine );
//Plot( stMA, "C", IIf(m_Above, colorLime, colorRed), styleLine | styleOwnScale );


_SECTION_END();
myVar = ", OL = " + OL;
//Plot((OI/ValueWhen(TimeNum() <= RefBarTime, OI)-1)*100, "Rel 10AM", colorOrange,styleHistogram);

_N( Title   = StrFormat( EncodeColor( colorWhite ) + myVar + ", " + EncodeColor( -1 ) + "{{VALUES}}" ) );