// Volume-Based Futures Trading System for Amibroker
// Inspired by TradingTuitions.com Volume Breakout Strategy
// Designed for futures trading (e.g., Nifty, Bank Nifty)
// Uses high-volume candles for breakout signals

_SECTION_BEGIN( "Volume Breakout Futures Trading System" );

// Chart Styling
Plot( C, "Price", IIf( C > O, colorPaleBlue, IIf( C <= O, colorOrange, colorLightGrey ) ), 64 | styleNoTitle, 0, 0, 0, 0 );

// Parameters
VolLookback = Param( "Volume Lookback Period", 16, 10, 50, 1 ); // Period for average volume
VolMultiplier = Param( "Volume Multiplier", 6, 3, 8, 0.2 ); // Volume threshold multiplier
TargetPercent = Param( "Target %", 0.4, 0.1, 5, 0.1 ) / 100; // Target as percentage of entry price
StartTime = ParamTime( "Start Time", "09:15:00" ); // Trading start time
EndTime = ParamTime( "End Time", "15:00:00" ); // Trading end time

// Volume and Price Calculations
AvgVol = MA( Ref( V, -1 ), VolLookback ); // Average volume over lookback period
HighVol = V > AvgVol * VolMultiplier; // High volume condition
HighLowDiff = High - Low;
boxSize = Max( 1, round( Percentile( HighLowDiff, VolLookback, 95 ) * 0.25 ) );
BigCandle = HighLowDiff > boxSize * 3;
BigCandle_1 = Ref( BigCandle, -1 );

TriggerCandle = HighVol AND BigCandle;
TriggerCandle_1 = Ref( TriggerCandle, -1 );
TriggerCandle_2 = Ref( TriggerCandle, -2 );
PrevHigh = ValueWhen( TriggerCandle_1, H ) + boxSize ; // Previous high
PrevLow  = ValueWhen( TriggerCandle_1, L ) - boxSize; // Previous low
DN = DateNum();
NewDay = DN != Ref( DN, -1 ); // Identify new day

// Trading Conditions
Buy   = C > PrevHigh AND Ref( C, -1 ) < PrevHigh AND TimeNum() >= StartTime AND TimeNum() <= EndTime AND BarsSince( TriggerCandle ) < BarsSince( NewDay ) AND !HighVol;
Sell  = L < ValueWhen( HighVol, L ) - boxSize  OR TimeNum() > EndTime;
Short = C < PrevLow AND Ref( C, -1 ) > PrevLow AND TimeNum() >= StartTime AND TimeNum() <= EndTime AND BarsSince( TriggerCandle ) < BarsSince( NewDay ) AND !HighVol;
Cover = H > ValueWhen( HighVol, H ) + boxSize  OR TimeNum() > EndTime; // Exit on high volume, price break above, or end time

// Remove Excessive Signals
Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

eqAtRisk = 25000;
bRisk = round( IIf( PrevLow < C, C - PrevLow, C - L ) );
bLots	= Min( 5, Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) ) );
sRisk = round( IIf( PrevHigh > C, PrevHigh - C, H - C ) );
sLots	= Min( 5, Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) ) );
printf( "bLots = %g", bLots );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );

// Entry and Exit Prices
BuyPrice   = C;
SellPrice  = PrevLow;
ShortPrice = C;
CoverPrice = PrevHigh;

// Stop Loss and Target
SellTarget = BuyPrice * ( 1 + TargetPercent ); // Target price for long
CoverTarget = ValueWhen( Short, ShortPrice ) * ( 1 - TargetPercent ); // Target price for short
ApplyStop( stopTypeProfit, stopModePoint, IIf( Buy, SellTarget - BuyPrice, ShortPrice - CoverTarget ), 1 );

// Plot Previous Day High/Low
Plot( PrevHigh, "Prev Day High", colorGrey50, styleStaircase | styleDashed );
Plot( PrevLow, "Prev Day Low", colorGrey50, styleStaircase | styleDashed );

Plot( CoverTarget, "CT", colorBlue );

// Plot Volume
// Plot(V, "Volume", colorGrey50, styleHistogram | styleOwnScale, 0, 1000000);
// Plot(AvgVol * VolMultiplier, "Volume Threshold", colorBlack, styleLine | styleOwnScale, 0, 1000000);
PlotShapes( IIf( HighVol, shapeDigit5, shapeNone ), colorBlack, 0, L, -25 );
PlotShapes( IIf( BigCandle, shapeStar, shapeNone ), colorBlack, 0, L, -40 );

// Plot Buy/Sell Signals
PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorGreen, 0, L, Offset = -40 );
PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorLime, 0, L, Offset = -50 );
PlotShapes( IIf( Buy, shapeUpArrow, shapeNone ), colorWhite, 0, L, Offset = -45 );
PlotShapes( IIf( Sell, shapeSquare, shapeNone ), colorRed, 0, H, Offset = 40 );
PlotShapes( IIf( Sell, shapeSquare, shapeNone ), colorOrange, 0, H, Offset = 50 );
PlotShapes( IIf( Sell, shapeDownArrow, shapeNone ), colorWhite, 0, H, Offset = -45 );
PlotShapes( IIf( Short, shapeSquare, shapeNone ), colorRed, 0, H, Offset = 60 );
PlotShapes( IIf( Short, shapeSquare, shapeNone ), colorOrange, 0, H, Offset = 70 );
PlotShapes( IIf( Short, shapeDownArrow, shapeNone ), colorWhite, 0, H, Offset = -65 );
PlotShapes( IIf( Cover, shapeSquare, shapeNone ), colorGreen, 0, L, Offset = -60 );
PlotShapes( IIf( Cover, shapeSquare, shapeNone ), colorLime, 0, L, Offset = -70 );
PlotShapes( IIf( Cover, shapeUpArrow, shapeNone ), colorWhite, 0, L, Offset = -65 );

_SECTION_END();