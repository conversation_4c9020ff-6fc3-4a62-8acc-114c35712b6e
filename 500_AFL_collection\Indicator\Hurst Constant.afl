//------------------------------------------------------------------------------
//
//  Formula Name:    Hurst <PERSON>
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-07-05 19:30:48
//  Origin:          
//  Keywords:        
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=65
//  Details URL:     http://www.amibroker.com/library/detail.php?id=65
//
//------------------------------------------------------------------------------
//
//  This indicator is an interesting study in chaos theory.
//
//  Just look up the title in any web search engine and start
//
//  reading.
//
//  This indicator is fails to calculate for some stocks. It
//
//  requires at least 600 hundred quotes to work.
//
//------------------------------------------------------------------------------

/*HURST formula*/
Bars = 600;
FR1 = Log(Sum(Abs( C - ref(C,-1)),Bars));
FR2 = Log(Sum(IIF(  (cum(1)/2 - Int(Cum(1)/2) ) == 0,Abs(C - ref(C,-2)  ),0),Bars));
FR3 = Log(Sum(IIF(  (cum(1)/3 - Int(Cum(1)/3) )  == 0,Abs(C-ref(c,-3)  ),0),Bars));
FR4 = Log(Sum(IIF(  (cum(1)/4 - Int(Cum(1)/4) ) == 0,Abs(C-ref(c,-4)  ),0),Bars));
FR5 = Log(Sum(IIF( (cum(1)/5 - Int(Cum(1)/5)  ) == 0,Abs( C - Ref(C,-5)  ),0),Bars));
FR6 = Log(Sum(IIF(  (cum(1)/6 - Int(Cum(1)/6) ) == 0,Abs( C - ref(C,-6)  ),0),Bars));
FR10 = Log(Sum(IIF(  (cum(1)/10 - Int(Cum(1)/10) ) == 0,Abs(C - ref(C,-10)  ),0),Bars));
FR20 = Log(Sum(IIF(  (cum(1)/20 - Int(Cum(1)/20) ) == 0,Abs(C - ref(C,-20) ),0),Bars));
FR30 = Log(Sum(IIF(  (cum(1)/30 - Int(Cum(1)/30) ) == 0,Abs(C - ref(C,-30)  ),0),Bars));
FR40 = Log(Sum(IIF(  (cum(1)/40 - Int(Cum(1)/40) ) == 0,Abs(C - ref(C,-40)  ),0),Bars));
FR50 = Log(Sum(IIF(  (cum(1)/50 - Int(Cum(1)/50) ) == 0,Abs( C - ref(C,-50) ),0),Bars));
FR60 = Log(Sum(IIF(  (cum(1)/60 - Int(Cum(1)/60) ) == 0,Abs(C - ref(C,-60)  ),0),Bars));
SOMXY = FR2*Log(2)+FR3*Log(3)+FR4*Log(4)+FR5*Log(5)+FR6*Log(6)+FR10*Log(10)+FR20*Log(20)+FR30*Log(30)+FR40*Log(40)+FR50*Log(50)+FR60*Log(60);
SOMX = Log(2)+Log(3)+Log(4)+Log(5)+Log(6)+Log(10)+Log(20)+Log(30)+Log(40)+ Log(50)+Log(60);
SOMY =  FR1+FR2+FR3+FR4+FR5+FR6+FR10+FR20+FR30+FR40+FR50+FR60;
SOMX2 = (Log(2)^2)+(Log(3)^2)+(Log(4)^2)+(Log(5)^2)+(Log(6)^2) +(Log(10)^2)+(Log(20)^2)+(Log(30)^2)+(Log(40)^2)+(Log(50)^2)+ (Log(60)^2);
HURST= 1+((12*SOMXY-SOMX*SOMY)/(12*SOMX2 - (SOMX^2)));
LIM= LastValue(Cum(HURST)/(Cum(1)-Bars-60));
Graph1 = HURST;
Grpah0 = LIM;
Graph2=0.5;
Graph2Style=5;
Graph2Color=5;
