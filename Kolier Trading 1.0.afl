//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi <PERSON>t=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>
#include <Options.afl>

// OptimizerSetEngine( "cmae" );

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

#include<KolierSignals.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );

_SECTION_END();


GfxSelectFont( "BOOK ANTIQUA", 10, 100 );
GfxSetBkMode( 1 );
GfxSetTextColor( colorWhite );
GfxSelectSolidBrush( colorDarkGrey );

GfxSelectPen( colorLightBlue, 1 ); // border color
pxHeight = Status( "pxchartheight" ) ;
xx = Status( "pxchartwidth" );
x = 20;
x2 = 105;
y = pxHeight;
GfxRoundRect( x, y - 45, x2, y - 10, 7, 7 ) ;
GfxTextOut( "CE: " + NumToStr( LastValue( strikeCE ), 1.0 ), x + 5, y - 45 );
GfxTextOut( "PE: " + NumToStr( LastValue( strikePE ), 1.0 ), x + 5, y - 30 );

#include<AlgoFoxAuto\AlgoFoxAutoOpt.afl>