//------------------------------------------------------------------------------
//
//  Formula Name:    Parabolic SAR in JScript
//  Author/Uploader: <PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2002-12-18 07:09:46
//  Origin:          <PERSON>
//  Keywords:        
//  Level:           medium
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=242
//  Details URL:     http://www.amibroker.com/library/detail.php?id=242
//
//------------------------------------------------------------------------------
//
//  This sample code shows parabolic SAR re-implemented
//
//  in JScript. Note that SAR is built-in function in AFL and
//
//  this code is provided as an example of AmiBroker scripting capabilities.
//
//------------------------------------------------------------------------------

/**********BEGIN Parabolic SAR Indicator****************/

EnableScript ( "jscript" );
psar = Low;

<%
AF = 0.02;                          //acceleration factor
Max = 0.2;                          //max acceleration

Close = VBArray ( AFL ( "close" ) ).toArray();
High   = VBArray ( AFL ( "high" ) ).toArray();
Low    = VBArray ( AFL ( "low" ) ).toArray();
psar  =  VBArray ( AFL ( "psar" ) ).toArray();

psar [ 0 ] = Close [ 0 ];           //initialize
long = 1;                           //assume long for initial conditions
af = AF;                            //init acelleration factor
ep = Low[ 0 ];                      //init extreme point
hp = High [ 0 ];
lp = Low [ 0 ];

for ( i=2; i<Close.length; i++ )
{
     if ( long )
     {
             psar [ i ] = psar [ i-1 ] + af * ( hp - psar [ i-1 ] );
     }
     else
     {
             psar [ i ] = psar [ i-1 ] + af * ( lp - psar [ i-1 ] );
     }

     reverse =  0;
     //check for reversal
     if ( long )
     {
         if ( Low [ i ] < psar [ i ]  )
         {
              long = 0;  reverse = 1;            //reverse position to short
              psar [ i ] =  hp;                  //sar is high point in prev trade
              lp = Low [ i ];
              af = AF;
         }
     }
     else
     {
         if ( High [ i ] > psar [ i ]  )
         {
              long = 1;    reverse = 1;        //reverse position to long
              psar [ i ] =  lp;
              hp = High [ i ];
              af = AF;
         }
     }

     if ( reverse == 0 )
     {
         if ( long )
         {
             if ( High [ i ] > hp ) { hp = High [ i ]; af += AF; af = Math.min ( af, Max ); }
             psar [ i ] = Math.min ( psar [ i ], Low [ i - 1 ], Low[ i-2 ] );
         }
         else
         {
             if ( Low [ i ] < lp )  { lp = Low [ i ]; af = af + AF; af = Math.min ( af, Max ); }
             psar [ i ] = Math.max ( psar [ i ], High [ i - 1 ], High[ i-2 ] );
         }
     }
}

AFL.Var ( "psar" ) = psar;
%>

Graph0 = Close;
Graph0Style = 64 +32 ;
//graph0Style = 128 +32 ;
Graph0BarColor=1;
Graph1 = psar;
Graph1Style = 8 + 16 + 32;
Graph1Color = 8;



Title=Name() + " - sar = "+WriteVal(psar);
/**********END Parabolic SAR Indicator****************/