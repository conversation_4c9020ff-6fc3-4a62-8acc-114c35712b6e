//------------------------------------------------------------------------------
//
//  Formula Name:    Fund Screener
//  Author/Uploader: <PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-10-13 12:58:30
//  Origin:          
//  Keywords:        funds, stocks, rate of change, rank
//  Level:           medium
//  Flags:           exploration
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=125
//  Details URL:     http://www.amibroker.com/library/detail.php?id=125
//
//------------------------------------------------------------------------------
//
//  Rank mutual funds [or securities] based on their rates of change (or
//  smoothed ROCs), and see how much time passed since buy/sell signals
//  generated by various systems.
//
//------------------------------------------------------------------------------

// FundScreaner by Dr. S.N.Berger & D.Rasnitsyn
// The Idea of this screaner is to identify the strongest securities (by their ROC), and also see how much time passed since the latest buy/sell signal generated by different trading systems for each of them.
// The columns contain:
// Weighted smoothed ROC (weight coefficients are configurable)
// average of Monthly, quartely, semi-anual, and anual ROCs (this score is used by FundX)
// 5 day smoothed ROC
// 10 day smoothed ROC
// 22 day (1 month) smoothed ROC
// 9 week (2 month) smoothed ROC
// 13 week -"-
// 26 week -"-
// The next columns show The number of days since the latest buy (positive) or sell (negative) signal generated by particular trading system:
// N/D - system based on Jaco MRI, Williams AC+, MACD, and Relative slope
// Dags [sorry if the name is misspelled] peaker
// Stochastics divergence
// Any other???
//
// Proposed usage:
// 1) Screen all monitored funds / securities
// 2) Sort by Weighted ROC, scroll down for a dosen, and replace minWeightedROC by the smallest acceptable value
// 3) Sort by CPR, and repeat the same.
// 4) If you want to put the down limit on 10 day or monthly ROC, do the same for these columns
// 5) Repeat exploration. Only the funds/stocks with ROCs more then defined values will be in the screen.
// 6) Check how many days passed since the alst buy/sell signal for the security. Probably it's not too late to rotate...
//
//
// The following sections defines filter criteria.
// Display only funds with ...

// Filter date: 10000 * (year - 1900) + 100 * month + day, so 2001-12-31 becomes 1011231 and 1995-12-31 becomes 951231
// < 0 means the latest available date; 2001-04-05 - 1010405
filterdate = -1;

//minRelStrengthSP =-1000; // relative strength over S&P500 >= this value
min10dayROC =-100;      // Minimum 10 day rate of change < [-1000 means better then S&P 500]
min22dayROC =-1000;      // Minimum 22 day rate of change < [-1000 means better then S&P 500]
min13weekROC = -1000;    // Minimum 13 week rate of change < [-1000 means better then S&P 500]
min9weekROC = -1000;     // Minimum 9 week rate of change < [-1000 means better then S&P 500]
minWeightedROC = -1000;  // Minimum weighted ROC !! FUNDS WITH EMPTY WEIGHTED ROC (due to lack of 26 week data) ARE ALWAYS INCLUDED !!
minCPR = -1000;          // Minimum CPR !! FUNDS WITH EMPTY WEIGHTED ROC (due to lack of 26 week data) ARE ALWAYS INCLUDED !!
    
// Smoothing factor (as EMA) for ROC parameters
// SET TO 1 if want to disable smoothing
//
ROC5DayEMA = 9;
ROC10DayEMA =9;
ROC22DayEMA = 9;
ROC9WeekEMA =9;
ROC13WeekEMA =9;
ROC26WeekEMA =9;

//
// WEIGHT coefficients for the weighted ROC calculation
//
weightROC5DayEMA = 10;
weightROC10DayEMA = 15;
weightROC22DayEMA = 15;
weightROC9WeekEMA = 10;
weightROC13WeekEMA = 5;
weightROC26WeekEMA = 1;

r5  = ema ( close, ROC5DayEMA);
r10 = ema ( close, ROC10DayEMA);
r22 = ema (close , ROC22DayEMA);
r9w = ema (close , ROC9WeekEMA);
r13w= ema (close, ROC13WeekEMA);
r13w = IIF (isEmpty (r13w), r9w, r13w);
r26w= ema (close, ROC26WeekEMA);
r26w = IIF (isEmpty (r26w), r13w, r26w);
r52w= ema (close, ROC26WeekEMA);
r52w = IIF (isEmpty (r52w), r26w, r52w);

numcolumns = 12;
column2 = ROC5d =  roc (r5 , 5 )  ;
column2name = "5 d %";      
column3 = ROC10d = roc (r10 , 10 ) ;
column3name = "10 d %";     
column4 = ROC22d = roc (r22, 22)  ;
column4name = "22 d %";     
column5 = ROC9w =  roc (r9w, 5*9) ;
column5name = "9 w %";      
column6 = ROC13w = roc (r13w, 5*13);
column6name = "13 w %";     
column7 = ROC26w = roc (r26w, 5*26);
column7name = "26 w % ";

ROC52w = roc (r52w, 5*52);

// Experimental: relation to S&P 500 10 day ROC
//column10 = RSSP = ROC10d * 100 / (ema (roc(foreign ("^SPC", "close") , 10 ), ROC10DayEMA));
//column10name = "RS S&P %";

column0 = weightedROC = (weightROC5DayEMA *ROC5d + weightROC10DayEMA *ROC10d + weightROC22DayEMA *ROC22d + weightROC9WeekEMA *ROC9w + weightROC13WeekEMA *ROC13w + weightROC26WeekEMA * ROC26w)/(weightROC5DayEMA + weightROC10DayEMA + weightROC22DayEMA + weightROC9WeekEMA + weightROC13WeekEMA + weightROC26WeekEMA);
column0name = "Wght ROC%";

column1 = fundXCPR =   (roc (c, 22) + roc (c, 13*5) +  roc (c, 26*5) + roc (c, 52*5))  / 4;
column1name = "CPR";

//
// Calculate S&P ROCs for filtering
//

r5sp   = roc (foreign ("^SPC", "close"), 5 )  ;
r10sp  = roc (foreign ("^SPC", "close"), 10 ) ;
r22sp  = roc (foreign ("^SPC", "close"), 22)  ;
r9wsp  = roc (foreign ("^SPC", "close"), 5*9) ;
r13wsp = roc (foreign ("^SPC", "close"), 5*13);
r26wsp = roc (foreign ("^SPC", "close"), 5*26);
r52wsp = roc (foreign ("^SPC", "close"), 5*52);


ROC5dSP  = ema (r5sp  , ROC5DayEMA)  ;
ROC10dSP = ema (r10sp , ROC10DayEMA)  ;
ROC22dSP = ema (r22sp , ROC22DayEMA)   ;
ROC9wSP  = ema (r9wsp , ROC9WeekEMA);
ROC13wSP = ema (r13wsp, ROC13WeekEMA);
ROC26wSP = ema (r26wsp, ROC26WeekEMA);
ROC52wSP = ema (r52wsp, ROC26WeekEMA);

min10dayROC  = IIF (min10dayROC  <= -1000, ROC10dSP, min10dayROC );
min22dayROC  = IIF (min22dayROC  <= -1000, ROC22dSP, min22dayROC );
min9weekROC  = IIF (min9weekROC  <= -1000, ROC9wSP , min9weekROC );
min13weekROC = IIF (min13weekROC <= -1000, ROC13wSP, min13weekROC);
minWeightedROC = IIF (minWeightedROC <= -1000, (weightROC5DayEMA *ROC5dSP + weightROC10DayEMA *ROC10dSP + weightROC22DayEMA *ROC22dSP + weightROC9WeekEMA *ROC9wSP + weightROC13WeekEMA *ROC13wSP + weightROC26WeekEMA * ROC26wSP)/(weightROC5DayEMA + weightROC10DayEMA + weightROC22DayEMA + weightROC9WeekEMA + weightROC13WeekEMA + weightROC26WeekEMA), minWeightedROC);
minCPR =  IIF (minCPR <= -1000, (r22sp + r13wsp +  r26wsp + r52wsp)  / 4, minCPR);

filter = IIF (filterdate <= 0, cum(1) == lastvalue (cum(1)), datenum() == filterdate) AND ROC10d >= min10dayROC AND ROC22d >= min22dayROC AND ROC9w >= min9weekROC AND ROC13w >= min13weekROC AND (isEmpty(weightedROC) OR weightedROC >= minWeightedROC) AND (isEmpty (fundXCPR) OR fundXCPR >= minCPR);

"CPR: " + writeval (fundXCPR) + ", S&P CPR: " + writeval (minCPR);

buy = filter;
sell = 0;
short =cover = 0;

// ======== Signals from different systems =====================

// 1. Nate/Dima system
/*
 * combination of Jaco MRI, Williams, and MACD
 */

// Adjustable criteria
moBuyCriteria = 60; // 70?
moSellCriteria = 20; // 30?
rocPeriod = 10;
jacoHold =20; // Hold period
exitOnOverbought  = 0;




// Jaco MRI
/* MRI developed by Jaco Jonker*/

mo=close/ref(close,1)*100 - rsi(14);
HV = hhv(mo,200);
LV = llv(mo,200);
AV = (HV+LV)/2;


jacoBuy = isTrue (mo > moBuyCriteria);
jacoBuy = hold (jacoBuy, jacoHold);
jacoSell = isTrue (mo < moSellCriteria);
jacoSell = hold (jacoSell, jacoHold);

// Williams AC+
outsidebar = outside();
insidebar = H <= Ref(H,-1) and L >= Ref(L,-1);
upbar = H > ref(H,-1) and L >= ref(L, -1);
downbar = L < ref(L,-1) and H <= ref(H,-1);
barcolor=iif(outsidebar, 1, 
               iif(downbar,   4, 
               iif(upbar,        5, 
               iif(insidebar,6, 0 ) ) ) );


var1=ma( A , 34);
var2=ma( A,5);
var3=var2-var1;
var4=var3-ma(var3,5);

wilBuy = var4 > 0;
wilSell = var4 < 0;

//  Graph0=var4;
//  graph0style=2+4;
//  Graph1=wilders(var4,5);
//  Graph1Style=5;
//  Graph0Barcolor=Barcolor;

// MACD
macdBuy = macd() > signal();
macdSell = macd() < signal();

//  MA (instead of Zig-Zag)
mac = ma (c, 10);
maBuy = close > mac AND ref (close > mac, -1); // AND mac >= ref (mac, -1);
maSell = close < mac;

/*Relative Slope*/
/*by Dimitris Tsokakis*/
K=EMA((H+L+C)/3,10);
S1=2*(K-REF(K,-1))/(K+REF(K,-1));
RS=100*EMA(S1,3);

rocDirBuy = RS > 0; // slope is up
rocDirSell = NOT rocDirBuy;

//
// BUY: Jaco MRI was in buy area recently (i.e. the fund was oversold); currently WIlliams AC+ and MACD are above their reference lines (probably should replace it by more sensitive slope of the oscilator as A.Elder recommends); price closes above 10 day MA, and Relative slope is positive
buyLine = isTrue (jacoBuy AND wilBuy AND macdBuy AND maBuy AND rocDirBuy);
SystemBuy = isTrue (cross (buyLine, 0));


// SELL: Jaco MRI was in sell area recently (i.e. the fund was overbought); currently WIlliams AC+ and MACD are below their reference lines (probably should replace it by more sensitive slope of the oscilator as A.Elder recommends); price closes below 10 day MA, and Relative slope is negative
sellLine = isTrue (jacoSell AND wilSell AND macdSell AND maSell AND rocDirSell);
SystemSell = isTrue (cross (sellLine, 0));

sinceBuy = barssince (SystemBuy);
sinceBuy = IIF (IsEmpty (sinceBuy), 1000, sinceBuy);
sinceSell = barssince (SystemSell);
sinceSell = IIF (IsEmpty (sinceSell), 1000, sinceSell);
sinceSignal = IIF (sinceBuy < sinceSell, sinceBuy, -sinceSell);

column8 = sinceSignal;
column8name = "N/D";
column8format = 2.1;

// ------------------  Peak based signals (based on Dags work) ======

pv = peak(close, 2);

buyPeak = isTrue (cross(close, pv + pv/500));
sellPeak = isTrue (cross( pv + pv/1000, close)); 

sinceBuy = barssince (buyPeak);
sinceBuy = IIF (IsEmpty (sinceBuy), 100, sinceBuy);
sinceSell = barssince (sellPeak);
sinceSell = IIF (IsEmpty (sinceSell), 100, sinceSell);
sinceSignal = IIF (sinceBuy < sinceSell, sinceBuy, -sinceSell);

column9 = sinceSignal;
column9name = "Peak";
column9format = 2.0;

// ============= Stochastics divergence =====================
/*Positive Stochastic Divergence for use in
Indicator Builder and Automatic Analysis (scan mode),
by Dimitris Tsokakis*/
ST33=STOCHD(14);
TR1=LLVBARS(ST33,4);
TR2=IIF(ST33<30 AND TR1>0 AND REF(TR1,-1)==0,ref(ST33,-1),0);
TRC=IIF(TR2>0,C,0);
vs=valuewhen(tr2, ref(st33,-1), 1);
dvs=vs-ref(vs,-1);
vc=valuewhen(trc, LLV(c,3), 1);
dvc=vc-ref(vc,-1);
diver=iif(dvs>0 and dvc<0,30,0);
DAS=BARSSINCE(REF(TR2,-1)>0);
DD=IIF(DAS<20 AND C>=REF(C,-1),DIVER,0);

buyStoch=DD>0 ;
sinceBuy = barssince (buyStoch);
sinceBuy = IIF (IsEmpty (sinceBuy), 100, sinceBuy);

/*Negative Stochastic divergence for use in
Indicator Builder and Automatic Analysis (scan mode),
by Dimitris Tsokakis*/
ST33=stochd(14);
TR1=HHVBARS(ST33,4);
TR2=IIF(ST33>70 AND TR1>0 AND REF(TR1,-1)==0,ref(ST33,-1),0);
TRC=IIF(TR2>0,C,0);
vs=valuewhen(tr2, ref(st33,-1), 1);
dvs=vs-ref(vs,-1);
vc=valuewhen(trc, HHV(H,3), 1);
dvc=vc-ref(vc,-1);
diver=iif(dvs<0 and dvc>0,90,0);
DAS=BARSSINCE(REF(TR2,-1)>0);
ddd=IIF(DAS<20 AND C<REF(C,-1),DIVER,0);

sellStoch = ddd > 0;
sinceSell = barssince (sellStoch);
sinceSell = IIF (IsEmpty (sinceSell), 100, sinceSell);
sinceSignal = IIF (sinceBuy < sinceSell, sinceBuy, -sinceSell);

column10 = sinceSignal;
column10name = "Stoch";
column10format = 2.0;

Slw = optimize("slw",3,1,4,1);
Pds1 =optimize("pds",5,1,16,1);
Pds2 = optimize("pds",10,1,16,1);
upM = optimize ("Up margine", 80, 75, 85, 1);
dnM = optimize ("Down margine", 20, 24, 10, 1);

A =ema((Close -LLV(Low,Pds1))/(HHV(H,Pds1)-LLV(L,Pds1)),Slw)*100;
B=ema((A-LLV(A,pds1))/(HHV(A,Pds1)-LLV(A,Pds1)),Slw)*100;

A1 =ema((Close -LLV(Low,Pds2))/(HHV(H,Pds2)-LLV(L,Pds2)),Slw)*100;
B1=ema((A1-LLV(A1,pds2))/(HHV(A1,Pds2)-LLV(A1,Pds2)),Slw)*100;
SHORT = B > upM AND B1 < dnM;
BUY = B1 > upM AND B < dnM;
confShort = Hold (Short, 5) AND B < Ref (B, -1) and (B - B1) > 40;
confBuy = Hold (Buy, 5) AND B > Ref (B, -1) and (B1 - B) > 40;;

SELL = COVER = 0;

// Do not display confirmed buy/sell meanwhile
sinceBuy = barssince (confBuy);
sinceBuy = IIF (IsEmpty (sinceBuy), 100, sinceBuy);
sinceSell = barssince (SHORT);
sinceSell = IIF (IsEmpty (sinceSell), 100, sinceSell);
sinceSignal = IIF (sinceBuy < sinceSell, sinceBuy, -sinceSell);

column11 = sinceSignal;
column11name = "5&10 Stch";
column11format = 2.0;
