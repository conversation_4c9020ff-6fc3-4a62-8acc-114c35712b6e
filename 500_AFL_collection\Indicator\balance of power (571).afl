//------------------------------------------------------------------------------
//
//  Formula Name:    balance of power
//  Author/Uploader: abdula 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2005-11-18 10:36:43
//  Origin:          
//  Keywords:        
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=571
//  Details URL:     http://www.amibroker.com/library/detail.php?id=571
//
//------------------------------------------------------------------------------
//
//  54
//
//------------------------------------------------------------------------------

// Balance of Power
// Example code can be found at:
// http://www.traders.com/Documentation/FEEDbk_docs/Archive/082001/TradersTips/TradersTips.html 

//----- This prevents a bad data point from generating a divide by zero.
THL = IIf(H != L, H - L, 0.01);

//----- Reward based on Open
BullOpen = (H - O)/THL;
BearOpen = (O - L)/THL;

//----- Reward based on Close
BullClose = (C - L)/THL;
BearClose = (H - C)/THL;

//----- Reward based on Open - Close
BullOC =IIf(C > O, (C - O)/THL, 0);
BearOC =IIf(O > C, (O - C)/THL, 0);
BullReward = (BullOpen + BullClose + BullOC)/3;
BearReward = (BearOpen + BearClose + BearOC)/3;

BOP = BullReward - BearReward;

Period1 = 34;
SmoothBOP = EMA(BOP, Period1);

Period2 = 34;
SmootherBOP = TEMA(SmoothBOP, Period2);

//Plot(BOP, "BOP", colorBlack, 1);
Plot(SmoothBOP, "BOP " + Period1, colorLightBlue, 1);
Plot(SmootherBOP, "", colorRed);
Plot(Ref(SmootherBOP, -2), "", colorBlue);
Plot(0.1, "", colorGreen);
Plot(0, "", colorBlack);
Plot(-0.1, "", colorGreen);