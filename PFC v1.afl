//AFL P&F Chart for Amibroker Indicator window. Based on High/low prices.
//Based on code in AB help files
//myReverse is 3 boxes.
//<PERSON> 30 Sep 2003

// Plot( C, "Close", colorBlack, styleCandle );

//Size for P&F boxes
Box = boxsize = 3;
HX = round( ( H / box ) * 10 ) / 10;
LX = round( ( L / box ) * 10 ) / 10;
RH = floor( HX );
FL = ceil( LX );

myVar = ", H " + H + ", HX = " + HX + ", RH = " + RH;

// initialize first element
j = 0;

myReverse = Param("Reverse", 3, 1, 10, 1);                      // reversal requirement

PFC[j] = FL[0];
PFO[j] = PFC[j] + 1;
down = 1;                  // By default the first bar is a down bar.
up = 0;
swap = 0;

// perform the loop that produces PF Chart
for( i = 1; i < BarCount; i++ )
{

    if( FL[i] <= PFC[j] - 1 && down )      //continue down
    {
        PFC[j] = FL[i];
        PFO[j] = PFC[j] + 1;
    }
    else
    {
        if( RH[i] >= PFC[j] + myReverse && down ) //Change direction to up
        {
            j++;
            swap = 1;
            PFC[j] = RH[i];
            PFO[j] = PFC[j] - 1;
        }
    }

    if( RH[i] >= PFC[j] + 1 && up )        //Continue up
    {
        PFC[j] = RH[i];
        PFO[j] = PFC[j] - 1;
    }
    else
    {
        if( FL[i] <= PFC[j] - myReverse && up )  //Change direction to down
        {
            j++;
            PFC[j] = FL[i];
            PFO[j] = PFC[j] + 1;
            swap = 1;
        }
    }

    if( swap )
    {
        swap = 0;

        if( up )
        {
            up = 0;
            down = 1;
        }
        else
        {
            up = 1;
            down = 0;
        }
    }
}

delta = BarCount - j - 1;

PFO = Ref( PFO, -delta );
PFC = Ref( PFC, -delta );

// High-Low range sets the height of the P&F bar
H = IIf( Ref( PFC, -1 ) > Ref( PFO, -1 ), Ref( HHV( PFC, 1 ), -1 ) - 1, Max( PFO, PFC ) ) * Box;
L = IIf( Ref( PFC, -1 ) < Ref( PFO, -1 ), Ref( LLV( PFC, 1 ), -1 ) + 1, Min( PFO, PFC ) ) * Box;
O = IIf( Ref( PFC, -1 ) > Ref( PFO, -1 ), Ref( HHV( PFC, 1 ), -1 ) - 1, IIf( Ref( PFC, -1 ) < Ref( PFO, -1 ), Ref( LLV( PFC, 1 ), -1 ) + 1, PFO ) ) * Box;
C = O + Box * IIf( PFC > PFO, 1, -1 );

// --- Main P&F Chart ---
Plot( C, "P&F Chart Close", IIf( PFC > PFO, colorBlue, colorRed ), styleCandle + styleNoLabel + stylePointAndFigure );
/*
// --- Expand P&F to timeline by holding levels constant until next column change
PfO = ValueWhen( PFC != Ref(PFC,-1), PO, 1 );
PfH = ValueWhen( PFC != Ref(PFC,-1), PH, 1 );
PfL = ValueWhen( PFC != Ref(PFC,-1), PL, 1 );
PfC = ValueWhen( PFC != Ref(PFC,-1), PC, 1 );
*/

GraphXSpace = 2;
Title = "P&F  " + Name() + "  PF HiLo, H: " + H + ", L: " + L + ", Box: " + box + ", Reversal: " + myReverse  + myVar + ", {{VALUES}}";

// --- Arrays to hold extended levels
HiLevel = Null;
LoLevel = Null;
ColIdx = 0;
for(i = 0; i < BarCount; i++) {
	if(!PFC[ColIdx])
		ColIdx++;
}

printf("ColIdx %g", ColIdx);
/*
// --- Loop through bars, extend until broken
for( i = 1; i < BarCount; i++ )
{
    // Start of a new P&F column (when Close != previous Close)
    if( ColIdx < BarCount && PFC[ColIdx] != PFC[ColIdx-1] )
    {
        // Record the high & low of this column
        levelHigh = PfH[ColIdx];
        levelLow  = PfL[ColIdx];

        // Extend forward until broken
        j = i;
        while( j < BarCount AND High[j] <= levelHigh )
        {
            HiLevel[j] = levelHigh;
            j++;
        }
        j = i;
        while( j < BarCount AND Low[j] >= levelLow )
        {
            LoLevel[j] = levelLow;
            j++;
        }
		    ColIdx++;

    }
}
*/
// --- Plot extended levels
Plot( HiLevel, "P&F High Level", colorBlue,  styleLine | styleThick | styleNoRescale );
Plot( LoLevel, "P&F Low Level",  colorRed,   styleLine | styleThick | styleNoRescale );;

// --- Identify new up and down columns
NewUp   = PFC > Ref(PFC, -1) AND PFC > PFO;
NewDown = PFC < Ref(PFC, -1) AND PFC < PFO;

// --- Support/Resistance levels at reversals
SuppLevel = IIf(NewUp,   H + box, Null);   // low of new up column
ResLevel  = IIf(NewDown, L - box, Null);   // high of new down column

// --- Fill forward the most recent levels
Support    = ValueWhen(NewUp,   SuppLevel, 1);
Resistance = ValueWhen(NewDown, ResLevel, 1);

Plot( Support, "Support", colorGreen, styleDots | styleLine );
Plot( Resistance, "Resistance", colorOrange, styleDots | styleLine );
