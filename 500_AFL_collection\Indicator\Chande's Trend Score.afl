//------------------------------------------------------------------------------
//
//  Formula Name:    <PERSON><PERSON>'s Trend Score
//  Author/Uploader: <PERSON> 
//  E-mail:          
//  Date/Time Added: 2005-03-04 13:42:00
//  Origin:          
//  Keywords:        Chande Trend Score
//  Level:           medium
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=436
//  Details URL:     http://www.amibroker.com/library/detail.php?id=436
//
//------------------------------------------------------------------------------
//
//  I just found this code in a Visual Trader site and transfered it into AFL.
//
//------------------------------------------------------------------------------

SmoothingPeriod = 5;

CTS=IIf(C>=Ref(C,-11),1,-1)+IIf(C>=Ref(C,-12),1,-1)+IIf(C>=Ref(C,-13),1,-1)+IIf(C>=Ref(C,-14),1,-1)+IIf(C>=Ref(C,-15),1,-1)+IIf(C>=Ref(C,-16),1,-1)+IIf(C>=Ref(C,-17),1,-1)+IIf(C>=Ref(C,-18),1,-1)+IIf(C>=Ref(C,-19),1,-1)+IIf(C>=Ref(C,-20),1,-1);

CTSM = WMA(CTS,SmoothingPeriod);
Histo = IIf(CTSM==10,5,IIf(CTS==-10,-5,Null)); 

Plot(CTSM, "CTSM", colorBlack, styleLine );
Plot(Histo, "", colorGreen, styleHistogram );

Plot(12,"",colorRed,styleLine);
Plot(-12,"",colorRed,styleLine);