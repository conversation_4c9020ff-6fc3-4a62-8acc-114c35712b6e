#include <Common.afl> 
#include <AvgVol.afl> 
#include <ATP.afl> 

GraphXSpace = 20;

myVar = ""; 
 
lastBarOfTheDayTime     = 150000; // Exit all trades 
lastBarOfTheDay         = (TimeNum() >= lastBarOfTheDayTime);
lastTradeOfTheDayTime   = 134000; //124200;  
lastTickOfTheDayTime    = 133000; //124200;  
firstTradeOfTheDayTime  = 104000; //124200;  
firstTickOfTheDayTime   = 104000; //124200;  
TradingZone             = (TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime); 
TickZone                = (TimeNum() >= firstTickOfTheDayTime AND TimeNum() <= lastTickOfTheDayTime); 

boxSize = 0.5; 

_SECTION_BEGIN( "stMA" ); 
// Moving Averages  
period = Param("Trade Avg", 60, 10, 100, 1);  
top 	= Max( LinearReg( High, period), WMA( High, ceil( period/ 2 )) ); 
bottom 	= Min( LinearReg( Low , period), WMA( Low , ceil( period/ 2 )) ); 
top 	= ceil (top   /boxSize)*boxSize; 
bottom 	= floor(bottom/boxSize)*boxSize; 
stMA 	= (top + bottom)/2; 
_SECTION_END();

_SECTION_BEGIN( "System" ); 
eqAtRisk	= Param("Risk", 30000, 5000, 200000, 5000); 
exit_risk   = eqAtRisk/RoundLotSize;
exit_risk   = floor(C/150);
sRisk		= top - C; 
bRisk		= C - bottom; 
p_ATR  = round(ATR(period/2)/boxSize)*boxSize;

Buyflag = Shortflag = BuyP = ShortP = 0; 
Buy = Short = Cover = Sell = 0; 
PThit = PT = exit = t_exit = 0; 
BuyZone = ShortZone = bzf = szf = bzh = szl = 0;

for( i = 1; i <= BarCount - 1; i++ ) 
{ 
  if(firstBarOfTheDay[i]) {
    no_of_trds = 0;
    lastTrade = True;
    bzf = bzh = False;
    szf = szl = False;
  }
  PT[i]	= PT[i-1];
  exit[i]  = exit[i-1];
  
  if(Buyflag){ 
	t_exit = Max(Min(H[i-1] - 4*p_ATR[i-1], H[i-1] - exit_risk[i]), bottom[i-1]);
	if (O[i] > t_exit) { 
	  exit[i] = Max(t_exit, exit[i-1]); 
  	} 
	else { 
	  exit[i] = exit[i-1]; 
	} 
  } 
  if(Shortflag){ 
	if (O[i] < Min(top[i-1], exit[i-1])) { 
	  exit[i] = Min(top[i-1], exit[i-1]); 
  	} 
	else { 
	  exit[i] = exit[i-1]; 
	} 
  }
  exit[i] = round(exit[i] / boxSize[i]) * boxSize[i]; 
  if(Buyflag AND L[i] < exit[i]) { 
    Sell[i] = 1; 
    Buyflag = 0; 
    SellPrice[i] = IIf(O[i] > exit[i], exit[i], C[i]); 
    lastTrade = SellPrice[i] > BuyP; 
    exit[i] = 0; 
    PT[i]	= 0; 
  } 
  if(Buyflag AND lastBarOfTheDay[i]) { 
    Sell[i] = 1; 
    Buyflag = 0; 
    SellPrice[i] = C[i]; 
    lastTrade = SellPrice[i] > BuyP; 
    exit[i] = 0; 
    PT[i]	= 0; 
  } 
  if(Buyflag AND PT[i] AND H[i] >= PT[i] AND NOT PThit) {    
    Buy[i]      = sigScaleOut; 
    BuyPrice[i] = PT[i]; 
    PT[i]	= 0; 
    PThit = 1;    
  }
  if(Shortflag AND PT[i] AND L[i] <= PT[i] AND NOT PThit) {    
    Short[ i ]    = sigScaleOut; 
    ShortPrice[i] = PT[i]; 
    PT[i]	= 0; 
    PThit = 1;    
  } 
  if(Shortflag AND H[i] > exit[i]){ 
	Cover[i] = 1; 
    Shortflag = 0; 
    CoverPrice[i] = IIf(O[i] < exit[i], exit[i], C[i]); 
    lastTrade = CoverPrice[i] < ShortP; 
    exit[i] = 0; 
    PT[i]	= 0; 
  } 
  if(Shortflag AND lastBarOfTheDay[i]){ 
	Cover[i] = 1; 
    Shortflag = 0; 
    CoverPrice[i] = C[i]; 
    exit[i] = 0; 
    PT[i]	= 0; 
  } 
  if(NOT bzf AND NOT Buyflag AND ema_osc[i] AND NOT Sell[i] AND C[i] > top[i]  AND TickZone[i] 	AND rising[i] AND rhvf[i]) {
	BuyZone[i] = bzf = 1;
	bzh = H[i];
  }
  if(NOT Buyflag AND TradingZone[i] AND bzf AND H[i] > bzh) {
    BuyPrice[i]	= BuyP = bzh; //IIf(H[i] > top[i-1] AND O[i] < top[i-1], top[i-1], C[i]);  
    exit[i]		= bottom[i-1];
    bRisk[i] 	= (BuyP - exit[i]);  
    if(floor(RoundLotSize * bRisk[i]) <= eqAtRisk) { 
      Buyflag 	= 1; 
      Shortflag = 0; 
      Buy[i]    = 1; 
      PT[i]		= BuyP + 1.5*bRisk[i]; 
      PThit     = 0; 
      bzf       = False;
    }
  }
  if(NOT szf AND NOT Shortflag AND NOT ema_osc[i] AND NOT Cover[i] AND C[i] < bottom[i] AND TickZone[i] AND falling[i] AND rhvf[i]) {
	ShortZone[i] = szf = 1;
	szl = L[i];
  }
  if(NOT Shortflag AND TradingZone[i] AND szf AND L[i] < szl) {  
    ShortPrice[i]	= ShortP	= szl;//IIf(L[i] < bottom[i-1] AND O[i] > bottom[i-1], bottom[i-1], C[i]);  
    exit[i]		    = top[i-1];  
    sRisk[i] 		= (exit[i] - ShortP); //eqAtRisk  
    if(floor(RoundLotSize * sRisk[i]) <= eqAtRisk) { 
      Buyflag   = 0; 
      Shortflag = 1; 
      Short[i]  = 1; 
      PT[i]		= ShortP - 1.5*sRisk[i]; 
      PThit     = 0; 
      szf       = False;
    } 
  } 
} 
 
PT    = round(PT / boxSize) * boxSize; 
PT    = IIf(PT > 0, PT, Null); 
exit  = IIf(exit > 0, exit, Null); 
bLots = Max(0, floor(eqAtRisk / (RoundLotSize * IIf(bRisk, bRisk, 1)) )); 
sLots = Max(0, floor(eqAtRisk / (RoundLotSize * IIf(sRisk, sRisk, 1)) )); 

bLots		= Min(floor(2040000/(RoundLotSize*C)), bLots); 
sLots		= Min(floor(2040000/(RoundLotSize*C)), sLots);
/*
bLots		= 1; 
sLots		= 1;
*/
 
SetPositionSize(IIf(Buy, bLots, sLots)*RoundLotSize, spsShares); 
SetPositionSize( 50 , IIf( Buy == sigScaleOut OR Short == sigScaleOut, spsPercentOfPosition, spsNoChange ) ); // for scale-out use 50% of current position size 
myvar  	+= StrFormat(EncodeColor(colorBlue) + ", channel = ( %0.2f, %0.2f )"  + EncodeColor(colorBlack) + ", lots = %0.0f, %0.0f", bzh, szl, bLots, sLots); 
myvar	+= ", risk = " + Max(0, round(RoundLotSize*bRisk)) + ", " + Max(0, round(RoundLotSize*sRisk));
//myVar += ", avur = " + NumToStr(MA(abs(avur), 21),1.2) + ", avdr = " + NumToStr(MA(abs(avdr), 21),1.2);
myVar 	+= ", rvol = " + NumToStr(rvol, 1.2);

_SECTION_END();

//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////// 
// Plot 
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////// 
Plot( Close, "Close", colorBlack, styleNoTitle | GetPriceStyle(), Null, Null, Null ); 
 
Plot( top   , "", ColorBlend(colorOrange, GetChartBkColor()), styleNoRescale | styleNoLabel, Null, Null, Null, -8 ); 
Plot( bottom, "", ColorBlend(colorBlue  , GetChartBkColor()), styleNoRescale | styleNoLabel, Null, Null, Null, -8 ); 
 
Plot(stMA, "", IIf(ema_osc, colorPaleGreen, colorRed), styleLine|styleThick, Null, Null, Null, -1 ); 
PlotOHLC( top, top, bottom, bottom, "", IIf(TradingZone, IIf(Flip(Close > top, Close < bottom), colorLime, colorPink), colorLightGrey), styleCloud | styleNoRescale | styleNoLabel, Null, Null, Null, -9 ); 
Plot( PT, "PT", colorSkyblue, styleStaircase | styleNoRescale | styleDashed, Null, Null, Null, -7 );     
 
 
Plot( exit, "exit", colorBlack, styleStaircase | styleNoRescale | styleDashed, Null, Null, Null, -7 ); 
 
PlotShapes(shapeSmallUpTriangle * Sell   , colorPink, 0, Low); 
PlotShapes(shapeSmallDownTriangle * Cover, colorPink, 0, High); 
PlotShapes(shapeSmallUpTriangle * Short  , colorRed   , 0, Low); 
PlotShapes(shapeSmallDownTriangle * Buy  , colorGreen , 0, High); 
PlotShapes(shapeSmallUpTriangle * ShortZone , colorYellow , 0, Low); 
PlotShapes(shapeSmallDownTriangle * BuyZone , colorYellow , 0, High); 
 
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////// 
// The Title 
//////////////////////////////////////////////////////////////////////////////////////////////////////////////////////// 
candleColor = SelectedValue( IIf( ROC(C, 1) < 0, colorRed, colorGreen ) ); 
_N( Title   = StrFormat( "{{NAME}}, {{INTERVAL}}, {{DATE}} - "
						+ EncodeColor(colorSkyblue) +"High  = "  + EncodeColor(colorwhite) + NumToStr(H,1.2)
						+ EncodeColor(colorSkyblue) +", Low  = " + EncodeColor(colorwhite) + NumToStr(L,1.2)
						+ EncodeColor(colorSkyblue) +", Open = " + EncodeColor(colorwhite) + NumToStr(O,1.2) 
						+ EncodeColor(colorSkyblue) +", Close =" + WriteIf( avur > avdr, EncodeColor(colorDarkGreen) + NumToStr(C,1.2),
										EncodeColor(colorRed) + NumToStr(C,1.2) ) 
						+ EncodeColor( colorBlack ) + myVar + ", " 
						+ EncodeColor( -1 ) + "{{VALUES}}" 
                     ) );