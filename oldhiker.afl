
{
    (
        GfxSetBkMode( 2 ) +
        GfxSetBkColor( colorWhite ) +
        GfxSetTextColor( colorBlack ) +
        GfxSetOverlayMode( 0 ) +
        GfxSelectFont( "Times New Roman", 12, 700, False ) +
        GfxTextOut( "Simple 3xEMAs Looping", 10, 15 ) +
        GfxTextOut( "Looping Trail Stop Loss", 8, 35 ) +
        GfxSelectFont( "Times New Roman", 10, 500, False ) +
        GfxTextOut( "Plot RedCCI only, 17-5-2021", 10, 55 ) 
    );
}
SetChartBkColor( ColorRGB( 255, 193, 105 ) );
Plot( Close, "Price", colorBlack, styleCandle | styleThick, 0, 0, 0, 0, 2 );


//==   Parameters   == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==
FxBG   = Optimize( "FxBG at SetUp", 1.15, 0.01, 1.5, 0.05 );
FxISL  = Optimize( "FxISL at SetUp", 0.8, 0.01, 1.0, 0.05 );

HoldBuyCandles  = Optimize( "HoldBuyCandles", 12, 2, 20, 1 );	// Hold Buy Candle for about 8 candles to get Hit
FxRedCCI  = Optimize( "FxRedCCI", 0.68, 0.1, 2.0, 0.01);	//  21-3-2021. Used by Low - FxRedCCI * ATR(10)  17-5-2021


//==   GMMA   == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==
{
    ATR10 = ATR( 10 );
    E3 = EMA( Close, 3 );
    E5 = EMA( Close, 5 );
    E8 = EMA( Close, 8 );
    E15 = EMA( Close, 15 );
    E20 = EMA( Close, 20 );
    E25 = EMA( Close, 25 );
    E30 = EMA( Close, 30 );
    E60 = EMA( Close, 60 );

    // Plot( E3, "", colorBlue, styleLine | styleThick );
    // Plot( E5, "", colorBlue, styleDashed );
    // Plot( E15, "", colorBlack, styleLine );
    // Plot( E30, "", colorBlue, styleLine );
    // Plot( E60, "", colorBlack, styleLine | styleThick );

	// Cloud plotting for GMMA    6/10/2012
    // PlotOHLC( E3, E3, E5, E5, "Cloud", IIf( E3 > E5, colorYellow , colorWhite ), styleCloud,  0, 0, 0, -2, 0 );
    PlotOHLC( E5, E5, E15, E15, "Cloud", IIf( E5 > E15, colorLightBlue , colorPink ), styleCloud,  0, 0, 0, -2, 0 );
    PlotOHLC( E30, E30, E60, E60, "Cloud", IIf( E30 > E60, ColorRGB( 51, 255, 102 ), colorTan ), styleCloud, 0, 0, 0, -3, 0 );
}
//_SECTION_BEGIN("selected trade values");
//== == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==   Calculate BG values and ISL values
{
    BGval = High + FxBG * ATR10;
    Plot( BGval, "BGval", colorBlue, styleLine | styleStaircase );

    ISLval = Low - FxISL * ATR10;
    Plot( ISLval, "ISLval", colorRed, styleLine | styleStaircase );

}
//== == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==
//==   Supporting indicators   == == == == ==    20-1-2021
//== == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==
//_SECTION_BEGIN("Enter Long");					//
//  Short list of Setups (SU) to Enter Long Stop Order
{
    CI = CCI( 14 );
    CIe = EMA( CI, 5 );
    CIx = ( Ref( CI, -1 ) > 125 AND Cross( 125, CI ) );					//redCCI event
    EvredCCI = CIx AND E3 > E15 AND  E15 > E60 ;
    // PlotShapes( EvredCCI  * shapeDigit1 , colorBlue, 0, BGval, 15 ); //== == == ==  Test

//-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
    EvE3COTup =  Ref( E3, -2 ) > Ref( E3, -1 ) AND
                 Ref( E3, -1 ) < E3 AND E3 > E15 AND  E15 > E60;
//PlotShapes( EvE3COTup * shapeHollowUpTriangle, colorBlue, 0, ISLval, -8 );  // Test
    // PlotShapes( EvE3COTup * shapeHollowUpArrow, colorBlue, 0, BGval, -8 );  // Test

    BuySU =   EvE3COTup ;	// This is too early EvredCCI, better to use the GMMA EvE3COTup.  12-05-2021
// Plot(BuySU , "BuySU ",  ColorRGB(255,109,36), styleHistogram|styleOwnScale|styleNoTitle, 0,0,0,-1,4);	// Orange  Test
}


//== == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==
//== == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==
//		All LONG Enter Setups	12-3-2021
//== == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==

//_SECTION_BEGIN (" ENTER");

{
// Show All Long Enter setup events.
    EvBuySU_All =  EvredCCI ||  EvE3COTup ;	// Event  Not all are shown 12-3-2021.
    // PlotShapes( EvBuySU_All * shapeSmallCircle, colorBlue,0 ,High, 45);	// Test OK

//-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
//Show only the FIRST of the series.
    EvBuySU_AllFst = Ref( EvBuySU_All, -1 ) == False AND EvBuySU_All;
    PlotShapes( EvBuySU_AllFst * shapeUpArrow, colorBlue,0 ,E60, -30);	// Test OK

//-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
//	To find BG/BarNo value at EvBuySU_AllFst event AND trailing down BGval
    BGvalBuySU_AllFst = ValueWhen( EvBuySU_AllFst, BGval, 1 ); //
    // Plot(BGvalBuySU_AllFst,"BGvalBuySU_AllFst", colorYellow, styleLine, 0,0,0,0,3); 		// Yellow line is OK. Testing
    BarNoBuySU_AllFst = ValueWhen( EvBuySU_AllFst, BarIndex(), 1 ); //
//-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
// Track the BGval when it falls from initial setup
    B2 =  Min( BGvalBuySU_AllFst, BGval ); 	// BGval tracks down from HIGH as the candles retreat
    // Plot(B2 , "B2 ", colorWhite,  styleLine | styleDashed, 0,0,0,0,2);  //   OK  Test only
    BGvalBuySU_AllFst = LowestSince( EvBuySU_AllFst, B2, 1 );
    // Plot(BGvalBuySU_AllFst , "BGval BGvalBuySU_AllFst ", colorGreen, styleStaircase | styleDashed , 0,0,0,1,2);  //  OK  Test only
//== == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==

    EvBuySU_AllActive = IIf( ( BarIndex() >= ( BarNoBuySU_AllFst ) ) AND( BarIndex() <= ( BarNoBuySU_AllFst + HoldBuyCandles ) ), BGvalBuySU_AllFst , Null ); //
    // Plot(EvBuySU_AllActive , "EvBuySU_AllActive ", colorWhite,  styleLine , 0,0,0,-1,5);  //  OK  Test only.
    BGvalBuySU_AllActive = ValueWhen( EvBuySU_AllActive, BGvalBuySU_AllFst, 1 );
	// BGvalBuySU_AllActive = IIf((BarIndex() >= (BarNoBuySU_AllFst)) AND  (BarIndex() <= (BarNoBuySU_AllFst + HoldBuyCandles)),BGvalBuySU_AllFst , Null); //
    // Plot(BGvalBuySU_AllActive , "BGvalBuySU_AllActive ", colorGreen,  styleLine , 0,0,0,-1,5);  //  OK  Test only. green plot finishes at Hit or after HoldBuyCandles
	// BarNoPastActive = ValueWhen(BarIndex() == (BarNoBuySU_AllFst + HoldBuyCandles), BarIndex(), 1);		//  Does not work
    // PlotShapes( BarIndex() == BarNoPastActive * shapeSmallUpTriangle, colorBlue,0 ,ISLval, -28);	//  Does not work

//-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --

    //== Hit Event when BGvalBuySU_AllFst is Hit event.
    EvLongActiveHit = Ref( High, -1 ) < Ref( BGvalBuySU_AllActive , -1 ) AND	// High yesterday is below Active plot
                      High > Ref( BGvalBuySU_AllActive , -1 ); // This is the only one that works. >> The High has penetrated above the Active Plot
    // PlotShapes( EvLongActiveHit * shapeCircle, colorRed,0 ,Ref(BGvalBuySU_AllFst, -1), 0);	// Test OK
    BarNoLongActiveHit = ValueWhen( EvLongActiveHit, BarIndex(), 1 );
    // Cannot get these lines below to work.
    //	BGvalBuySU_AllActive = IIf((BarIndex() >= (BarNoBuySU_AllFst)) AND  (BarIndex() <= (BarNoBuySU_AllFst + HoldBuyCandles)) AND BarIndex() < BarNoLongActiveHit  ,BGvalBuySU_AllFst , Null); //
    //		Plot(BGvalBuySU_AllActive , "BGvalBuySU_AllActive ", colorBrown,  styleLine , 0,0,0,-1,10);  //    Test only

// Cannot get this to work		//	EvBuySU_AllFst = ExRem( EvBuySU_AllFst, EvLongActiveHit ); 		// Cannot get this to work
// Cannot get this to work		//	EvLongActiveHit = ExRem(EvLongActiveHit, EvBuySU_AllFst);		// Cannot get this to work

    // To find the Buy price for the Enter order
    BGvalLongActiveHit = ValueWhen( EvLongActiveHit , Ref( BGvalBuySU_AllFst, -1 ), 1 );
    // PlotShapes( BGvalLongActiveHit * shapeUpArrow, colorBlue,0 ,Low, -25);
    PlotShapes( EvLongActiveHit * shapeStar, colorBlue, 0 , Ref( BGvalBuySU_AllFst, -1 ), 0 );	//  Test. This is the Buy price for the Enter order
    BarNoLongActiveHit = ValueWhen( EvLongActiveHit , BarIndex(), 1 );
    //PlotShapes( EvLongActiveHit * shapeSmallUpTriangle, colorBlue,0 ,ISLval, -8);	//  Test
    //PlotShapes( EvLongActiveHit * shapeSmallCircle, colorYellow,0 ,ISLval, -8);	//  Test   To crate a distinction 14-3-2021

    // BarNo at LongSU - BarNo when Hit.  Template
    BarNoDiffEn = ( BarNoBuySU_AllFst - BarNoLongActiveHit ) ;		//   NOTE THE ORDER OF THE SUBTRACTION == == == == == == ==
    // Plot( BarNoDiffEn  ,"BarNoDiffEn ", colorWhite, styleLine|styleOwnScale | styleStaircase, 0,0,0,0,3);	//  Test

    LinLenEn = BarNoDiffEn ;
    ShowLineEn = BarsSince( BGvalBuySU_AllFst ) < LinLenEn ;	// True/False result.
    // Plot(ShowLineEn , "ShowLineEn ", colorYellow, styleLine|styleLeftAxisScale|styleStaircase, 0,0,0,0,3 ); 	//  Test

    // Plot(IIf(ShowLineEn == True AND (BarIndex() < (BarNoBuySU_AllFst + HoldBuyCandles)),BGvalBuySU_AllFst , Null),"Bgval for BGvalBuySU_AllFst", 	//  Test
    //	colorGreen,styleLine|styleThick|styleDots, 0,0,0,0,5);

    //  Define extent of BGvalBuySU_AllFst
    BGvalAllLongSetUpsA = IIf( ShowLineEn == True AND( BarIndex() < ( BarNoBuySU_AllFst + HoldBuyCandles ) ), BGvalBuySU_AllFst , Null );
    //  Plot BGvalAllLongSetUpsA
    Plot( BGvalAllLongSetUpsA , "Bgval for AllLongSetUps", colorBlue, styleDots, 0 , 0, 0, 0, 4 );		// This is correct, OK

	//==  To plot the ISLval during the SU (Setup phase)  Thin white line under the thick blue line
    EvISLvalAtSU = IIf( BGvalAllLongSetUpsA, ISLval, Null );
    Plot( EvISLvalAtSU , "ISLval during SU phase", colorWhite, styleDots, 0 , 0, 0, 0, 2 );		// This is correct, OK

//==  To Calculate trade size   === == ==
NoOfShares = 25000/(BGvalAllLongSetUpsA - EvISLvalAtSU);	// Risk Captial = 25000 per trade
blots = floor( NoOfShares / RoundLotSize ) * RoundLotSize;

//ApproxCost = NoOfShares * EvISLvalAtSU;


}
//_SECTION_END();	// End of ENTER
//== ==   SETUPS for TrailStopLoss (TSL) for Long trade     == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==


//_SECTION_BEGIN( "Exit" );

//== == == == == == == == == == == == == == == == == == == == == == == == == == == == == == == ==
//==  List of possible Exit setups, for evaluation. (This is not complete, just for Proof of Concept

//---   RedCCIex   -----------------------  21-3-2021
{
    CI = CCI( 14 );
    CIe = EMA( CI, 5 );
    CIx = ( Ref( CI, -1 ) > 125 AND Cross( 125, CI ) );							//redCCI event
    EvredCCIex = CIx AND  E3 > E15 AND  E15 > E60 ;
    PlotShapes( EvredCCIex  * shapeDigit1 , colorRed, 0, Low, -15 ); 			// redCCI OK
    ExitPrice3 = ValueWhen( EvredCCIex, Low - ( FxRedCCI * ATR( 10 ) ) );
    Plot( ExitPrice3, "ExitPrice4", colorGreen, styleLine | styleDashed, 0, 0, 0, 0, 3 );				// Green Test OK
}

//   To find when  EvRedCCIHit   -- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --
EvRedCCIHit = Ref( Low, -1 ) > Ref( ExitPrice3, -1 ) AND Low <  ExitPrice3 ; 		// Low Price falls below TrailStopVal, much better 16-3-2021
PlotShapes( EvRedCCIHit * shapeCircle, colorWhite, 0, ExitPrice3, 0, 0 );		//      					shapeCircle
//-- -- -- -- -- -- -- -- -- -- -- -- -- -- -- --


//

//How to plot a trailing stop in the Price chart				// REFERENCE LOCATION from Knowledge Base

//StopLevel = 1 - Param("trailing stop %", 3, 0.1, 10, 0.1)/100;	// Not used

Buy = EvBuySU_All;
Sell = 0;
trailARRAY = Null;
trailStop = 0;			//
stopLevel = FxRedCCI * ATR10;		// 17-5-2021		// Margin below the Low

for( i = 1; i < BarCount; i++ )
{

    if( trailStop == 0 AND Buy[ i ] )
    {
        //trailStop = High[ i ] - stoplevel[i];	// This gives a TSL value under the High of the candles
        trailStop = Low[i] - stopLevel[i];
    }
    else Buy[ i ] = 0; // remove excess buy signals


    if( trailStop > 0 AND Low[ i ] < trailStop )	// Low has gone below TrailStop, so close the Long trade
    {
        Sell[ i ] = 1;
        SellPrice[ i ] = trailStop;
        trailStop = 0;		// This resets trailARRAY
    }

    //if( trailStop > 0 )	// To get the next trailARRAY
    //if(EvRedCCIHit
    if( trailStop[i] > 0 )	// To get the next trailARRAY
    {
        // trailStop = Max( High[ i ] - stoplevel[i], trailStop );	// Get the higher of existing trailStop and the current trailStop
        trailStop = Max( Low[ i ] - stoplevel[i], trailStop );	// Get the higher of existing trailStop and the current trailStop
        trailARRAY[ i ] = trailStop;
    }

}

SetPositionSize( blots, spsShares );

PlotShapes( Buy*shapeUpArrow, colorGreen, 0, Low );
PlotShapes( Sell*shapeDownArrow, colorRed, 0, High );

Plot( Close, "Price", colorBlack, styleCandle );
Plot( trailARRAY, "trailARRAY", colorWhite , styleStaircase | styleLine, 0, 0, 0, -1, 6 );	// PaleTurquoise	styleStaircase

OptimizerSetEngine("cmae");

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"));