//------------------------------------------------------------------------------
//
//  Formula Name:    <PERSON>'s Volatility Channels
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2006-08-08 09:47:28
//  Origin:          
//  Keywords:        
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=670
//  Details URL:     http://www.amibroker.com/library/detail.php?id=670
//
//------------------------------------------------------------------------------
//
//  Volatility channels (or bands) move with the stock, quickly adjusting to
//  price movements. When a security trends outside the volatility channel, it
//  adds strength to a high probability long trade . Look out for a stock to
//  "trend" outside these bands to catch the momentum. When the swing
//  increases, the bands respond by opening up. The Lower channel normally
//  points to a key fibonacci support zone.
//
//------------------------------------------------------------------------------

/* Larry William's Volatility Channels 
Right Click select Param "parameters" and select variable you need..*/

/* AFL code by Prakash Shenoi*/

// Enable radio button "middle" for grid lines.


MaxGraph = 4;
Vc= Param("Volatility channel -",10,2,14);
Up=HHV(((((H+L+C)/3)*2)- H),Vc);
Lo=LLV(((((H+L+C)/3)*2)- L),Vc);
Graph0=C;
Graph0Style=128+4;
Graph0BarColor = IIf( Close > Ref( Close, -1 ), colorDarkGreen, colorDarkRed );
Graph0Style = styleBar;
Graph0 = Close;
Graph1=Up;
Graph1Style=styleLine;
Graph1Color=colorBlue;
Graph2=Lo;
Graph2Style=styleLine;
Graph2Color=colorBlue;
Title=Name() + "  Volatiltiy Channels - LC" + WriteVal( Graph2 )+ ", UC" + WriteVal( Graph1 )+
", Close" + WriteVal( Graph0 );