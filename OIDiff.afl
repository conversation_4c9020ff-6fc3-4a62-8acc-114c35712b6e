_SECTION_BEGIN( "OI Diff" );
#include <Common.afl>
#include <Options.afl>

strike = GetStrike( Name() );
NIFTYC = Foreign( "NIFTY 50.NSE_IDX", "C" );

//=================================================================================
// Call
//=================================================================================
SetForeign( SymbolCE );
ce = -1;
myC = MA(C + ce * ( NIFTYC - strike ), 3);
Plot( C, "Close", colorBlack, styleNoTitle | GetPriceStyle() );
Plot( myC, "diff", colorBlack, styleLine | styleThick );
vlots = V / RLS;
vCheck = vlots / MA( Ref( vlots, -1 ), 15 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -10 );

RestorePriceArrays();

//=================================================================================
// Put
//=================================================================================
SetForeign( SymbolPE );
ce = 1;
myC = MA(C + ce * ( NIFTYC - strike ), 3);
printf("NIFTYC %f\n", ( NIFTYC - strike ));
Plot( C, "Close", colorWhite, styleNoTitle | GetPriceStyle() );
Plot( myC, "diff", colorWhite, styleLine | styleThick );
vlots = V / RLS;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -10 );
_N( Title   = StrFormat( "{{VALUES}}") );

RestorePriceArrays();



_SECTION_END();