//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi <PERSON>t=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>
// OptimizerSetEngine( "cmae" );
SetOption( "DisableRuinStop", True );

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

//=============================DISPLAY PARAMS======================================
atrMultiplier   = Param( "ATR_Multiplier", 2.5, 1, 10, 0.1 ); // BNF - 4.7
atrPeriod       = Param( "ATR_Period", 5, 5, 30, 1 );
equityAtRisk    = 3000; // Param( "Risk", 10000, 10000, 100000, 5000 );
Target		= 3.5 / 100; //OptimizeNot( "Target", 5.5, 4, 7, 0.5 ) / 100;
//=================================================================================

tn = TimeNum();
dn = DateNum();
newDay = dn != Ref( dn, -1 );

tt1 = OptimizeNot( "T1", 5, 0, 54, 2 );
tt2 = tt1 + Optimize( "T2", 27, 2, 45, 2 );

T1 = TA[tt1]; // 95400
T2 = TA[tt2]; // 103800

TradingZone = ( tn > T2 AND tn <= 150000 );
boxSize = Min( Max( floor( MA( C, 15 ) / 20 ), 1 ), 5 );

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;
Buyflag = 0;

tr = ATR( atrPeriod );
CalcPrice = ( H + L ) / 2;
calcTrend_proc( atrPeriod, tr, atrMultiplier, CalcPrice );

vlots = V / RLS;
vlotsMA = MA( Ref( vlots, -1 ), OptimizeNot( "vp", 12, 1, 50, 1 ) );
vCheck = vlots / vlotsMA;

wick_size = IIf( ( H - L ) == 0, 0, ( H - Max( O, C ) ) / ( H - L ) );
LongWick = wick_size > 0.6;

GC = C > O + boxSize;
RC = C < O - boxSize;
ShortP = BuyP = brisk = srisk = 0;

// define start/end hours in TimeNum format
StartTime = T1;
Endtime = T2;
StartBar = tn == StartTime;
EndBar = tn == Endtime;
Hat12 = ValueWhen( EndBar, HighestSince( StartBar, High ) );
Lat12 = ValueWhen( EndBar, LowestSince( StartBar, Low ) );


hBar = lBar = StartBar;

Trend = ValueWhen( EndBar, IIf( BarsSinceCompare( H, "==", Hat12 ) > BarsSinceCompare( L, "==", Lat12 ), -1, 1 ) );

Mid = myBox( ( ( Hat12 + Lat12 ) ) / 2 );
diff = ( ( Hat12 - Lat12 ) * OptimizeNot( "D1", 1.1, 0.5, 1.5, 0.1 ) ) / 2;
LB = Max( 0.1, myboxF( Mid - diff ) );
UB = Max( 0.1, myBoxC( Mid + diff ) );
UPT = myBoxC( UB + diff );
LPT = Max( 0.1, myBoxC( LB - diff ) );

// pp = Optimize( "pp", 1, 0.5, 3, 0.1 );

RAWB = HighestSince( EndBar, C ) > UB;
L5 = LLV( Ref( L, -1 ), 5 );
H5 = HHV( Ref( H, -1 ), 5 );

PCheck = C > IIf( StrFind( Name(), "NIFTY" ), 15, 2 );

Buy = Trend == -1 AND C > Mid AND L < Mid AND LowestSince( EndBar, Low ) > Lat12 AND WhiteBody;
Buy = C > UPT  AND L < UPT;
Buy = Buy AND TradingZone AND vlotsMA > 100 AND vCheck > 0.5 AND PCheck AND H > H5 AND LowestSince( EndBar, Low ) > Lat12 AND WhiteBody;
Buy = Buy AND Sum( Buy, BarsSince( newDay ) + 1 ) <= 1;
BuyExit = LB;
ShortExit = UB;

Short = Trend == 1 AND C <= Mid AND H > Mid AND HighestSince( EndBar, H ) < Hat12 AND BlackBody;
Short = Short AND TradingZone AND vlotsMA > 100 AND vCheck > 0.5;
Short = Short AND Sum( Short, BarsSince( newDay ) + 1 ) <= 1;
InBuy = Flip( Buy, !TradingZone );

for( i = 10; i < BarCount; i++ )
{

    if( InBuy[i] )
    {
        t_exit = IIf( H[i] > UPT[i] AND vCheck[i] > 3, L[i] - boxSize[i], BuyExit[i - 1] );
        t_exit = MAX( t_exit, IIf( H[i] < UPT[i] AND vCheck[i] > 1, L5[i] - boxSize[i], BuyExit[i - 1] ) );
        BuyExit[i] = Max( Max( BuyExit[i - 1], LB[i] ), t_exit );
    }

    else
    {

        if( Short[i] )
        {
            ShortExit[i] = UB[i];
        }
        else
        {
            t_exit = IIf( L[i] < LB[i] OR vCheck[i] > 1, H[i] + boxSize[i], ShortExit[i - 1] );
            ShortExit[i] = Max( H[i - 1], Min( Min( ShortExit[i - 1], UB[i] ), t_exit ) );
        }

    }
}

// ShortExit = Max(HHV(Ref(H, -1), 10), ShortExit);

Sell = tn >= 150800 OR L < BuyExit;
Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );

// PT
InTrade = Flip( Buy, Sell );
DoScaleOut = ExRem( InTrade AND !Buy AND H > UPT, Sell );
// Buy = Buy + sigScaleOut * DoScaleOut;

BuyPrice = IIf( H > UPT, UPT, C );
SellPrice = IIf( !TradingZone, C, IIf( L < BuyExit, BuyExit, C ) );

Cover = tn >= 150800 OR( Trend == 1 AND H > ShortExit ) ;
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

InTrade = Flip( Short, Cover );
DoScaleOut = ExRem( InTrade AND !Short AND L < LB, Cover );
Short = Short + sigScaleOut * DoScaleOut;
ShortPrice = IIf( L < LB, LB, C );
CoverPrice = IIf( !TradingZone, C, IIf( H > ShortExit, ShortExit, C ) );


bkcolor = LastValue( IIf( Trend < 0, ColorBlend( colorCustom9, colorWhite ), IIf( Trend > 0, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

#include <Alert.afl>

bRisk	= BuyPrice - BuyExit;
bLots	= Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) );

sRisk	= Max( ShortExit - ShortPrice, ( Mid  - LB ) / 2 );
sLots	= Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) );

max_lots = 20;
bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );
SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
SetPositionSize( 50, spsPercentOfPosition * ( Buy == sigScaleOut OR Short == sigScaleOut ) ); // scale out 50% of position

myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", BuyExit, ShortExit );

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );
Plot( IIf( TradingZone, LPT,  Null ), "LPT", ColorRGB( 205, 51, 51 ), styleDashed );
Plot( IIf( TradingZone, LB,  Null ), "LB", ColorRGB( 205, 51, 51 ), styleThick );
Plot( IIf( TradingZone, Mid, Null ), "Mid"  , colorBlack, styleDashed );
Plot( IIf( TradingZone, UB,  Null ), "UB", ColorRGB( 68, 134, 238 ), styleThick );
Plot( IIf( TradingZone, UPT,  Null ), "UPT", ColorRGB( 68, 134, 238 ), styleDashed );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -25 );
Plot( IIf( TradingZone, BuyExit,  Null ), "UPT", colorBlack, styleDashed );

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{DATE}}, "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), vCheck
                       ) );

_SECTION_END();