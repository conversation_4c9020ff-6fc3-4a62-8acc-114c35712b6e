/*****************************************************************
** Strategy: Merged Simple Line Momentum & Linear Regression
** Version: 1.0
**
** --- LOGIC ---
** Long:  From "Linear Regression" by Trading Tuitions.
** Enters long based on a filter using Linear Regression,
** VWMA, and various price action conditions.
**
** Short: From "Simple Line Momentum v1.3".
** Enters short on two down-moves of a buffer line
** without a corresponding up-move in the opposite buffer.
**
** Merged by: Gemini
******************************************************************/

//----------------------------------------------------------------
// SECTION: INCLUDES & SETTINGS
//----------------------------------------------------------------
#include <Common.afl>

//----------------------------------------------------------------
// SECTION: PARAMETERS
//----------------------------------------------------------------
_SECTION_BEGIN( "Parameters" );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

// -- General Params --
eqAtRisk = Param( "Risk", 25000, 20000, 45000, 5000 );

// -- Long Strategy Params --
Periods_Long = Optimize( "Periods (Long)", 102, 20, 120, 1 );
f_Stdev = Param( "F StDev (Long)", 1.1, 1, 3, 0.1 );
nPoints_PSig = Param( "PSignal Bars", 38, 20, 100, 2 );
max_sl_Long = Param( "Max SL Points (Long)", 120, 50, 250, 10 );

// -- Short Strategy Params --
Periods_Short = Param( "Periods (Short)", 90, 20, 200, 2 );
boxSizePercent = Param( "Box %", 0.1, 0.1, 1, 0.1 );
boxSizeORbFactor = Param( "ORB ATR factor", 0.9, 0.2, 1, 0.1 );
p_no_above = Param( "No Above MA (Short)", 60, 5, 200, 5 );

_SECTION_END();

//----------------------------------------------------------------
// SECTION: CUSTOM BAR & INDICATORS
//----------------------------------------------------------------
_SECTION_BEGIN( "Calculations" );

// -- Custom Bar OHLC (for Short Logic) --
firstBar = Day() != Ref( Day(), -1 );
ORbHigh = ValueWhen( firstBar, High );
ORbLow = ValueWhen( firstBar, Low );
ORbCenter = ValueWhen( firstBar, ( ORbHigh + ORbLow ) / 2 );
tr = ATR( 5 * Periods_Short );
ORBAtr = ValueWhen( firstBar, tr );
s_box_size = round( Max( boxSizePercent * ORbCenter / 100, boxSizeORbFactor * ORBAtr ) / 5 ) * 5;

rH = round( H / s_box_size );
myH = s_box_size * IIf( rH < H / s_box_size, rH + 1, rH );
rL = round( L / s_box_size );
myL = s_box_size * IIf( rL > L / s_box_size, rL - 1, rL );
rO = round( O / s_box_size );
myO = s_box_size * IIf( rO > O / s_box_size, rO - 1, rO );
rC = round( C / s_box_size );
myC = s_box_size * IIf( rC > C / s_box_size, rC - 1, rC );

myC = IIf( myH - C < s_box_size / 3, myH, myC );
myC = IIf( C - myL < s_box_size / 3, myL, myC );

// -- Short Logic Indicators --
Vw_Short = KAMA( myC, Periods_Short );
Vw_Short_1 = Ref( Vw_Short, -1 );
top_Short = HMA( myH, ceil( Periods_Short / 2 ) );
bottom_Short = HMA( myL, ceil( Periods_Short / 2 ) );
Lr_Short = ( top_Short + bottom_Short ) / 2;
Lr_Short_1 = Ref( Lr_Short, -1 );
buffer_line_up = ( round( Vw_Short_1 / s_box_size ) + 5 ) * s_box_size;
buffer_line_down = ( round( Vw_Short_1 / s_box_size ) - 5 ) * s_box_size;
No_Above_MA = BarsSince( C < Vw_Short_1 );
candle_size = ( myH - myL ) / s_box_size;

// -- Long Logic Indicators --
Vw_Long		= myround( VWMA2( C, Periods_Long ) );
Lr_Long 	= mybox( ( LinearReg( C, Periods_Long ) + H + L ) / 3 );
Vw_Long_1	= Ref( Vw_Long, -1 );
Lr_Long_1	= Ref( Lr_Long, -1 );
mode 		= Flip( Lr_Long > Vw_Long + boxSize, Lr_Long < Vw_Long - boxSize );
Sd			= myBox( StDev( C, Periods_Long ) );
four_sd_up	= C + f_Stdev * SD;
four_sd_down = C - f_Stdev * SD;

function fPSignal( ser, integer )
{
    nStDev = StDev( ser, integer );
    nSma = MA( ser, integer );
    return IIf( nStDev > 0, Erf( nSma / nStDev / sqrt( 2 ) ), sign( nSma ) * 1.0 );
}
ohlc4 = ( O + H + L + C ) / 4;
nIntr = nPoints_PSig - 1;
nPSignal = MA( fPSignal( Ref( ohlc4, 0 ) - Ref( ohlc4, -1 ), nIntr ), nIntr );
ndPSignal = sign( nPSignal - Ref( nPSignal, -1 ) );
pBuy = nPSignal < 0 AND ndPSignal > 0;

H_16 = myboxC( HHV( myH, 16 ) );
H_16_1 = Ref( H_16, -1 );
L_16 = myboxF( LLV( myL, 16 ) );
L_16_1 = Ref( L_16, -1 );

wick_size = IIf( ( H - L ) == 0, 0, ( H - Max( O, C ) ) / ( H - L ) );
LongWick = wick_size > 0.6;
BuyFilter = Ref( mode, -1 ) AND L > Lr_Long_1 AND C > H_16_1 AND TradingZone AND C > O AND fch > C1D AND pBuy;
myVar += ", BuyFilter " + BuyFilter;
_SECTION_END();

//----------------------------------------------------------------
// SECTION: TRADING LOGIC
//----------------------------------------------------------------
_SECTION_BEGIN( "Trade Execution" );

Buy = Sell = Short = Cover = 0;
Buyflag = 0;
Shortflag = 0;
short_state = 0; // State machine for short logic
long_state = 0; // State machine for long logic
exit = Null;

b_exit = Min(floor( Max(Vw_Short_1, four_sd_down) / s_box_size ) * s_box_size, L_16_1);
bRisk = C - b_exit;
srisk = floor( Vw_Short_1 / s_box_size ) * s_box_size - C;
bl_up_dn = bl_up_up = bl_down_dn = bl_down_up = 0;

for( i = 2; i < BarCount; i++ )
{
    // --- Ratchet buffer lines for short logic ---
    buffer_line_down[i] = IIf( myC[i] > buffer_line_down[i]   AND buffer_line_down[i] < buffer_line_down[i - 1], buffer_line_down[i - 1], buffer_line_down[i] );
    buffer_line_up[i]   = IIf( myC[i] < buffer_line_up[i - 1] AND buffer_line_up[i]   > buffer_line_up[i - 1]  , buffer_line_up[i - 1]  , buffer_line_up[i] );

    bl_down_dn[i] = IIf( buffer_line_down[i] > buffer_line_down[i - 1], False, IIf( buffer_line_down[i] < buffer_line_down[i - 1], True, bl_down_dn[i - 1] ) );
    bl_up_dn[i] = IIf( buffer_line_up[i] > buffer_line_up[i - 1], False, IIf( buffer_line_up[i] < buffer_line_up[i - 1], True, bl_up_dn[i - 1] ) );

    bl_down_up[i] = IIf( buffer_line_down[i] < buffer_line_down[i - 1], False, IIf( buffer_line_down[i] > buffer_line_down[i - 1], True, bl_down_up[i - 1] ) );
    bl_up_up[i] = IIf( buffer_line_up[i] < buffer_line_up[i - 1], False, IIf( buffer_line_up[i] > buffer_line_up[i - 1], True, bl_up_up[i - 1] ) );

    ShortTrend[i] = bl_down_dn[i] && bl_up_dn[i];
    LongTrend[i] = bl_down_up[i] && bl_up_up[i];

    // --- LONG POSITION MANAGEMENT ---
    if( Buyflag > 0 )
    {
        t_exit = b_exit[i];
        exit[i] = exit[i - 1];

        if( O[i] > t_exit )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }

        if( Buyflag == 3 )  // Entry type with specific SL
        {
            t_exit = C[i - 1] - max_sl_Long[i];
            exit[i] = Max( t_exit, exit[i - 1] );
        }

        exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];
    }

    // --- EXIT TRIGGERS ---
    // SELL (Exit Long Position)
    if( Buyflag AND L[i] < exit[i] )
    {
        Sell[i] = True;
        Buyflag = 0;
        PThit = False;
        long_state = 2;
        SellPrice[i] = IIf( O[i] > exit[i], exit[i], C[i] );
    }

    // --- SHORT ENTRY LOGIC ---
    // if( C[i] < buffer_line_down[i] ) long_state = 0;

    // --- ENTRY TRIGGERS
    if( long_state == 2 AND candle_size[i] < 7 AND myC[i] > myO[i] AND NOT Buyflag AND myL[i]  + s_box_size[i] > Vw_Short_1[i] AND Lr_Short_1[i] > Vw_Short_1[i] )
    {
        Buy[i] = 1;
        Buyflag = True;
        long_state = 3; // Post-entry state
        exit[i] = b_exit[i];
    }
    else
        if( long_state == 1 AND C[i] < Vw_Short_1[i] )
        {
            long_state = 2; // Armed state
        }
        else
            if( long_state == 0 AND Lr_Short_1[i] > Vw_Short_1[i] )
            {
                long_state = 1; // Armed state
            }


    // --- SHORT POSITION MANAGEMENT ---
    if( Shortflag )
    {
        exit[i] = exit[i - 1];

        if( O[i] < Vw_Short_1[i] )
        {
            exit[i] = Min( Vw_Short_1[i], exit[i - 1] );
        }

        exit[i] = floor( exit[i] / s_box_size[i] ) * s_box_size[i];
    }

    // COVER (Exit Short Position)
    if( Shortflag AND C[i] > exit[i] )
    {
        Cover[i] = True;
        Shortflag = 0;
        short_state = 1; // Re-arm short state
    }

    // --- SHORT ENTRY LOGIC ---
    if( C[i] > buffer_line_up[i] ) short_state = 0;

    if( short_state == 1 AND myC[i] < buffer_line_down[i - 1] AND myH[i] < Vw_Short_1[i] AND candle_size[i] < 7 AND myC[i] < myO[i] AND NOT Shortflag )
    {
        Short[i] = 1;
        Shortflag = True;
        short_state = 3; // Post-entry state
        exit[i] = floor( Vw_Short_1[i] / s_box_size[i] ) * s_box_size[i];
        long_state = 0;
    }
    else
        if( short_state == 0 AND No_Above_MA[i - 1] > p_no_above AND myC[i] < Vw_Short_1[i] )
        {
            short_state = 1; // Armed state
        }

    state[i] = short_state + 10 * long_state;
}

myVar += ", state = " + state;

// Ensure final exit array is rounded
exit = myRound( exit );
BuyPrice = C;

// --- POSITION SIZING ---
bRisk = Max( 1, bRisk );
sRisk = Max( 1, sRisk );

bLots = Min( floor( eqAtRisk / ( RoundLotSize * bRisk ) ), floor( 7500000 / ( RoundLotSize * C ) ) );
sLots = Min( floor( eqAtRisk / ( RoundLotSize * sRisk ) ), floor( 7500000 / ( RoundLotSize * C ) ) );

PositionSize = IIf( Buy, bLots, IIf( Short, sLots, 0 ) ) * RoundLotSize;
SetPositionSize( PositionSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );

_SECTION_END();

//----------------------------------------------------------------
// SECTION: PLOTTING
//----------------------------------------------------------------
_SECTION_BEGIN( "Chart Display" );

// -- Background Color --
bkcolor = LastValue( IIf( mode, ColorBlend( colorCustom9, colorWhite ), ColorBlend( colorCustom12, colorWhite ) ) );
SetChartBkColor( bkcolor );

// -- Price and Exit Levels --
PlotOHLC( myO, myH, myL, myC, "", colorDefault, styleCandle );
Plot( IIf( exit, exit, Null ), "exit", colorBrown, styleStaircase | styleDashed, Null, Null, Null, -1 );
// Plot( IIf( Buy, PT, Null ), "Target (L)", colorGreen, styleDots, Null, Null, Null, 0 );

// -- Indicators from Short Logic --
Plot( buffer_line_up, "", ColorRGB( 68, 134, 238 ), styleThick );
Plot( buffer_line_down, "", ColorRGB( 205, 51, 51 ), styleThick );
Plot( Lr_Short, "", colorGrey40, styleThick, Null, Null, Null, 2 );
Plot( Vw_Short, "VWMA (Short)", IIf( Lr_Short > Vw_Short + boxSize, colorGreen, IIf( Lr_Short < Vw_Short - boxSize, colorPink, colorWhite ) ), styleThick | styleNoLabel );

// -- Indicators from Long Logic --
// Plot( Vw_Long , "", IIf( mode, colorGreen, colorRed ), styleThick | styleNoLabel, Null, Null, Null, 0 );
// Plot( Lr_Long_1, "", colorGrey40, styleThick, Null, Null, Null, 2 );

// -- Trade Shapes --
PlotShapes( shapeSmallUpTriangle * Buy, colorGreen, 0, myL, -10 );
PlotShapes( shapeSmallDownTriangle * Sell, colorDarkGreen, 0, myH, 10 );
PlotShapes( shapeSmallDownTriangle * Short, colorRed, 0, myH, 10 );
PlotShapes( shapeSmallUpTriangle * Cover, colorDarkRed, 0, myL, -10 );

// -- Title --
_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "Open %0.2f, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 )
                       ) );

_SECTION_END();