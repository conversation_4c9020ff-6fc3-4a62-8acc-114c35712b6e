//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi <PERSON>t=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>
// OptimizerSetEngine( "cmae" );

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

//=============================DISPLAY PARAMS======================================
atrMultiplier   = Param( "ATR_Multiplier", 2.5, 1, 10, 0.1 ); // BNF - 4.7
atrMultiplier 	= IIf( StrFind( Name(), "BANKNIFTY" ), Param( "ATR_Multiplier2", 1.3, 1, 10, 0.1 ), atrMultiplier ); // 2.5
atrPeriod       = Param( "ATR_Period", 5, 5, 10, 1 );
equityAtRisk    = Param( "Risk", 25000, 20000, 100000, 5000 );
//=================================================================================

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

tr = ATR( atrPeriod );
CalcPrice = ( H + L ) / 2;
calcTrend_proc( atrPeriod, tr, atrMultiplier, CalcPrice );

pp = IIf( StrFind( Name(), "BANKNIFTY" ), 1.1, 0.45 );
buffer_line_up = mybox( buffer_line_up );
buffer_line_down = mybox( buffer_line_down );

Buy		= !IsEmpty( Ref( buffer_line_up, -1 ) ) AND Ref( L, -1 ) >  Ref( buffer_line_up, -1 )
          AND C > Ref( C, -1 ) AND Ref( C, -1 ) > Ref( C, -2 ) AND Ref( C, -2 ) > Ref( C, -3 )
          AND C > O AND Ref( C, -1 ) > Ref( O, -1 ) AND Ref( C, -2 ) > Ref( O, -2 ) AND !firstBarOfTheDay AND TradingZone AND ROC( C, 3 ) < pp AND ROC( C, 10 ) > 0;
Sell 	= !IsEmpty( Ref( buffer_line_up, -1 ) ) AND L < Ref( buffer_line_up, -1 );
SellPrice = IIf( firstBarOfTheDay AND O < Ref( buffer_line_up, -1 ), C, Ref( buffer_line_up, -1 ) );
Buy		= ExRem( Buy, Sell );
Sell	= ExRem( Sell, Buy );


Short	= !IsEmpty( Ref( buffer_line_down, -1 ) ) AND Ref( H, -1 ) <  Ref( buffer_line_down, -1 ) AND buffer_line_down < Ref( buffer_line_down, -1 )
          AND C < Ref( C, -1 ) AND Ref( C, -1 ) < Ref( C, -2 ) AND Ref( C, -2 ) < Ref( C, -3 )
          AND C < O AND Ref( C, -1 ) < Ref( O, -1 ) AND Ref( C, -2 ) < Ref( O, -2 ) AND !firstBarOfTheDay AND TradingZone AND ROC( C, 3 ) > -0.5 AND ROC( C, 10 ) < 0;
Cover 	= !IsEmpty( Ref( buffer_line_down, -1 ) ) AND H > Ref( buffer_line_down, -1 ) OR( C > Ref( C, -1 ) AND Ref( C, -1 ) > Ref( C, -2 ) AND Ref( C, -2 ) > Ref( C, -3 ) AND Ref( C, -3 ) > Ref( C, -4 ) ) ;
CoverPrice = IIf( H > Ref( buffer_line_down, -1 ), Ref( buffer_line_down, -1 ), C );
Short	= ExRem( Short, Cover );
Cover	= ExRem( Cover, Short );

vlots = V / RoundLotSize;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );
Plot( buffer_line_up, "tu", ColorRGB( 68, 134, 238 ), styleThick );
Plot( buffer_line_down, " // td", ColorRGB( 205, 51, 51 ), styleThick );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -25 );
#include <Alert.afl>

bRisk = IIf( buffer_line_up, ( C - buffer_line_up ), 0 );
bLots	= Max( 0, floor( equityAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );

sRisk = buffer_line_down - C;
sLots	= Max( 0, floor( equityAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

// max_lots = IIf( StrFind( Name(), "BANKNIFTY" ), 9, 12 );
// max_lots = Min( floor( 200000 / ( RoundLotSize * C ) ), max_lots );
max_lots = 5000000 / ( RoundLotSize * C );
bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );
SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );

myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
myvar	+= StrFormat( ", ROC = %0.2f", ROC( C, 3 ) );

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );

_SECTION_END();

_SECTION_BEGIN( "AlgoFoxAuto" );
SetOption( "StaticVarAutoSave", 5 );
INSTR  = "FUTIDX";
PRDT   = "NRML";
ORTYPE = "Market";
STG    = "PRO1";
DN     = DateNum();
TSTIME = 91500;

// Get the symbol
SYM		= Name();

Zero	= NumToStr( 0, 1.0, False );
ENTIME	= TimeNum() >= TSTIME AND TimeNum() < SquareOffTime;

// Global variable for AlgoFox
AlgoFox = SYM + Interval( 0 );

Checkdn =  Nz( LastValue( StaticVarGet( AlgoFox + "lastdn" ) ) );

if( LastValue( DN ) > Checkdn AND NOT Nz( StaticVarGet( AlgoFox + "InitializationDone" ) ) )
{
    StaticVarSet( AlgoFox + "lastdn", LastValue( DN ), True );
    StaticVarSet( "UID", 1, True );
}

if( NOT Nz( StaticVarGet( AlgoFox + "InitializationDone" ) ) )
{
    StaticVarSet( AlgoFox + "autotrade", 1 );
}

dt 			= LastValue( BarIndex() );
Checkdt		= Nz( StaticVarGet( AlgoFox + "lastdt" ) );
LastBlots	= LastValue( Ref( blots, -1 ) * RoundLotSize );
LastSlots	= LastValue( Ref( slots, -1 ) * RoundLotSize );
LastBuy		= LastValue( Ref( Buy	, -1 ) );
LastShort	= LastValue( Ref( Short	, -1 ) );

// Trade Execution Logic
if( Status( "action" ) == actionIndicator AND NOT Nz( StaticVarGet( AlgoFox + "autotrade" ) ) )
{
    RTBuy = RTShort = RTSell = RTCover = 0;
}
else
{
    RTBuy	= LastValue( LastBuy   AND ENTIME AND dt > Checkdt AND LastBlots > 0 );
    RTShort = LastValue( LastShort AND ENTIME AND dt > Checkdt AND LastSlots > 0 );
    RTSell	= LastValue( Sell )  AND dt > Checkdt AND Nz( StaticVarGet( AlgoFox + "OL" ) );
    RTCover = LastValue( Cover ) AND dt > Checkdt AND Nz( StaticVarGet( AlgoFox + "OS" ) );
}

if( RTBuy )
{
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1, True );
    QTY = NumToStr( LastBlots, 1.0, False );
    StaticVarSet( AlgoFox + "LQT", LastBlots, True );

    FoxInteractive( UID, "BUY", SYM, ORTYPE + "|" + PRDT, "", Zero, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + ", BUY," + SYM + "," + ORTYPE + "|" + PRDT + ", ," + Ref( C, -1 ) + "," + QTY + "," + INSTR + "," + STG );
    Message = "Buy " + SYM + " @" + Ref( C, -1 ) + " At " + Now( 2 );
    StaticVarSetText( AlgoFox + "message", Message );
    StaticVarSet( AlgoFox + "lastdt", dt, True );
    tradetime = GetPerformanceCounter() / 1000;
    printf( "tradetime = " + tradetime );

    while( ( GetPerformanceCounter() / 1000 - tradetime ) < 2 )
    {
        printf( "GetPerformanceCounter " + GetPerformanceCounter() );
        ThreadSleep( 100 );
    }
}

if( RTShort )
{
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1, True );
    QTY = NumToStr( LastSlots, 1.0, False );
    StaticVarSet( AlgoFox + "SQT", LastSlots, True );

    FoxInteractive( UID, "SHORT", SYM, ORTYPE + "|" + PRDT, "", Zero, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + ",SHORT," + SYM + "," + ORTYPE + "|" + PRDT + ", ," + Ref( C, -1 ) + "," + QTY + "," + INSTR + "," + STG );
    Message = "Short " + SYM + " @" + Ref( C, -1 ) + " At " + Now( 2 );
    StaticVarSetText( AlgoFox + "message", Message );
    StaticVarSet( AlgoFox + "lastdt", dt, True );
    StaticVarSet( AlgoFox + "OS", True, True );
    tradetime = GetPerformanceCounter() / 1000;
    printf( "tradetime = " + tradetime );

    while( ( GetPerformanceCounter() / 1000 - tradetime ) < 2 )
    {
        printf( "GetPerformanceCounter " + GetPerformanceCounter() );
        ThreadSleep( 100 );
    }
}

if( RTSell AND !RTShort )
{
    QTY = NumToStr( StaticVarGet( AlgoFox + "LQT" ), 1.0, False );
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    FoxInteractive( UID, "SELL", SYM, ORTYPE + "|" + PRDT, "", LMS, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + "," + "SELL" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + " " + "," + LMS + "," + QTY + "," + INSTR + "," + STG );
    Message = "Sell " + SYM + " @" + LMS + " At " + Now( 2 );
    StaticVarSetText( "message", Message );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
    StaticVarSet( AlgoFox + "lastsdt", dt );
    StaticVarSet( AlgoFox + "OL", False );
    RTSell = False;
}

if( RTCover AND !RTBuy )
{
    QTY = NumToStr( StaticVarGet( AlgoFox + "SQT" ), 1.0, False );
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    FoxInteractive( UID, "COVER", SYM, ORTYPE + "|" + PRDT, "", Zero, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + "," + "COVER" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + " " + "," + Zero + "," + QTY + "," + INSTR + "," + STG );
    Message = "Cover " + SYM + " @" + Zero + " At " + Now( 2 );
    StaticVarSetText( "message", Message );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
    StaticVarSet( AlgoFox + "lastcdt", dt );
    StaticVarSet( AlgoFox + "OS", False );
    RTCover = False;
}


_SECTION_END();

_SECTION_BEGIN( "Message Box" );
procedure PlotTextEx1( String, x1, y1, x2, y2, pxWidth, pxHeight, HOR, VER )
{
    x1 = x1 * pxWidth / 100 + HOR;
    y1 = ( 100 - y1 ) * pxHeight / 100 + VER;
    x2 = x2 * pxWidth / 100 + HOR;
    y2 = ( 100 - y2 ) * pxHeight / 100 + VER;

    GfxRectangle( x1, y1, x2, y2 );
    GfxDrawText( String, x1, y1, x2, y2, 32 + 5 );
}


if( Status( "action" ) == actionIndicator )
{
    MSG = ParamToggle( "Message Window", "Hide|Show", 1 );
    HOR = Param( "Horizontal Position", 181, -1000, 2000, 1 );
    VER = Param( "Vertical Position", 0, -1000, 1000, 1 );
    WID = Param( "Width", 5, 1, 10, 0.1 );
    HET = Param( "Height", 4, 1, 10, 0.1 );
    FONT = Param( "Font Size", 9, 4, 12, 1 );
    pxHeight = Status( "pxchartheight" );
    pxWidth = Status( "pxchartwidth" );

    GfxSetBkMode( 1 );
    GfxSetOverlayMode( 0 );

    if( Nz( StaticVarGet( AlgoFox + "OL" ) ) )
        GfxSelectSolidBrush( colorGreen );
    else
        if( Nz( StaticVarGet( AlgoFox + "OS" ) ) )
            GfxSelectSolidBrush( colorDarkRed );
        else
            GfxSelectSolidBrush( colorBlueGrey );

    GfxSelectPen( colorWhite, 1 );
    GfxSetTextColor( colorWhite );
    GfxSetTextAlign( 0 );

    if( MSG )
    {
        GfxSelectFont( "Lucida Console", FONT, 600, False, False, 0 );
        PlotTextEx1( StaticVarGetText( AlgoFox + "message" ), 0, HET, 5 * WID, 0, pxWidth, pxHeight, HOR, VER );
    }
}

_SECTION_END();

_SECTION_BEGIN( "Button Trading" );

if( Status( "action" ) == actionIndicator )
{

    GfxSetBkMode( 1 );
    GfxSetOverlayMode( 0 );
    yy = Status( "pxcharttop" ) + 50;
    xx = Status( "pxchartleft" ) + 50;
    GfxSelectPen( colorWhite, 1 );
    GfxSetTextColor( colorBlack );
    GfxSetTextAlign( 0 );
    GfxSelectFont( "Lucida Console", 11, 500, False, False, 1 );
    cl = GetCursorMouseButtons() == 9;
    cx = GetCursorXPosition( 1 );
    cy = GetCursorYPosition( 1 );

    bex1 = xx;
    bey1 = yy;
    bex2 = bex1 + 60;
    bey2 = bey1 + 60;
    bxx1 = bex2 + 5;
    bxy1 = yy;
    sex1 = xx;
    sey1 = bey2 + 5;
    sex2 = sex1 + 60;
    sey2 = sey1 + 60;
    sxx1 = sex2 + 5;
    sxy1 = sey1;
    sxx2 = sxx1 + 60;
    sxy2 = sxy1 + 60;

    Chartid = SYM + "  " + NumToStr( Interval( 0 ) / 60, 1.0, False ) + "-Min";

    atx1 = sex1;
    aty1 = sey2 + 5;
    atx2 = sxx2;
    aty2 = sey2 + 40;

    if( Nz( StaticVarGet( AlgoFox + "autotrade" ) ) )
    {
        GfxSelectSolidBrush( colorBrightGreen );
        GfxRectangle( atx1, aty1, atx2, aty2 ) ;
        GfxTextOut( "AUTO ON", atx1 + 25, aty1 + 8 );
    }
    else
    {
        GfxSelectSolidBrush( colorRed );
        GfxRectangle( atx1, aty1, atx2, aty2 ) ;
        GfxTextOut( "AUTO OFF", atx1 + 25, aty1 + 8 );
    }

    if( cl AND cx > atx1 AND cy > aty1 AND cx < atx2 AND cy < aty2 )
    {
        StaticVarSet( AlgoFox + "autotrade", !Nz( StaticVarGet( AlgoFox + "autotrade" ) ) );

        if( StaticVarGet( AlgoFox + "autotrade" ) )
        {
            _TRACE( "Auto Trading Started: " + Chartid );
            AlertIf( 1, "SOUND C:\\Windows\\Media\\notify.wav", "Audio Alert", 1, 2, 1 );
            PopupWindow( "Auto Trade Started: " + Chartid, "Auto Trade", 1, -1, -1 );
        }
        else
        {
            _TRACE( "Auto Trading Stopped: " + Chartid );
            AlertIf( 1, "SOUND C:\\Windows\\Media\\notify.wav", "Audio Alert", 1, 2, 1 );
            PopupWindow( "Auto Trade Stopped: " + Chartid, "Auto Trade", 1, -1, -1 );
        }

        RequestTimedRefresh( 1 );
    }
}

_SECTION_END();

// OR( ( Ref( ROC( C, 3 ), -1 ) > 11 OR Ref( ROC( C, 2 ), -1 ) > 11 OR Ref( ROC( C, 1 ), -1 ) > 11 ) AND ROC( C, 1 ) < -4 AND NOT Ref( Buy, -1 ) ) OR ( ( HHV( Ref( H, -1 ), BarsSince( Buy ) + 1 ) - C ) / C > pp ) ;
// OR( ( Ref( ROC( C, 3 ), -1 ) < -11 OR Ref( ROC( C, 2 ), -1 ) < -11 OR Ref( ROC( C, 1 ), -1 ) < -11 ) AND ROC( C, 1 ) > 4 AND NOT Ref( Short, -1 ) ) OR ( ( HHV( Ref( H, -1 ), BarsSince( Buy ) + 1 ) - C ) / C > 0.04 ) ;
