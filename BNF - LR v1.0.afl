//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//-----------------------------------------------------
// Bank Nifty
//-----------------------------------------------------

#include <ReactionTrendSystem.afl>
SetChartBkColor( colorWhite ) ;
// OptimizerSetEngine( "cmae" );

Periods		= Param( "Periods", 110, 50, 600, 10 ); //470
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 6 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
eqAtRisk	= Param( "Risk", 25000, 15000, 35000, 5000 );

Vw 		= myround( VWMA2( C, Periods ) );
top 	= Max( LinearReg( High, Periods ), LinearReg( High, ceil( Periods / 2 ) ) );
bottom 	= Min( LinearReg( Low , Periods ), LinearReg( Low , ceil( Periods / 2 ) ) );
Lr 		= mybox( ( LinearReg( C, Periods ) + H + L ) / 3 );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( Lr > Vw + boxSize, Lr < Vw - boxSize );
Sd = myBox( StDev( C, Periods ) );

SetChartOptions( 0, chartShowArrows | chartShowDates );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

f = OptimizeNot("F", 0.8, 0.1, 4, 0.1);

up   	= Lr + f * SD;
down 	= Lr - f * SD;
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

pp = OptimizeNot( "L", 5, 5, 30, 1 );
L_5 = myboxF( LLV( L, pp ) );
H_5 = myboxC( HHV( H, pp ) );
H_5_1 = Ref( H_5, -1 );
L_5_1	= Ref( L_5, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
sRisk 	= Min( Vw_1, up_1 ) - ( ( IIf( L_5_1 > O, L_5_1, C ) + C ) / 2 );
bRisk 	= ( ( IIf( H_5 > 0 AND H_5_1 < O, H_5_1, C ) + C ) / 2 ) - Max( Vw_1, down_1 );
max_sl	= myRound( eqAtRisk / RoundLotSize );
curr_sl	= 0;

InBuy = 0;
InShort = 0;
BuyCondition   =     Ref( mode, - 1 ) AND L > Lr AND H > H_5_1 AND TradingZone;
ShortCondition = NOT Ref( mode, - 1 ) AND H < Lr AND L < L_5_1 AND TradingZone;

PB			= False;
BSTT		= Optimize( "BSTT", 120, 100, 300, 10 );
BST			= 0;

for( i = 1; i < BarCount; i++ )
{
    BST++;
    PT[i]   = PT[i - 1];

    if( Buyflag )
    {
        InBuy[i] = 1;
        t_exit =  Max(Max( Vw[i - 1], down[i - 1] ), H[i - 1] - max_sl/2);

        if( H[i] >= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Max( down[i - 1], C[i - 1] * ( 1 - StopLoss ) );
        }

        if( O[i] > t_exit )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        if( firstBarOfTheDay[i] )
        {
            if( exit[i] > L[i] )
            {
                exit[i] = L[i];
            }
        }

        exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Shortflag )
    {
        InShort[i] = 1;
        t_exit = Min( Vw[i - 1], up[i - 1] );

        if( L[i] <= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Min( up[i - 1], C[i - 1] * ( 1 + StopLoss ) );
        }

        if( O[i] < t_exit )
        {
            exit[i] = Min( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        if( firstBarOfTheDay[i] )
        {
            if( exit[i] < H[i] )
            {
                exit[i] = H[i];
            }
        }

        exit[i] = ceil( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Buyflag AND L[i] < exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Buyflag AND NOT mode[i - 1] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = C[i];
    }

    if( Buyflag AND !PB AND DTSquareOff[i] AND BST > BSTT )
    {
        Sell[i] = 1;
        PB		= True;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Shortflag AND H[i] > exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( ( O[i] < exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Shortflag AND mode[i - 1] )
    {
        Cover[i] 	= 1;
        Shortflag	= 0;
        CoverPrice[i] = C[i];
    }

    if( Shortflag AND !PB AND DTSquareOff[i] AND BST > BSTT )
    {
        Cover[i] = 1;
        PB		 = True;
        CoverPrice[i] = C[i];
    }
    if( NOT Buyflag AND BuyCondition[i] )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= Max( Vw[i - 1], down[i - 1] );
        Buyflag 	= 1;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i] 	= BuyP - exit[i];
        curr_sl		= bRisk[i];
        PT[i]		= BuyP * ( 1 + Target );
        PThit		= False;
        PB			= False;
        BST			= 0;
    }

    if( NOT Shortflag AND ShortCondition[i] AND NOT Cover[i] )
    {
        ShortP			= C[i];
        ShortPrice[i]	= ShortP;
        exit[i]			= Min( Vw[i - 1], up[i - 1] );
        Buyflag   		= 0;
        Shortflag 		= 1;
        Short[i]  		= 1;
        sRisk[i] 		= ( exit[i] - ShortP ); //eqAtRisk
        curr_sl			= sRisk[i];
        PT[i]			= ShortP * ( 1 - Target );
        PThit			= False;
        PB				= False;
        BST				= 0;
    }
}

exit = myRound( exit );
PT	 = myRound( PT );
bLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

sRisk = IIf(exit > C, exit - C, sRisk);

max_lots = Param( "Max Lots", 12, 1, 20, 1 );
max_lots = ceil( 4000000 / C / RoundLotSize );

bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC(H + boxSize), myboxF(L - boxSize) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
myvar	+= ", PT = " + PT + ", ATR = " + p_ATR;

bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

if( ParamToggle( "Show RT", "0|1" ) )
{
// draw levels from Reaction Trend System
// Plot( HBOP, "R2", colorOrange, styleStaircase | styleNoLabel );
    Plot( S1, "R1", colorRed, styleStaircase | styleNoLabel );
    Plot( B1, "S1", colorBrightGreen, styleStaircase | styleNoLabel );
// Plot( LBOP, "S2", colorGreen, styleStaircase | styleNoLabel );
}

Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, bkcolor ), styleStaircase | styleDashed, Null, Null, Null, -1 );
Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( IIf( TradingZone AND mode AND NOT InBuy, Max( H_5_1, S1 ), null ), "", colorBlue, styleDashed, Null, Null, Null, 1 );
Plot( IIf( TradingZone AND NOT mode AND NOT InShort, Min( L_5_1, B1 ), null ), "", colorRed, styleDashed, Null, Null, Null, 1 );
Plot( Vw , "", IIf( mode, colorGreen, colorPink ), ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( Lr, "", colorGrey40, ParamStyle( "Style" ) | styleThick | styleNoLabel, Null, Null, Null, 2 );
Plot( MA(Vw, 50) , "", colorBlack, ParamStyle( "Style" ) | styleThick | styleNoLabel );

myVar += ", boxSize = " + boxSize;

#include <Alert.afl>

_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "Open %0.2f, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 )
                       ) );
//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTIDX";
SYS  = "FUT";
#include<AlgoFoxAuto/AlgoFoxAuto.afl>
