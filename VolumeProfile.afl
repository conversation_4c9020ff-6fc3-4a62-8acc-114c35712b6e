#include <Common.afl>
SetChartBkColor(colorWhite);

lastBarOfTheDay			= ( TimeNum() == 152000 );

// Calculate Volume and Average Volume of Last Bar
Lots = V / RoundLotSize;
VolumeOfLastBar = Lots * lastBarOfTheDay;
AvgVolumeOfLastBar = Ref(round(MA(VolumeOfLastBar, 20 * 5) * 38), -1);

// Calculate Volume and Average Volume of First Bar
VolumeOfFirstBar = Lots * firstBarOfTheDay;
AvgVolumeOfFirstBar = Ref(round(MA(VolumeOfFirstBar, 20 * 5) * 38), -1);

// Calculate Highest High Value and other parameters
DaysToCheck = 4;
CompressedVolume = TimeFrameCompress(V, inDaily, compressHigh);
HighestHighValue = HHV(CompressedVolume, DaysToCheck);
HighValue = TimeFrameExpand(HighestHighValue, inDaily, expandLast) / RoundLotSize;

AvgVolumeOf50Bar = round(WMA(Ref(Lots, -1), 50));
High5 = HHV(Ref(H, -1), 5);
Low5 = LLV(Ref(L, -1), 5);
MultiHighest = Lots / HighValue;
MultiMovingAverage = IIf(!lastBarOfTheDay AND !firstBarOfTheDay, Lots / AvgVolumeOf50Bar, IIf(lastBarOfTheDay, VolumeOfLastBar / AvgVolumeOfLastBar, VolumeOfFirstBar/AvgVolumeOfFirstBar));

// Plot Volume Histograms
_SECTION_BEGIN("Volume");
VolumeColor = IIf(MultiHighest > 1, IIf(H > High5, colorBrightGreen, IIf(L < Low5, colorPink, colorWhite)), colorWhite);
Plot(MultiHighest, "", VolumeColor, styleHistogram, Null, Null, 0, 0, 2);

MAColor = IIf(!lastBarOfTheDay AND MultiMovingAverage > 2, colorBlue,
            IIf(lastBarOfTheDay AND MultiMovingAverage > 2, colorRed, colorWhite));
Plot(MultiMovingAverage, "", MAColor, styleHistogram);

// Display Variables in Title
myVar  = "Lots = " + NumToStr(Lots, 1) + ", MultiHighest = " + StrFormat("%.2f", MultiHighest) +
                  ", MultiMovingAverage = " + StrFormat("%.2f", MultiMovingAverage);
// myVar += ", AvgVolumeOfLastBar = " + StrFormat("%.2f", AvgVolumeOfLastBar) +", AvgVolumeOfFirstBar = " + StrFormat("%.2f", AvgVolumeOfFirstBar)+", AvgVolumeOf50Bar = " + StrFormat("%.2f", AvgVolumeOf50Bar);

_N(Title = StrFormat(EncodeColor(colorBlack) + myVar + ", " + "{{VALUES}}"));

_SECTION_END();
Filter = 1;
Addcolumn( V, "Volume" );
AddColumn( O, "O" );
AddColumn( H, "H" );
AddColumn( L, "L" );
