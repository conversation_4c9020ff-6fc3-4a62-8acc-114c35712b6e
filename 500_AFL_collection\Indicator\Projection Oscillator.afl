//------------------------------------------------------------------------------
//
//  Formula Name:    Projection Oscillator
//  Author/Uploader: Hans 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2004-03-14 17:37:56
//  Origin:          
//  Keywords:        
//  Level:           medium
//  Flags:           system,indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=344
//  Details URL:     http://www.amibroker.com/library/detail.php?id=344
//
//------------------------------------------------------------------------------
//
//  Developed by <PERSON>, Ph.D., the Projection Oscillator is a by-product
//  of his Projection Bands (see Projection Bands). The Projection Oscillator
//  is basically a slope-adjusted Stochastic. Where the Stochastic Oscillator
//  (see Stochastic Oscillator) shows the relationship of the current price to
//  its minimum and maximum prices over a recent time period, the Projection
//  Oscillator shows the same thing, but the minimum and maximum prices are
//  adjusted up/down by the slope of the price s regression line. This
//  adjustment makes the Projection Oscillator more responsive to short-term
//  price moves than an equi-period Stochastic.
//
//  Put another way, the Projection Oscillator shows where the current priceis
//  relative to the Projection bands. A value of 50 indicates that thecurrent
//  price is exactly in the middle of the bands. A value of 100indicates that
//  prices are touching the top band. A value of 0 indicatesthat prices are
//  touching the bottom band.
//
//  Interpretation
//
//  The Projection Oscillator can be used as both a short- and
//  intermediate-term trading oscillator depending on the number of time
//  periods used when calculating the oscillator. When displaying a short-term
//  Projection Oscillator(e.g., 10-20 days), it is popular to use a 3-day
//  trigger line.
//
//  There are several ways to interpret a Projection Oscillator.
//
//  Overbought/oversold. Buy when the oscillator falls below a specific level
//  (e.g., 20) and then rises above that level, and sell when the Oscillator
//  rises above a specific level (e.g., 80) and then falls below that level.
//  High values (i.e., above 80) indicate excessive optimism. Low values (i.e.,
//  below 20) indicate excessive pessimism.
//
//  However, before basing any trade off of strict overbought/oversold levels,
//  you should first qualify the trendiness of the market using indicators such
//  as r-squared (see r-squared) or CMO (see Chande Momentum Oscillator). If
//  these indicators suggest a non-trending market, then trades based on strict
//  overbought/oversold levels should produce the best results. If a trending
//  market is suggested, then you can use the oscillator to enter trades in the
//  direction of the trend.
//
//  Crossovers. Buy when the oscillator crosses above its trigger (dotted) line
//  and sell when the oscillator crosses below its trigger line. You may want
//  to qualify your trades by requiring that the crossovers occur above the 70
//  level or below the 30 level.
//
//  Divergences. You may consider selling if prices are making a series of new
//  highs and the oscillator is failing to surpass its previous highs. You may
//  consider buying if prices are making a series of new lows and the
//  oscillator is failing to surpass its previous low. You may qualify your
//  trades by requiring that the divergence occur above the 70 level orbelow
//  the 30 level.
//
//  Thanks to Ed and Aequalsz from Amibroker Yahoo group for develoment of
//  function and better coding.
//
//------------------------------------------------------------------------------

/* Projection Oscillator - Version 2.0 - 
Hans -  2004 march 14

*/

n = Param("Periods",12,5,50,1);
av = Param("Average",5,2,20,1);

n = Optimize("Periods",n,5,50,1);
av = Optimize("Average",av,2,20,1);

function ProjOsc(n) { 

// Slope of High {n period regression line of High)} 
SlopeHigh = ((n * (Sum( Cum(1) * High, n))) - (Sum( Cum(1),n) * ( 
Sum(High, n)))) / ((n * Sum( Cum(1) ^ 2 , n)) - (Sum(Cum(1),n) ^ 
2)); 

//Slope of Low {n period regression line of Low} 
SlopeLow = ((n * (Sum( Cum(1) * Low, n))) - (Sum( Cum(1), n) * ( 
Sum(Low, n)))) / ((n * Sum( Cum(1)^ 2, n)) - ( Sum(Cum(1),n) ^ 
2)); 

//Upper Projection Band 
UpProjBand = 0; 
for (i=0; i<n-1; i++) 
{ 
UpProjBand = 
Max(Max(Ref(High,-i)+i*slopehigh,Ref(High,-i-1)+(i+1)*slopehigh),UpProjBand); 
} 

//Lower Projection Band 
LoProjBand = 10000; 
for (i=0; i<n-1; i++) 
{ 
LoProjBand = 
Min(Min(Ref(Low,-i)+i*slopelow,Ref(Low,-i-1)+(i+1)*slopelow),LoProjBand); 
} 

//Projection Oscillator 
ProOsc = 100 * (Close - LoProjBand) / (UpProjBand - LoProjBand); 

return ProOsc; 

} 
aa= ProjOsc(n);
bb= MA(ProjOsc(n),av);

Plot(aa,"Projection Osc",colorGreen,styleLine); 
Plot(bb,"MA ProjOsc",colorRed,styleLine); 

Buy = Cross (aa,bb);
Sell = Cross (bb,aa);
Cover=Buy;
Short=Sell;