// Downloaded From https://www.WiseStockTrader.com
//Author :  <PERSON><PERSON>
//Credits to all authors , code adopted from open source authors and modified as per real time needs!
_SECTION_BEGIN( "Total OI" );
#include <Common.afl>
#include <Options.afl>

SetChartBkColor( colorWhite ); // color of outer border
SetOption( "DisableRuinStop", True );

tn = TimeNum();
TradingZone = ( tn > 93200 AND tn <= 150600 );

V1 = Ref( V, -1 );
C1 = Ref( C, -1 );
OI1 = Ref( OI, -1 );
ROC_price = ROC( C, 5 );
ROC_vol = MA( V, 5 ) / MA( V1, 5 );
ROC_oi = MA( OI, 5 ) / MA( OI1, 5 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );

m_fill = OptimizeNot( "fill", 10, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;

pp = Optimize("pp", 25, 5, 50, 5);

MAV = MA(Ref(V, -1), 25);
HV = HHV(Ref(V, -1), pp);
HHV5 = myboxC(HHV( Ref( H, -1 ), 5 ));
LLV5 = myboxF(LLV( Ref( L, -1 ), 5 ));

vol_up =  V > MAV;
price_up = C > HHV5;
oi_up = Flip(oi_Above, oi_Below); 
vol_dn = V < HV AND V < MAV;
price_dn = C < LLV5;
oi_dn = Flip(oi_Below, oi_Above);
oi_dn = oi_dn AND Ref(oi_dn, -1);

// myVar += ", P (" +  price_up + ", " + price_dn + "), V (" + vol_up + ", " + vol_dn + "), I (" + oi_up + ", " + oi_dn + ")";

long_buildup = price_up AND vol_up AND oi_dn;
short_buildup = price_dn AND vol_up AND oi_up;
long_unwinding = price_up AND vol_dn AND oi_up;
short_covering = price_dn AND vol_dn AND oi_dn;
long_setup = long_buildup;
short_setup = short_buildup;

trendCOLOR = IIf( long_buildup, colorGreen,
                  IIf( short_buildup, colorRed,
                       IIf( long_unwinding, colorBlue,
                            IIf( short_covering, colorGold, colorWhite ) ) ) );


vlots = V / RLS;

BuyExit = myboxF( LLV5 - boxSize );
ShortExit = myBoxC( Ref(H, -1) + 2.5 * boxSize );
Buy   = long_setup  AND L > 11 AND H < 300 AND TradingZone AND vlots > 100;
Short = short_setup AND L > 11 AND H < 300 AND TradingZone AND vlots > 100;
Sell = Cover = 0;
InBuy = InShort = 0;
BuyPrice = SellPrice = ShortPrice = CoverPrice = C;
BuyP  = ShortP = 0;
ShortI  = BuyI = 0;

for( i = 10; i < BarCount; i++ )
{
    if( Buy[i] AND NOT InBuy )
    {
        InBuy = 1;
        // BuyExit[i] = Max( floor( L[i] / boxSize[i] ) * boxSize[i] - 3 * boxSize[i], BuyExit[i] );
        BuyP = C[i];
        BuyI = i;
        InShort = 0;
    }

    if( InBuy AND O[i] > Max( BuyExit[i], BuyExit[i - 1] ) )
    {
		BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
        ShortExit[i] = Null;
    }

    if( L[i] < BuyExit[i] OR !TradingZone[i] )
    {
        Sell[i] = 1;
        InBuy = 0;
    }

    if( Short[i] AND NOT InShort )
    {
        InShort = 1;
        // ShortExit[i] = Min( ceil( H[i] / boxSize[i] ) * boxSize[i] + 2.5 * boxSize[i], ShortExit[i] );
        ShortP = C[i];
        ShortI = i;
        InBuy = 0;
    }

    if( InShort AND O[i] < ShortExit[i - 1] )
    {
        ShortExit[i] = Min( ShortExit[i], ShortExit[i - 1] );
		BuyExit[i] = Null;
    }

    if( H[i] > ShortExit[i] OR !TradingZone[i] OR long_buildup[i] )
    {
        Cover[i] = 1;
        InShort = 0;
    }
    
    if(NOT TradingZone[i] OR ( NOT InShort AND NOT InBuy) ) {
        ShortExit[i] = Null;
		BuyExit[i] = Null;
    }
}

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleCandle );
Plot( ShortExit, "SE", colorGrey40, styleDashed );
Plot( BuyExit, "BE", colorGrey40, styleDashed );

/*
Buy = Buy AND isITM AND ITMRange == 0 AND daysToExpiry < 7;
Short = Short AND isITM AND ITMRange == 0 AND daysToExpiry < 7;
*/

Sell = Sell OR Short;
Cover = Cover OR Buy;

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );
equityAtRisk = 3000;

bRisk = BuyPrice - BuyExit;
bLots	= Min(4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) ) ));

sRisk = ShortExit - ShortPrice;
sLots	= Min(4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) ) ));
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", SellPrice, CoverPrice );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{DATE}}, "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + "OI %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V, OI
                       ) );


_SECTION_END();