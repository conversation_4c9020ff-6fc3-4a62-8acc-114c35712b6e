//------------------------------------------------------------------------------
//
//  Formula Name:    Elder Triple Screen Trading System  -- LONG
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2005-07-13 21:27:37
//  Origin:          Derrived from the trading books, "Trading For A Living" and "Come Into My Trading Room" by <PERSON>.
//  Keywords:        Triple Screen, <PERSON>,Pullback scan
//  Level:           semi-advanced
//  Flags:           system,exploration
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=519
//  Details URL:     http://www.amibroker.com/library/detail.php?id=519
//
//------------------------------------------------------------------------------
//
//  This scan finds candidates by the Weekly MACD Historgam slope, and the
//  Daily 2 Period Force Index dipping above or below it's Zero Line. Plot a
//  Weekly 26 Period EMA to help confirm the weekly direction. It should be
//  rising along with an uptick on the Weekly MACD Histogram to go long.
//  However, Elder writes that divergences in the MACD Histogram override the
//  EMA. The Daily 2 Period Force Index will be below it's Zero Line. Look for
//  the stock to pullback to around it's Daily 13 Period EMA. Also use the
//  Daily 22 Period EMA to confirm the direction of the daily trend. Do the
//  opposite for shorts. Use the Long/Short EMA Weekly Direction Tabs as
//  filters to cull through the scan to display the Weekly EMA only going in
//  the intended trading direction. Use the Long/Short Elder Ray Tabs
//  (BullPower AND BearPower) to fine tune the entry signals. This tab is best
//  used when in agreement with the Long/Short EMA Weekly Direction Tabs. A 50
//  Period EMA > 100000 is used to filter Volume. A minimum of a 5 point run in
//  one month is used as a filter for a stock's range. This scan is best used
//  as an Exploration. The best sorts are found using the Force Index 13 Period
//  Weekly tab with the highest value first.
//
//------------------------------------------------------------------------------

// Elder Triple Screen Trading System. Elder Long.
// Coded by Dennis Skoblar 7/05/2005.
// Derrived from "Trading For A Living" and "Come Into My Trading Room" by Alexander Elder.

// This scan finds candidates by the Weekly MACD Historgam slope, and the Daily 2 Period Force Index dipping above or below it's Zero Line. Plot a Weekly 26 Period EMA to 
// help confirm the weekly direction. It should be rising along with an uptick on the Weekly MACD Histogram to go long. However, Elder writes that divergences in the MACD 
// Histogram override the EMA. The Daily 2 Period Force Index will be below it's Zero Line. Look for the stock to pullback to around it's Daily 13 Period EMA. Also use the
// Daily 22 Period EMA to confirm the direction of the daily trend. Do the opposite for shorts. Use the Long/Short EMA Weekly Direction Tabs as filters to cull through the
// scan to only display the Weekly EMA going in the intended trading direction. Use the Long/Short Elder Ray Tabs (BullPower AND BearPower) to fine tune the entry signals. 
// This tab is best used when in agreement with the Long/Short EMA Weekly Direction Tabs. A 50 Period EMA > 100000 is used to Filter Volume. A minimum of a 5 point run in 
// one Month is used as a Filter for a stock's range. This scan is best used as an Exploration. The best sorts are found using the Force Index 13 Period Weekly tab with the 
// highest value first.

TimeFrameSet( inWeekly );
WeeklyMACD = MACD(12,26) - Signal(12,26,9);
WeekHistRising = Ref(WeeklyMACD, -1) < Ref(WeeklyMACD, 0);
WeekHistFalling = Ref(WeeklyMACD, -1) > Ref(WeeklyMACD, 0);
FIWeekly = EMA(((C-Ref(C,-1))*V),13);
WeeklyForceIndexLong = FIWeekly > 0;
WeeklyForceIndexShort = FIWeekly < 0;
TimeFrameRestore();

// Weekly criteria
MACDLongW = TimeFrameExpand( WeekHistRising, inDaily );
MACDShortW= TimeFrameExpand( WeekHistFalling, inDaily );
FILongW = TimeFrameExpand( WeeklyForceIndexLong, inDaily );
FIShortW = TimeFrameExpand( WeeklyForceIndexShort, inDaily );

// Daily criteria
FIDaily = EMA(((C-Ref(C,-1))*V),2);
FILongD = FIDaily < 0;
FIShortD = FIDaily > 0;
VFilter = EMA(V,50) > 100000;
TenTwentyFilter = HHV(H,20)-LLV(L,20); // How much price has gone in one month (>=10 points preferable)
FiftyDayHVFilter = round(StDev(log(C/Ref(C,-1)),50)*100*sqrt(256)); // One year volotility (>=40 preferable)
bullpower= High - EMA(Close,13); 
bearpower= Low - EMA(Close,13); 

// Scan criteria
ElderLong = MACDLongW AND FILongD;
ElderShort = MACDShortW AND FIShortD;

// Columns for exploration

NumColumns = 10;

Column0 = FullName();     
Column0Name = "Ticker name";

Column1 = " ";
Column1Name =" ";

Column2 = ElderLong;
Column2Name = "Long";

Column3 = ElderLong AND EMA(C,130) > Ref(EMA(C,130),-5);
Column3Name = "Long EMA Weekly Direction";

Column4 = Column3 AND (bearpower < 0 AND bullpower > 0);
Column4Name = "Long Elder Ray Filter";

Column5 = " ";
Column5Name =" ";

Column6 = FIWeekly;
Column6Name = "Force Index 13 Period Weekly";

Column7 = FIDaily;
Column7Name = "Force Index 2 Period Daily";

Column8 = TenTwentyFilter;
Column8Name = "One Month Point Range";

Column9 = FiftyDayHVFilter;
Column9Name = "Historical Volotility 50 Day";

AddTextColumn( IndustryID(1), "Industry" );

AddTextColumn( MarketID(1), "Market" );

// Filters
Filter = TenTwentyFilter > 5 AND VFilter AND ElderLong;
Buy = ElderLong;
//Sell = ElderShort;

// ---------------------------------------------------------------------------------------------------------------------- 

// Charts -- (cut and paste the chart/indicator to it's own window with it's own file name and remove the Remark Slashes "//", except
// for the first line, this line desctibes the chart function. Example...leave "//Weekly Bar Chart" from the following first line as as.

// Weekly Bar Chart
//_SECTION_BEGIN("Weekly Graph");
//TimeFrameSet( inWeekly );
//wo = O;
//wh = H;
//wl = L;
//wc = C;
//TimeFrameRestore(); 
//PlotOHLC( wo, wh, wl, wc, "Weekly Close", colorCustom9, styleBar );
//_SECTION_END();

//_SECTION_BEGIN("EMA");
//P = ParamField("Price field",-1);
//Periods = Param("Periods", 15, 2, 200, 1, 10 );
//Plot( EMA( P, Periods ), _DEFAULT_NAME(), ParamColor( "Color", colorYellow ), ParamStyle("Style") ); 
//_SECTION_END();

// ----------------------------------------------------------------------------------------------------------------------

// Weekly Force Index 13 Chart
//_SECTION_BEGIN("Weekly Force Index Thirteen Period");
//TimeFrameSet( inWeekly ); 
//FI=EMA(((C-Ref(C,-1))*V),13);
//TimeFrameRestore();
//Plot(FI,"FI",colorCustom11,styleLine);
//Plot(0,"ZERO LINE",colorWhite,styleThick);
//_SECTION_END();

// ----------------------------------------------------------------------------------------------------------------------

// Weekly MACD Histogran
//_SECTION_BEGIN("Weekly MACD");
//TimeFrameSet( inWeekly );
//MACDw = MACD( 12, 26 ) - Signal( 12, 26, 9 );
//MACDwLINE =  MACD( 12, 26 ) ;
//MACDwSignal = Signal( 12, 26, 9 );
//TimeFrameRestore();
//Plot(MACDw,"MACD Weekly",colorYellow,styleHistogram);
//Plot(MACDwLINE,"MACD Weekly Line",colorRed,styleLine);
//Plot(MACDwSignal,"MACD Weekly Signal Line",colorBlue,styleLine);
//_SECTION_END();

// ----------------------------------------------------------------------------------------------------------------------

// Daily Bar Chart
//_SECTION_BEGIN("Elder Daily Chart with Envelope");
//SetChartOptions(0,chartShowArrows|chartShowDates);
//Plot( EMA(C,13),"EMA(13)",colorRed,styleLine);
// LookBkPd = 100 ;
// AvgPd = 22 ;
// ExternalBarPct = 15 ;
// ConvergePct = 2 ;
// Middle = EMA( C,AvgPd ) ;
// Rng = HHV( H,LookBkPd ) - LLV( L,LookBkPd ) ;
// X = Rng ;
// deltaX = X/2 ;
// do
// {
// Over = H > Middle + X ;
// Under = L < Middle - X ;
// OuterPct = 100*( Sum( Over, LookBkPd ) + Sum( Under, LookBkPd )
// )/LookBkPd ;
// OP = LastValue(OuterPct) ;
// X=X+sign( OP - ExternalBarPct )*deltaX ;
// deltaX = deltaX/2 ;
// }while ( abs( OP - ExternalBarPct ) > ConvergePct ) ;
// Plot( Middle, "MA", colorYellow, styleLine|styleNoTitle ) ;
// Plot( Middle+X, "MA", colorSkyblue, styleDashed|styleNoTitle ) ;
// Plot( Middle-X, "MA", colorSkyblue, styleDashed|styleNoTitle ) ;
//_N(Title = StrFormat("{{NAME}} - {{INTERVAL}} {{DATE}} Open %g, Hi %g, Lo %g, Close %g (%.1f%%) {{VALUES}}", O, H, L, C, SelectedValue( ROC( C, 1 ) ) ));
//Plot( C, "Close", ParamColor("Color", colorCustom9 ), styleBar | ParamStyle("Style") | GetPriceStyle() ); 
//_SECTION_END();

// ----------------------------------------------------------------------------------------------------------------------

// Daily Force Index 2 Period
//_SECTION_BEGIN("Force Index 2 Day");
//FI=EMA(((C-Ref(C,-1))*V),2);
//Plot(FI,"FI",colorCustom11,styleLine);
//Plot(0,"ZERO LINE",colorWhite,styleThick);
//_SECTION_END();

// ----------------------------------------------------------------------------------------------------------------------

// Daily MACD Histogram
//_SECTION_BEGIN("MACD");
//r1 = Param( "Fast avg", 12, 2, 200, 1 );
//r2 = Param( "Slow avg", 26, 2, 200, 1 );
//r3 = Param( "Signal avg", 9, 2, 200, 1 );
//Plot( ml = MACD(r1, r2), StrFormat(_SECTION_NAME()+"(%g,%g)", r1, r2), ParamColor("MACD color", colorRed ), ParamStyle("MACD style") );
//Plot( sl = Signal(r1,r2,r3), "Signal" + _PARAM_VALUES(), ParamColor("Signal color", colorBlue ), ParamStyle("Signal style") );
//Plot( ml-sl, "MACD Histogram", ParamColor("Histogram color", colorBlack ), styleNoTitle | ParamStyle("Histogram style", styleHistogram | styleNoLabel, maskHistogram ) );
//_SECTION_END();

// ----------------------------------------------------------------------------------------------------------------------

// Daily BullPower
//_SECTION_BEGIN("Bull Power EMA");
///*Bull Power*/
//Lookback = Param ("EMA Lookback",13);
//BullPower = High - EMA(Close,Lookback);
//Plot (BullPower, "", ParamColor ("Color", colorCustom11), styleHistogram );
//Title = Name() + "   " + Date() + "     Bull Power" + WriteVal (Lookback, 3.0) + " Day:   " + WriteVal (BullPower, 5.3);
//GraphXSpace = 5;
//_SECTION_END();

// ----------------------------------------------------------------------------------------------------------------------

// Daily BearPower
//_SECTION_BEGIN("Bear Power EMA");
///*Bear Power*/
//Lookback = Param ("EMA Lookback", 13);
//BearPower = Low - EMA(Close,Lookback);
//Plot (BearPower, "", ParamColor ("Color", colorRed), styleHistogram );
//Title = Name() + "   " + Date() + "     Bear Power" + WriteVal (Lookback, 3.0) + " Day:   " + WriteVal (BearPower, 5.3);
//GraphXSpace = 5;
//_SECTION_END();

// End ---------------------------------------------------------------------------------------------------------------------