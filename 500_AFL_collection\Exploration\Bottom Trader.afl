//------------------------------------------------------------------------------
//
//  Formula Name:    Bottom Trader
//  Author/Uploader: <PERSON><PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2006-10-01 06:48:29
//  Origin:          Bollinger Bands
//  Keywords:        Exploration
//  Level:           basic
//  Flags:           exploration
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=726
//  Details URL:     http://www.amibroker.com/library/detail.php?id=726
//
//------------------------------------------------------------------------------
//
//  If a trend is your friend, then you are thrice as lucky to have three
//  buddies in Bollinger Bands. The bands and their MA are not only great
//  trading tools, but superb trend indicators as well. Here, I have for you a
//  Bollinger Bottom Band Exploration (needless to say you can customize it for
//  O, H, L or C). I have set it to filter stocks that have crossed the bottom
//  band at L. If you are conservative with you money then this exploration is
//  ideal for you. Explore and ENTER the trade. All you need is a good eye and
//  I assure you, you would seldom lose money on a trade.
//
//------------------------------------------------------------------------------

P = ParamField("Price field",-1);
Periods = Param("Periods", 20, 2, 100, 1 );
Width = Param("Width", 2, 0, 10, 0.05 );


Filter = L <  BBandBot( P, Periods, Width );

P  =  ((H + L + C) / 3);
R1  =  ((2 * P) - L);
S1  =  ((2 * P)  - H);
R2  =  ((P - S1) + R1);
S2  =  (P - (R1 - S1));

AddTextColumn( FullName(), "Name");
AddColumn( Close, "Close" );
AddColumn (RSI(), "RSI",1.2);
AddColumn( V, "Volume",1 );
AddColumn( r2, "R 2", 1.2);
AddColumn( r1, "R 1", 1.2);
AddColumn( P, "Pivot", 1.2);
AddColumn( S1, "S 1", 1.2);
AddColumn( S2, "S 2", 1.2);