//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Tata Motors, Reliance
//-----------------------------------------------------

#include <ReactionTrendSystem.afl>
SetChartBkColor( colorWhite ) ;
OptimizerSetEngine( "cmae" );

Periods		= Param( "Periods", 100, 50, 500, 5 ); //470
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= Param( "Target", 6, 4, 7, 0.5 ) / 100;
eqAtRisk	= Param( "Risk", 20000, 20000, 100000, 5000 );

Vw 		= myround( VWMA2( C, Periods ) );
Lr 		= mybox( ( LinearReg( C, Periods ) ) );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( Lr > Vw + boxSize, Lr < Vw - boxSize );
Sd = StDev( C, Periods );

lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

ATR_mul = 1;
f = OptimizeNot( "F", 2.4, 1.0, 6, 0.1 );
ATR_f = ATR( Periods );

up   	= Lr + f * Max( ATR_f * ATR_mul, Sd );
down 	= Lr - f * Min( ATR_f * ATR_mul, Sd );
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

pp = Optimize( "L", 30, 5, 100, 5 );
L_25 = myboxF( LLV( L, pp ) );
H_25 = myboxC( HHV( H, pp ) );
H_25_1 = Ref( H_25, -1 );
L_25_1	= Ref( L_25, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
sRisk 	= Min( Vw_1, up_1 ) - ( ( IIf( L_25_1 > O, L_25_1, C ) + C ) / 2 );
bRisk 	= ( ( IIf( H_25 > 0 AND H_25_1 < O, H_25_1, C ) + C ) / 2 ) - Max( Vw_1, down_1 );
max_sl	= myRound( eqAtRisk / RoundLotSize );
curr_sl	= 0;

//------------------------------------------------------
// Entry and exit conditions
//------------------------------------------------------
BuyCondition   =     Ref( mode, - 1 ) AND Ref( L, -1 ) > Ref( Lr, -2 ) AND H > H_25_1 AND TradingZone;
ShortCondition = NOT Ref( mode, - 1 ) AND Ref( H, -1 ) < Ref( Lr, -1 ) AND L < L_25_1 AND TradingZone;
InBuy = InShort = 0;

for( i = 1; i < BarCount; i++ )
{
    if( Buyflag )
    {
        InBuy[i] = 1;
        t_exit =  Max( Vw[i - 1], down[i - 1] );

        if( H[i] >= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Max( down[i - 1], C[i - 1] * ( 1 - StopLoss ) );
        }

        if( O[i] > t_exit )
        {
            exit[i] = Max( floor( t_exit / boxSize[i] ) * boxSize[i], exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        PT[i]   = PT[i - 1];
        // exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Shortflag )
    {
        InShort[i] = 1;
        t_exit = Min( Vw[i - 1], up[i - 1] );

        if( L[i] <= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Min( up[i - 1], C[i - 1] * ( 1 + StopLoss ) );
        }

        if( O[i] < t_exit )
        {
            exit[i] = Min( ceil( t_exit / boxSize[i] ) * boxSize[i], exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        PT[i]   = PT[i - 1];
        // exit[i] = ceil( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Buyflag AND L[i] < exit[i] AND O[i] > exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Buyflag AND C[i - 1] < exit[i - 1] AND O[i - 1] < exit[i - 1] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = O[i];
    }

    /*
        if( Buyflag AND NOT mode[i - 1] )
        {
            Sell[i] = 1;
            Buyflag = 0;
            SellPrice[i] = C[i];
        }
    */
    if( Shortflag AND H[i] > exit[i] AND O[i] < exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( ( O[i] < exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Shortflag AND C[i - 1] > exit[i - 1] AND O[i - 1] > exit[i - 1] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = O[i];
    }

    /*
        if( Shortflag AND mode[i - 1] )
        {
            Cover[i] 	= 1;
            Shortflag	= 0;
            CoverPrice[i] = C[i];
        }
    */
    if( NOT Buyflag AND BuyCondition[i] )
    {
        BuyP 		= IIf( H_25_1[i] > O[i] AND NOT firstBarOfTheDay[i], H_25_1[i], C[i] );
        BuyPrice[i] = BuyP;
        exit[i]		= Max( Vw[i - 1], down[i - 1] );
        Buyflag 	= 1;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i] 	= BuyP - exit[i];
        curr_sl		= bRisk[i];
        PT[i]		= BuyP * ( 1 + Target );
        PThit		= False;
    }

    if( NOT Shortflag AND ShortCondition[i] )
    {
        ShortP			= IIf( L_25_1[i] < O[i] AND NOT firstBarOfTheDay[i], L_25_1[i], C[i] );
        ShortPrice[i]	= ShortP;
        exit[i]			= Min( Vw[i - 1], up[i - 1] );
        Buyflag   		= 0;
        Shortflag 		= 1;
        Short[i]  		= 1;
        sRisk[i] 		= ( exit[i] - ShortP ); //eqAtRisk
        curr_sl			= sRisk[i];
        PT[i]			= ShortP * ( 1 - Target );
        PThit			= False;
    }
}

exit = myRound( exit );
PT	 = myRound( PT );
myVar += ", PT = " + PT;
bLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

max_lots = 4;
max_lots = IIf( StrFind( Name(), "TATAMOTORS" ) > 0, 3, max_lots );

bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );

bRisk = IIf( bRisk == 0 AND exit < C, C - exit, brisk );
sRisk = IIf( sRisk == 0 AND exit > C, exit - C, sRisk );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
myVar 	+= ", boxSize = " + boxSize;

bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

//------------------------------------------------------
// Plotting and Alerts
// draw levels from Reaction Trend System
//------------------------------------------------------
if( ParamToggle( "Show RT", "0|1" ) )
{
    Plot( B1, "", colorBrightGreen, styleStaircase | styleNoLabel );
    Plot( S1, "", colorRed, styleStaircase | styleNoLabel );
    // Plot( HBOP, "", colorOrange, styleStaircase | styleNoLabel );
    // Plot( LBOP, "", colorGreen, styleStaircase | styleNoLabel );
}

Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, colorLightGrey ), styleStaircase | styleDashed |  styleNoLabel * IIf( LastValue( exit ), 0, 1 ), Null, Null, Null, -1 );
Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( IIf( TradingZone AND     Ref( mode, - 1 ) AND Ref( L, -1 ) > Ref( Lr, -2 ) AND NOT InBuy  , Ref( H_25, -1 ), null ), "", colorBlue, styleDashed, Null, Null, Null, 1 );
Plot( IIf( TradingZone AND NOT Ref( mode, - 1 ) AND Ref( H, -1 ) < Ref( Lr, -1 ) AND NOT InShort, Ref( L_25, -1 ), null ), "", colorRed , styleDashed, Null, Null, Null, 1 );
Plot( Vw , "", IIf( mode, colorGreen, colorBrown ), ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( Lr, "", colorGrey40, ParamStyle( "Style" ) | styleThick | styleNoLabel, Null, Null, Null, 2 );

#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );
_SECTION_END();

//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTSTK";
SYS  = "FUT";
#include<AlgoFoxAuto/AlgoFoxAuto.afl>
