#include <Common.afl>
_SECTION_BEGIN( "Price" );
SetChartOptions( 0, chartShowArrows | chartShowDates );
Plot( C, "Close", ParamColor( "Color", colorDefault ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );
_SECTION_END();

myVar = "";

//ORB

_SECTION_BEGIN( "ORB" );

newday = Day() != Ref( Day(), -1 ) ; //Identifies the beginning of new day

VMA = V / MA( Ref( V, -1 ), 16 );
vCheck = VMA > 3 AND C < O;
vCheckD = ( Cum( vCheck ) - ValueWhen( lastBarOfTheDay, Cum( vCheck ) ) );

SLprc = Param( "SLprc", 0.42, 0.05, 1, 0.01 );
IBL = myBoxF( Min( ValueWhen( vCheck , L ), LLV( Ref( L, -1 ), 5 ) ) );
Condition1 = C > Ref( C, -1 ) AND Ref( C, -1 ) > Ref( C, -2 ) AND Ref( C, -2 ) > Ref( C, -3 ) AND C > O AND Ref( C, -1 ) > Ref( O, -1 ) AND Ref( C, -2 ) > Ref( O, -2 );
Condition2 = C < Ref( C, -1 ) AND Ref( C, -1 ) < Ref( C, -2 ) AND Ref( C, -2 ) < Ref( C, -3 );
IBH = Max( ValueWhen( Condition1, myboxC( H ) ), ValueWhen( Condition2, myboxC( Ref( H, -3 ) ) ) );

//Timing functions

//Entry time should be greater than 10:15 and 2:30 PM
Starttime = TimeNum() > 100000 ;
endtime = TimeNum() < 150000 ;
EODsquareoff = TimeNum() > 151500 ;

//Define the strategy rules
Short = 0;
ShortPrice = 0;
Cover = 0;
CoverPrice = 0;
Buy = 0;
Sell = 0;
Buyflag = 0;
PT = 0;
singleT = True;

myVar += ", Sell = " + Sell;

BuySL = Max( IBL, myboxF( LLV( Ref( L, -1 ), 10 ) ) - round( boxSize ) );
BuyCondition = H > Ref( IBH, -1 ) AND starttime AND endtime;

Plot( IIf( starttime AND endtime, IBH, null ), "", colorBlue, styleDashed, Null, Null, Null, 1 );
Plot( IBL, "IBL", colorGreen );


for( i = 1; i < BarCount; i++ )
{
    if( NOT newday[i] AND Buyflag )
    {
        BuySL[i] = Max( BuySL[i - 1], BuySL[i] );
        PT[i]  = PT[i - 1];
    }

    if( newday[i] )
    {
        singleT = False;
    }

    if( ( EODsquareoff[i] OR L[i] < IBL[i] ) AND Buyflag )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( EODsquareoff[i], C[i], IBL[i] );
    }

    if( BuyCondition[i] AND NOT Buyflag )
    {
        Buy[i] = 1;
        Buyflag = 1;
        BuyPrice[i] = IBH[i - 1];
        singleT = True;
        PT[i] = BuyPrice[i] * 2;
    }

    if( H[i] > PT[i] AND Buyflag AND NOT BuyCondition[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = PT[i];
    }
}

myVar += ", PT = " + PT;
bRisk = BuyPrice - IBL;

max_lots = 20;
bLots	= Min( max_lots, floor( 20000 / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots = 0;
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H ), myboxF( L ) );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );

PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorBlue, 0, L, Offset = -40 );
PlotShapes( IIf( Buy, shapeSquare, shapeNone ), colorBlue, 0, L, Offset = -50 );
PlotShapes( IIf( Buy, shapeHollowUpArrow, shapeNone ), colorWhite, 0, L, Offset = -45 );
PlotShapes( IIf( sell, shapeStar, shapeNone ), colorred, 0, H, 20 );
PlotShapes( IIf( vCheck, shapeStar, shapeNone ), colorBlue, 0, L, -20 );


_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + ", Vol %0.f"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );
