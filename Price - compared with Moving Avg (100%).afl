pds = 10;
C1 = C / MA( Ref( C, -1 ), 5 );
C2 = MA( C, 5 ) / MA( C, 10 );
C3 = MA( C, 10 ) / MA( C, 20 );

V1 = V / MA( Ref( V, -1 ), 5 );
V2 = MA( V, 5 ) / MA( V, 10 );
V3 = MA( V, 10 ) / MA( V, 20 );

unsure = C1 < 1.1 AND C2 < 1.1 AND( V1 > 1 OR V2 > 1 OR V3 > 1 );
sure   = C1 > 1 AND C2 > 1.1 AND V1 > 1;
sure   = ( Ref( unsure, -1 ) OR Ref( sure, -1 ) ) AND sure;
exit = Ref( sure, -1 ) AND C1 < 1 AND V1 < Ref( V1, -1 );

barcolor = IIf( sure, colorPaleGreen, IIf( unsure, colorPaleBlue, IIf( exit, colorRed, colorWhite ) ) );

/* Colourized price bars drawn here */
if( ParamToggle( "V1", "0|1" ) )
{
	barcolor = IIf( V1 > 1, IIf(V1 < Ref(V1, -1), colorPaleBlue, colorPaleGreen), colorWhite  );
    Plot( 100 * V1, "    V1/ma(10)", barcolor, 2 + 4 );
}
if( ParamToggle( "V2", "0|1" ) )
{
	barcolor = IIf( V2 > 1, IIf(V2 < Ref(V2, -1), colorPaleBlue, colorPaleGreen), colorWhite  );
	Plot( 100 * V2, "    V2/ma(10)", barcolor, 2 + 4 );
}
if( ParamToggle( "V3", "0|1" ) )
{
	barcolor = IIf( V3 > 1, IIf(V3 < Ref(V3, -1), colorPaleBlue, colorPaleGreen), colorWhite  );
    Plot( 100 * V3, "    V3/ma(10)", barcolor, 2 + 4 );
}

Plot( 100, "", 1, 1 );

myVar = ", C1 = " + C1 + ", C2 = " + C2 + ", C3 = " + C3 + ", V1 = " + V1 + ", V2 = " + V2 + ", V3 = " + V3;

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{DATE}}, {{VALUES}} " + myVar ) );
