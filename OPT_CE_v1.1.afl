// Expiry Day
_SECTION_BEGIN( "CE System" );
#include <Options.afl>
// #include <AlgoFoxAuto/AlgoFoxButton.afl>

SetOption( "DisableRuinStop", True );

boxSize = ceil( MA( C, 100 ) / 200 ) / 10;
boxSize = IIf( StrFind( Name(), "NIFTY" ) > 0, 6, boxSize );
function myboxC( number )
{
    return ceil( number / boxsize ) * boxsize;
}
function myboxF( number )
{
    return floor( number / boxsize ) * boxsize;
}
RLS = IIf( StrFind( Name(), "MIDCPNIFTY" ), 50, IIf( StrFind( Name(), "BANKNIFTY" ), 15, IIf( StrFind( Name(), "RELIANCE" ), 500, IIf( StrFind( Name(), "TATAMOTORS" ), 550, IIf( StrFind( Name(), "NIFTY" ), 75, RoundLotSize ) ) ) ) );
RLS = IIf( StrFind( Name(), "TATAMOTORS" ), 550, IIf( StrFind( Name(), "RELIANCE" ), 500, IIf( StrFind( Name(), "INFY" ), 400, RLS ) ) );

tn = TimeNum();
DN                      = DateNum();
myVar = "";
firstBarOfTheDay = DN != Ref( DN, -1 );
fch  = ValueWhen( firstBarOfTheDay, H );
fcl  = ValueWhen( firstBarOfTheDay, L );

TradingZone = ( tn >= 100000 AND tn <= 151000 );
equityAtRisk = 3000;

V1 = Ref( V, -1 );
MAV = MA( V1, 15 );
HV = HHV( Ref( V, -1 ), 5 );
C1 = Ref( C, -1 );
H1 = Ref( H, -1 );
L1 = Ref( L, -1 );
O1 = Ref( O, -1 );

vol_up = V > MAV;
vol_up_1 = Ref( vol_up, -1 );

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = Optimize( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn = Flip( oi_Below, oi_Above );
oi_up   = Flip( oi_Above, oi_Below );

HHV10 = HHV( H1, 10 );
LLV10 = LLV( L1, 10 );

ACDUp 	= myboxC( fcl + Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );
ACDDown	= myboxF( fch - Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );

price_up = C > HHV10;
price_dn = C < LLV10;

long_buildup = price_up AND vol_up AND oi_dn;
short_buildup = price_dn AND vol_up AND oi_up;


CP_IH	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( H - C ) / ( .001 + H - L ) > 0.6 ) AND( ( H - O ) / ( .001 + H - L ) > 0.6 ) );
CP_H	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( C - L ) / ( .001 + H - L ) > 0.6 ) AND( ( O - L ) / ( .001 + H - L ) > 0.6 ) );
CP_EH	= ( ( C1 > O1 ) AND( O > C ) AND( O <= C1 ) AND( O1 <= C ) AND( ( O - C ) < ( C1 - O1 ) ) );
CP_UH 	= ( ( O1 > C1 ) AND( C > O ) AND( C <= O1 ) AND( C1 <= O ) AND( ( C - O ) < ( O1 - C1 ) ) );
CP_LC	= ( abs( C - O ) / ( .001 + H - L ) > .6 );
CP_LC1	= Ref( CP_LC, -1 );

BuyExit = myboxF( LLV10 - 2 * boxSize );
BE1 = Ref( BuyExit, -1 );
// ShortExit = Min( C + floor( equityAtRisk / RLS ), myboxC( HHV10  + 2.5 * boxSize ));
ShortExit = myboxC( HHV10  + 2.5 * boxSize );
SE1 = Ref( ShortExit, -1 );

ShortCondition	= H1 > Ref( HHV10, -1 ) AND C < C1 AND L > 5 AND( vol_up OR vol_up_1 ) AND( CP_LC OR CP_LC1 ) AND H > ACDDown;
// ShortCondition = ShortCondition AND isITM AND ITMRange == 0 AND daysToExpiry < 7;
BuyCondition	= L1 < Ref( LLV10, -1 ) AND C > C1 AND L > 5 AND( vol_up OR vol_up_1 ) AND( CP_LC OR CP_LC1 ) AND H < ACDUp;

myVar += ", BC = " +  BuyCondition + ", SC = " + ShortCondition;

BuyP  = ShortP = 0;
InShort = InBuy = 0;
ShortI  = BuyI = 0;
Short = Cover = 0 ;
Buy = Sell = 0;

BuyFlag = ShortFlag = False;
BF = SF = 0;
SPT = LPT = 0;
SellPrice = CoverPrice = C;

ROC_price = ROC( C, 3 );
myVar += ", ROC = " + ROC_price;
pp = Optimize( "pp", 0.5, 0.2, 2, 0.1 );

for( i = 10; i < BarCount; i++ )
{
    if( InShort )
    {
        if( i - ShortI > 5 AND C[i] > ShortP AND ShortExit[i] - boxSize[i] > O[i] )
        {
            ShortExit[i] = ShortExit[i] - boxSize[i];
        }

        ShortExit[i] = Min( ShortExit[i], ShortExit[i - 1] );
    }

    if( ( C[i] > ShortExit[i] OR !TradingZone[i] OR C[i] < 5 OR( CP_H[i] AND vol_up[i] AND L[i] < LLV10[i] AND C[i] < ShortP )) AND InShort )
    {
        Cover[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
    }

    if( InBuy AND O[i] > Max( BuyExit[i], BuyExit[i - 1] ) )
    {
    
		if( C[i] > LPT AND O[i] > Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] ) )
		{
			BuyExit[i] = Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] );
		}
        BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
    }

    if( ( C[i] < BuyExit[i] OR NOT TradingZone[i] OR( CP_IH[i - 1] AND vol_up[i - 1] AND C[i] < C1[i] ) )  AND InBuy )
    {
        Sell[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
    }

    if( ShortCondition[i] AND NOT InShort AND NOT InBuy )
    {
        ShortFlag = True;
        ShortI = i;
    }

    if( ShortFlag AND TradingZone[i] AND oi_dn[i] AND C[i] < C1[i] AND L[i] > 5 )
    {
        Short[i] = 1;
        ShortP = C[i];
        ShortI = i;
        ShortExit[i] = Min( C[i] + floor( equityAtRisk / RLS ), ShortExit[i] );
        InBuy = 0;
        InShort = 1;
        BuyFlag = False;
        ShortFlag = False;
    }

    SF[i] = ShortFlag;

    /*
        if(BuyFlag AND L[i] < L[i - 1]) {
            BuyFlag = False;
        }
    */
    if( BuyCondition[i] AND NOT InBuy AND NOT InShort )
    {
        BuyFlag = True;
        BuyI = i;
    }

    if( ROC_price[i] > 34 OR NOT TradingZone[i] )
    {
        BuyFlag = False;
    }

    if( BuyFlag AND vol_up[i] AND TradingZone[i] AND oi_up[i] AND H[i] > HHV10[i] AND L[i] > 5 AND NOT CP_IH[i] AND V[i] < HV[i] )
    {
        Buy[i] = 1;
        BuyP = C[i];
        BuyI = i;
        BuyExit[i] = Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] );
        InBuy = 1;
        InShort = 0;
        BuyFlag = False;
        ShortFlag = False;
        LPT = C[i] + 0.7 * floor( equityAtRisk / RLS );
    }

    if( H[i] < HHV10[i] OR NOT TradingZone[i]	)
    {
        ShortFlag = False;
    }

    BF[i] = BuyFlag;
}

myVar += ", SF = " + SF + ", BF = " + BF;
Buy = Buy AND isITM AND ITMRange == 0 AND daysToExpiry < 7;
Short = Short AND isITM AND ITMRange == 0 AND daysToExpiry < 7;
Sell = Sell OR NOT TradingZone;
Cover = Cover OR NOT TradingZone OR C < 5;
InBuy = InShort = 0;
BuyPrice = ShortPrice = C;

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleCandle );
Plot( ShortExit, "SE", IIf( SF, colorRed, colorGrey40 ), styleDashed );
Plot( BuyExit, "BE", IIf( BF, colorLime, colorGrey40 ), styleDashed );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

bRisk = BuyPrice - BuyExit;
bLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) ) ) );

sRisk = ShortExit - ShortPrice;
sLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) ) ) );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", SellPrice, CoverPrice );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{DATE}}, "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + "OI %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V, OI
                       ) );


RestorePriceArrays();
_SECTION_END();

GfxSelectFont( "BOOK ANTIQUA", 10, 100 );
GfxSetBkMode( 1 );
GfxSetTextColor( colorWhite );
GfxSelectSolidBrush( colorDarkGrey );

GfxSelectPen( colorLightBlue, 1 ); // border color
pxHeight = Status( "pxchartheight" ) ;
xx = Status( "pxchartwidth" );
x = 20;
x2 = 105;
y = pxHeight;
GfxRoundRect( x, y - 45, x2, y - 28, 7, 7 ) ;
GfxTextOut( "CE: " + NumToStr( SelectedValue( nearestITM_CE ), 1.0 ), x + 5, y - 45 );
