//########################################################################
//================= www.stockbangladesh.com ==============================
//=======================  Mehedi Arafat  ================================
//########################################################################

#include <Kolier.afl>
#include <Common.afl>

//------------------------------------------------------
// Chart Settings
//------------------------------------------------------
SetChartBkColor( colorLightBlue );   // color of outer border

//------------------------------------------------------
// Variables & Parameters
//------------------------------------------------------
Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

equityAtRisk    = Param( "Risk", 20000, 20000, 100000, 5000 );
ExpiryThursday  = Day() > 23 AND DayOfWeek() == 5;

vlots   = V / RoundLotSize;
vCheck  = vlots / MA( Ref( vlots, -1 ), OptimizeNot( "vp", 13, 1, 50, 1 ) );

//------------------------------------------------------
// Price Levels
//------------------------------------------------------
BuyP       = myboxC( fcl + 2 * ( fch - fcl ) );
ShortP     = myboxF( fch - 2 * ( fch - fcl ) );
ShortExit  = myboxC( Max( ShortP + boxSize, fch - 3 * boxSize ) );
BuyExit    = myboxF( Min( BuyP - boxSize, fcl + 3 * boxSize ) );

SPT        = myboxF( fch - 5 * ( fch - fcl ) );
LPT        = myboxC( fcl + 5 * ( fch - fcl ) );

//------------------------------------------------------
// Adjust Short/Buy Exits in Trading Zone
//------------------------------------------------------
for( i = 10; i < BarCount; i++ )
{
    if( TradingZone[i] )
    {
        if( L[i] < SPT[i] AND L[i] < Min( ShortExit[i], ShortP[i] ) )
            ShortExit[i] = Min( ShortExit[i], ShortP[i] );

        // Disabled: BuyExit adjustment
        // if( H[i] > LPT[i] AND Max( BuyExit[i], BuyP[i] ) < L[i] )
        //     BuyExit[i] = Max( BuyExit[i], BuyP[i] );
    }
}

//------------------------------------------------------
// Trade Conditions
//------------------------------------------------------
fbcheck = ( ( fch - fcl ) / fcl ) * 1000 < 3.4;
myVar  += ", check = " + ( ( fch - fcl ) / fcl ) * 1000;

pp      = Optimize( "pp", 100000, 100000, 113000, 1000 );
TT      = IIf( Interval() > in1Minute * 10, 100000, 104000 );

Short   = L < ShortP
          AND !firstBarOfTheDay
          AND TradingZone
          AND Ref( C, -1 ) >= Ref( ShortP, -1 )
          AND ( vCheck > 3.4 OR Ref( ExpiryThursday, -1 ) )
          AND tn < TT
          AND fbcheck;

Buy     = H > BuyP
          AND !firstBarOfTheDay
          AND TradingZone
          AND Ref( C, -1 ) <= Ref( BuyP, -1 )
          AND ( vCheck > 2.2 OR Ref( ExpiryThursday, -1 ) )
          AND tn < TT
          AND fbcheck;

//------------------------------------------------------
// Exit Levels: Propagation in Trading Zone
//------------------------------------------------------
ST = BT = 0;

for( i = 10; i < BarCount; i++ )
{
    if( Short[i] OR ST )
    {
        ST = True;
        ShortExit[i] = Min( ShortExit[i], ShortExit[i - 1] );
    }

    if( Buy[i] OR BT )
    {
        BT = True;
        BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
    }

    if( !TradingZone[i] )
        ST = BT = 0;
}

//------------------------------------------------------
// Final Trade Signals
//------------------------------------------------------
Cover   = H > ShortExit OR !TradingZone;
Short   = ExRem( Short, Cover );
Cover   = ExRem( Cover, Short );

Sell    = L < BuyExit OR !TradingZone;
Buy     = ExRem( Buy, Sell );
Sell    = ExRem( Sell, Buy );

//------------------------------------------------------
// Trade State Tracking
//------------------------------------------------------
InBuy   = TradingZone AND Flip( Buy, Sell );
InBuy   = InBuy OR Ref( InBuy, -1 );

InShort = TradingZone AND Flip( Short, Cover );
InShort = InShort OR Ref( InShort, -1 );

//------------------------------------------------------
// Prices for Orders
//------------------------------------------------------
BuyPrice    = C;
ShortPrice  = C;
CoverPrice  = IIf( !TradingZone AND L < ShortExit, C, ShortExit );
SellPrice   = IIf( !TradingZone AND H > BuyExit, C, BuyExit );

//------------------------------------------------------
// Plotting
//------------------------------------------------------
Plot( IIf( InBuy, BuyExit, C ), "", IIf( InBuy, colorPink, colorLightBlue ), styleDashed );
Plot( IIf( InShort, ShortExit, C ), "", IIf( InShort, colorPink, colorLightBlue ), styleDashed );

Plot( C, "Close", ParamColor( "Color", colorBlack ),
      styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );

PlotShapes( IIf( vCheck > 5, shapeDigit5,
            IIf( vCheck > 3, shapeDigit3, shapeNone ) ),
            colorBlack, 0, L, -25 );

Plot( IIf( TradingZone, BuyP,   Null ), "CP", colorBlue, styleLine | styleDashed );
Plot( IIf( TradingZone, ShortP, Null ), "SP", colorBlue, styleLine | styleDashed );

#include <Alert.afl>

//------------------------------------------------------
// Position Sizing
//------------------------------------------------------
bRisk   = BuyPrice - BuyExit;
bLots   = floor( Max( 0, floor( equityAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) ) );

sRisk   = ShortExit - ShortPrice;
max_lots= floor( 7500000 / ( RoundLotSize * C ) );

sLots   = floor( Max( 0, floor( equityAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) ) );

max_lots= 5;    // fixed max lots
bLots   = round( Min( max_lots, bLots ) );
sLots   = round( Min( max_lots, sLots ) );

SetPositionSize( IIf( Buy, bLots, sLots ) * RoundLotSize, spsShares );
SetPositionSize( 50, spsPercentOfPosition * ( Buy == sigScaleOut OR Short == sigScaleOut ) );

//------------------------------------------------------
// Debug Info
//------------------------------------------------------
myVar += StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myVar += ", risk = " + round( RoundLotSize * bRisk ) + ", " + round( RoundLotSize * sRisk );
myVar += StrFormat( ", exit = %0.0f, %0.0f", SellPrice, CoverPrice );
myVar += StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

_N( Title   = StrFormat(
        EncodeColor( colorDarkGrey ) + Name() + " - "
        + "O %0.2f, "
        + "H %0.2f, "
        + "L %0.2f, "
        + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
        + "Vol %0.1f, "
        + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
        + EncodeColor( colorDarkGrey ) + myVar,
        Open, High, Low, Close, ROC( C, 1 ), vCheck ) );

_SECTION_END();

//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR = "FUTSTK";
SYS   = "ACD";

if( StrFind( Name(), "NIFTY" ) )
{
    INSTR = "FUTIDX";
}

#include <AlgoFoxAuto/AlgoFoxCurr.afl>
