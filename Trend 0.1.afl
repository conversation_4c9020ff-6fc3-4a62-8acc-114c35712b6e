//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Tata Motors, Reliance
//-----------------------------------------------------

#include <ReactionTrendSystem.afl>
SetChartBkColor( colorWhite ) ;
SetChartOptions( 0, chartShowArrows | chartShowDates );
// OptimizerSetEngine( "cmae" );

Periods		= Optimize( "Periods", 20, 10, 500, 5 ); //470
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 6.0 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
eqAtRisk	= Param( "Risk", 20000, 20000, 100000, 5000 );
Periods 	= IIf( StrFind( Name(), "RELIANCE" ) > 0, Param( "RP", 120, 10, 500, 5 ), Periods );

//------------------------------------------------------
// psignal function
//------------------------------------------------------
function fPSignal( ser, integer )
{
    nStDev = StDev( ser, integer );
    nSma = MA( ser, integer );
    return IIf( nStDev > 0, Erf( nSma / nStDev / sqrt( 2 ) ), sign( nSma ) * 1.0 );
}

nPoints = OptimizeNot( "Number of Bars", 60, 20, 100, 2 );
ohlc4		= ( O + H + L + C ) / 4;
nIntr		= nPoints - 1;
nPSignal	= MA( fPSignal( Ref( ohlc4, 0 ) - Ref( ohlc4, -1 ), nIntr ), nIntr );
ndPSignal	= sign( nPSignal - Ref( nPSignal, -1 ) );

pBuy	= nPSignal < 0 AND ndPSignal > 0;
pSell	= nPSignal > 0 AND ndPSignal < 0;

sm = Param( "Smoothing Period", 21, 5, 500, 1 );
cd = Param( "Constant D", 0.4, 0.1, 2, 0.1 );
di = ( sm - 1.0 ) / 2.0 + 1.0;
c1 = 2 / ( di + 1.0 );
c2 = 1 - c1;
c3 = 3.0 * ( cd * cd + cd * cd * cd );
c4 = -3.0 * ( 2.0 * cd * cd + cd + cd * cd * cd );
c5 = 3.0 * cd + 1.0 + cd * cd * cd + 3.0 * cd * cd;
src = Close;
i1 = 0;
i2 = 0;
i3 = 0;
i4 = 0;
i5 = 0;
i6 = 0;

TimeFrameSet( inHourly );

for( start = 0; start < BarCount; start++ )
{
    if( NOT IsNull( Close[ start ] ) ) break;
}

for( i = start + 1; i < BarCount; i++ )
{
    i1[i] = c1[i] * C[i] + c2[i] * i1[i - 1];
    i2[i] = c1[i] * i1[i] + c2[i] * i2[i - 1];
    i3[i] = c1[i] * i2[i] + c2[i] * i3[i - 1];
    i4[i] = c1[i] * i3[i] + c2[i] * i4[i - 1];
    i5[i] = c1[i] * i4[i] + c2[i] * i5[i - 1];
    i6[i] = c1[i] * i5[i] + c2[i] * i6[i - 1];
}

bfr = -cd * cd * cd * i6 + c3 * ( i5 ) + c4 * ( i4 ) + c5 * ( i3 );
coralmode = bfr > Ref( bfr, -1 );
TimeFrameRestore();
bfr = TimeFrameExpand( bfr, inHourly );
coralmode = TimeFrameExpand( coralmode, inHourly );
color = IIf( coralmode , colorGreen, colorRed );
Plot( bfr, "Coral Trend Indicator", color, styleDots | styleNoLine | styleThick );

//------------------------------------------------------
// VWMA2 function
//------------------------------------------------------
function VWMA2( array, period )
{
    MAV		= MA( V, period );
    SDAV	= StDev( V,  320 );
    AV2		= Min( V, MAV + 2 * SDAV );
    return Sum( array * AV2, period ) / Sum( AV2, period );
}

top 	= Max( LinearReg( High, period ), WMA( High, ceil( period / 2 ) ) );
bottom 	= Min( LinearReg( Low , period ), WMA( Low , ceil( period / 2 ) ) );
top 	= ceil( top   / boxSize ) * boxSize;
bottom 	= floor( bottom / boxSize ) * boxSize;
stMA 	= ( top + bottom ) / 2;
stMA1 = Ref( stMA, -1 );

Vw 		= stMA;
Lr 		= mybox( ( LinearReg( C, Periods ) + H + L ) / 3 );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( ( Vw > bfr + boxSize ) AND( Lr > Vw + boxSize AND Vw > Vw_1 ), ( Vw < bfr - boxSize ) OR( Lr < Vw - boxSize AND Vw < Vw_1 ) );
Sd = StDev( C, Periods );

lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

ATR_mul = 2;
f = OptimizeNot( "F", 2.4, 1.0, 6, 0.1 );
ATR_f = ATR( Periods );

up   	= Lr + f * Max( ATR_f * ATR_mul, Sd );
down 	= Lr - f * Min( ATR_f * ATR_mul, Sd );
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

pp = OptimizeNot( "L", 25, 20, 30, 1 );
C_D = HHV( C, 39 );
L_25 = myboxF( LLV( L, pp ) );
H_25 = myboxC( HHV( H, pp ) );
pp2 = OptimizeNot( "exit", 8, 2, 50, 1 );
H_8 = myboxC( HHV( H, 8 ) );
H_8_1 = Ref( H_8, -1 ) + 2 * boxSize;
L_8 = myboxF( LLV( L, 8 ) );
L_8_1 = Ref( L_8, -1 ) - 2 * boxSize;
H_25_1 = Ref( H_25, -1 );
L_25_1	= Ref( L_25, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
sRisk 	= Min( Vw_1, up_1 ) - ( ( IIf( L_25_1 > O, L_25_1, C ) + C ) / 2 );
bRisk 	= ( ( IIf( H_8_1 < O, H_8_1, C ) + C ) / 2 ) - Max( Vw_1, down_1 );
max_sl	= myRound( eqAtRisk / RoundLotSize );
curr_sl	= 0;

//------------------------------------------------------
// Entry and exit conditions
//------------------------------------------------------
mode = Flip( Lr > Vw + boxSize, Lr < Vw - boxSize );
modeVw = Flip( Vw > Vw_1 + boxSize, Vw < Vw_1 - boxSize );
modeLr = Flip( Lr > Lr_1, Lr < Lr_1 );
BuyCondition   =     mode AND TradingZone AND mode;
ShortCondition = NOT mode AND TradingZone AND Vw > C AND L < L_8_1 AND C < O;
InBuy = 0;
InShort = 0;
exit = C;
HH = 0;
LL = H;
BF = 0;

for( i = 1; i < BarCount; i++ )
{

    if( mode[i] )
    {
        HH[i] = HH[i - 1];

        if( NOT modeLr[i] )
        {
            HH[i] = Max( H[i - 1], HH[i] );
            LL[i] = Min( L[i], LL[i] );
            exit[i] = exit[i - 1];
        }
        else
        {
            LL[i] = LL[i - 1];
            exit[i] = Max( LL[i], exit[i - 1] );
        }

        if( HH[i] > 0 AND C[i] > HH[i] AND NOT Buyflag )
        {
            Buyflag = 1;
            Buy[i] = 1;
            exit[i] = LL[i];
        }

    }
	exit[i] = Min(exit[i], Vw[i - 1]);
    exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];

    if( NOT mode[i] )
    {
        exit[i] = exit[i - 1];

        if( Buyflag AND NOT Buy[i] )
        {
            Sell[i] = 1;
            Buyflag = 0;
        }
    }

    if( Buyflag AND NOT Buy[i] )
    {
        if( C[i] < exit[i] )
        {
            Sell[i] = 1;
            Buyflag = 0;
        }
    }

    BF[i] = BuyFlag;
}

myVar = ", C0 = " + modeLr + ", LL = " + LL + ", exit[i - 1] = " + Ref( exit, -1 );

exit = myRound( exit );
PT	 = myRound( PT );
bLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

max_lots = 4;
max_lots = IIf( StrFind( Name(), "TATAMOTORS" ) > 0, 3, max_lots );

bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );

bRisk = IIf( bRisk == 0 AND exit < C, C - exit, brisk );
sRisk = IIf( sRisk == 0 AND exit > C, exit - C, sRisk );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", buy pos = %0.0f, short pos = %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

//------------------------------------------------------
// Plotting and Alerts
//------------------------------------------------------
if( ParamToggle( "Show RT", "0|1" ) )
{
// draw levels from Reaction Trend System
    Plot( B1, "", colorBrightGreen, styleStaircase | styleNoLabel );
    Plot( S1, "", colorRed, styleStaircase | styleNoLabel );
    Plot( HBOP, "", colorOrange, styleStaircase | styleNoLabel );
    Plot( LBOP, "", colorGreen, styleStaircase | styleNoLabel );
}

Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, bkcolor ), styleStaircase | styleDashed, Null, Null, Null, -1 );
Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( IIf( TradingZone AND mode AND HH > 0, HH, null ), "", colorBlue, styleDashed, Null, Null, Null, 1 );
// Plot( IIf( TradingZone AND NOT mode AND NOT InShort, Ref( L_25, -1 ), null ), "", colorRed, styleDashed, Null, Null, Null, 1 );
Plot( Vw , "", IIf( mode, colorGreen, colorPink ), ParamStyle( "Style" ) );
Plot( Lr, "", IIf( modeLr, colorGreen, colorPink ), ParamStyle( "Style" ) | styleThick, Null, Null, Null, 2 );

#include <Alert.afl>

_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );


//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTSTK";
#include<AlgoFoxAuto/AlgoFoxAuto.afl>
