//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Tata Motors
//-----------------------------------------------------

#include <ReactionTrendSystem.afl>
SetChartBkColor( colorWhite ) ;

Periods		= Param( "Periods", 55, 50, 500, 5 ); //470
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 6.0 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
eqAtRisk	= Param( "Risk", 25000, 20000, 100000, 5000 );
Periods 	= IIf( StrFind( Name(), "RELIANCE" ) > 0, Param( "RP", 120, 50, 500, 5 ), Periods );

//------------------------------------------------------


Vw 		= myround( VWMA2( C, Periods ) );
top 	= Max( LinearReg( High, Periods ), LinearReg( High, ceil( Periods / 2 ) ) );
bottom 	= Min( LinearReg( Low , Periods ), LinearReg( Low , ceil( Periods / 2 ) ) );
Lr 		= mybox( ( LinearReg( C, Periods ) + H + L ) / 3 );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( Lr > Vw + boxSize, Lr < Vw - boxSize );
Sd = StDev( C, floor( Periods / 2 ) );

SetChartOptions( 0, chartShowArrows | chartShowDates );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

ATR_mul = 1.4;
pp = Optimize( "L", 1, 0.5, 2, 0.1 );
ATR_mul = 1.4;
f = OptimizeNot( "F", 2.4, 1.0, 6, 0.1 );
ATR_f = ATR( floor( Periods / 2 ) );

up   	= Lr + f * Max( ATR_f * ATR_mul, Sd );
down 	= Lr - f * Min( ATR_f * ATR_mul, Sd );
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

L_5 = myboxF( LLV( L, 22 ) );
H_5 = myboxC( HHV( H, 22 ) );
H_3 = myboxC( HHV( H, 3 ) );
H_3_1 = Ref( H_3, -1 );
H_5_1 = Ref( H_5, -1 );
L_5_1	= Ref( L_5, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
sRisk 	= Min( Vw_1, up_1 ) - ( ( IIf( L_5_1 > O, L_5_1, C ) + C ) / 2 );
bRisk 	= ( ( IIf( H_5 > 0 AND H_5_1 < O, H_5_1, C ) + C ) / 2 ) - Max( Vw_1, down_1 );
max_sl	= myRound( eqAtRisk / RoundLotSize );

BuyCondition   =     Ref( mode, - 1 ) AND L > Lr AND H > H_5_1 AND TradingZone AND WhiteBody;
ShortCondition = NOT Ref( mode, - 1 ) AND H < Lr AND L < L_5_1 AND TradingZone;
InBuy = 0;
InShort = 0;

for( i = 1; i < BarCount; i++ )
{
    if( Buyflag )
    {
        InBuy[i] = 1;
        t_exit =  Max( Vw[i - 1], down[i - 1] );

        if( H[i] >= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Max( down[i - 1], C[i - 1] * ( 1 - StopLoss ) );
        }

        if( O[i] > t_exit )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        PT[i]   = PT[i - 1];
        exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Shortflag )
    {
        InShort[i] = 1;
        t_exit = Max( Vw[i - 1], up[i - 1] );

        if( L[i] <= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Min( up[i - 1], C[i - 1] * ( 1 + StopLoss ) );
        }

        if( O[i] < t_exit )
        {
            exit[i] = Min( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        PT[i]   = PT[i - 1];
        exit[i] = ceil( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Buyflag AND L[i] < exit[i] AND O[i] > exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Buyflag AND C[i] < exit[i] AND O[i] < exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Buyflag AND NOT mode[i - 1] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = C[i];
    }

    if( Shortflag AND H[i] > exit[i] AND O[i] < exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( ( O[i] < exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Shortflag AND C[i] > exit[i] AND O[i] > exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( ( O[i] < exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Shortflag AND mode[i - 1] )
    {
        Cover[i] 	= 1;
        Shortflag	= 0;
        CoverPrice[i] = C[i];
    }

    if( NOT Buyflag AND BuyCondition[i] )
    {
        // BuyP 		= ( IIf( ( H_5[i - 1] < O[i] ) AND NOT firstBarOfTheDay[i], H_5[i - 1], C[i] ) + L[i] ) / 2;  // needed as autotrade happens on candle close
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= Max( Vw[i - 1], down[i - 1] );
        Buyflag 	= 1;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i] 	= BuyP - exit[i];
        PT[i]		= BuyP * ( 1 + Target );
        PThit		= False;
    }

    if( NOT Shortflag AND ShortCondition[i] )
    {
        // ShortP			= ( IIf( ( L_5[i - 1] > O[i] ) AND NOT firstBarOfTheDay[i], L_5[i - 1], C[i] ) + H[i] ) / 2;  // needed as autotrade happens on candle close
        ShortP			= C[i];
        ShortPrice[i]	= ShortP;
        exit[i]			= Min( Vw[i - 1], up[i - 1] );
        Buyflag   		= 0;
        Shortflag 		= 1;
        Short[i]  		= 1;
        sRisk[i] 		= ( exit[i] - ShortP ); //eqAtRisk
        PT[i]			= ShortP * ( 1 - Target );
        PThit			= False;
    }
}

exit = myRound( exit );
PT	 = myRound( PT );
max_lots = Min( floor( 500000 / ( RoundLotSize * C * 0.2 ) ), 20 );
bLots	= Min( max_lots, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Min( max_lots, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

if( ParamToggle( "Show RT", "0|1" ) )
{
// draw levels from Reaction Trend System
    Plot( B1, "", colorBrightGreen, styleStaircase | styleNoLabel );
    Plot( S1, "", colorRed, styleStaircase | styleNoLabel );
    Plot( HBOP, "", colorOrange, styleStaircase | styleNoLabel );
    Plot( LBOP, "", colorGreen, styleStaircase | styleNoLabel );
}

Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, bkcolor ), styleStaircase | styleDashed, Null, Null, Null, -1 );
Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( IIf( TradingZone AND mode AND NOT InBuy, Ref( H_5, -1 ), null ), "", colorBlue, styleDashed, Null, Null, Null, 1 );
Plot( IIf( TradingZone AND NOT mode AND NOT InShort, Ref( L_5, -1 ), null ), "", colorRed, styleDashed, Null, Null, Null, 1 );
Plot( Vw , "", IIf( mode, colorGreen, colorPink ), ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( MA(Vw, 50) , "", colorBlack, ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( Lr_1, "", colorGrey40, ParamStyle( "Style" ) | styleThick, Null, Null, Null, 2 );

#include <Alert.afl>

_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );


//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTSTK";
SYS  = "FUT";
// #include<AlgoFoxAuto/AlgoFoxAuto.afl>
