// Statistical Validation Mean Reversion Trading System for Amibroker
// Uses custom DLL for Hurst Exponent and ADF Test Statistic to filter mean-reverting assets
// Applies Bollinger Bands and RSI for mean reversion trading on futures

_SECTION_BEGIN("Statistical Validation Mean Reversion");

// Chart Styling
Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );

// Trading Setup
SetPositionSize(RoundLotSize, spsShares); // Use spsContracts for futures

// ===================================================================================
// ==                          PARAMETERS & STATISTICAL TEST                        ==
// ===================================================================================

LookbackPeriod   = Optimize("LookbackPeriod", 100, 10, 200, 10);
ADFLags          = Param("ADF Lags", 1, 0, 10, 1);

// --- Statistical Thresholds ---
HurstThreshold   = Param("Hurst Exponent Threshold", 0.4, 0.1, 0.5, 0.01); // H < 0.5 for mean reversion
ADFCriticalValue = Param("ADF Critical Value", -2.86, -4.0, -2.0, 0.01);   // 5% significance level is ~-2.86

// --- Technical Indicator Parameters ---
BBPeriods        = Param("Bollinger Periods", 20, 10, 50, 1);
BBStdDev         = Param("Bollinger StdDev", 2, 1, 3, 0.1);
RSIPeriod        = Param("RSI Period", 14, 5, 30, 1);
RSIOverbought    = Param("RSI Overbought", 70, 50, 90, 1);
RSIOversold      = Param("RSI Oversold", 30, 10, 50, 1);
StopLookback     = Param("Stop Lookback Period", 5, 3, 10, 1);
StartTime        = ParamTime("Start Time", "09:30:00");
EndTime          = ParamTime("End Time", "15:00:00");

// --- Parameter Validation ---
LookbackPeriod   = Max(10, LookbackPeriod);
ADFLags          = Max(0, Min(ADFLags, LookbackPeriod/2 - 1));

// --- Call custom DLL functions ---
// NOTE: Ensure your DLL file is in the AmiBroker\\Plugins directory
hurst = HurstValue(Close, LookbackPeriod);
adf   = ADFPValue(Close, LookbackPeriod, ADFLags);

myVar = ", hurst = " + hurst + ", adf = " + adf;

// --- Corrected Validation Logic ---
// We check if the Hurst Exponent is low AND the ADF statistic is MORE NEGATIVE than the critical value.
IsMeanReverting = hurst < HurstThreshold AND adf < ADFCriticalValue;

// ===================================================================================
// ==                             TRADING LOGIC & SIGNALS                           ==
// ===================================================================================

// Indicator Calculations
BBTop    = BBandTop(C, BBPeriods, BBStdDev);
BBBottom = BBandBot(C, BBPeriods, BBStdDev);
BBMid    = MA(C, BBPeriods);
RSIValue = RSI(RSIPeriod);

// Trading Conditions
TimeFilter = TimeNum() >= StartTime AND TimeNum() <= EndTime;

Buy   = IsMeanReverting AND C < BBBottom AND RSIValue < RSIOversold AND TimeFilter;
Short = IsMeanReverting AND C > BBTop    AND RSIValue > RSIOverbought AND TimeFilter;

// Exit conditions: revert to the mean or end of the day
Sell  = C > BBMid OR RSIValue > RSIOverbought OR TimeNum() > EndTime;
Cover = C < BBMid OR RSIValue < RSIOversold   OR TimeNum() > EndTime;

// Remove redundant signals between entry and exit
Buy   = ExRem(Buy, Sell);
Sell  = ExRem(Sell, Buy);
Short = ExRem(Short, Cover);
Cover = ExRem(Cover, Short);

// ===================================================================================
// ==                               PLOTTING & EXPLORATION                          ==
// ===================================================================================

// Plotting Bollinger Bands and Price
Plot(BBTop,    "BB Top",    colorBlue, styleLine);
Plot(BBBottom, "BB Bottom", colorBlue, styleLine);
Plot(BBMid,    "BB Mid",    colorRed,  styleDashed);

// Plotting RSI in a separate pane
Plot(RSIValue, "RSI", colorYellow, styleLine | styleOwnScale);
Plot(RSIOverbought, "OB", colorRed, styleDashed | styleOwnScale);
Plot(RSIOversold,   "OS", colorGreen, styleDashed | styleOwnScale);

// Plotting Statistical Indicators in their own panes
// Plot(hurst, "Hurst (H < " + HurstThreshold + ")", IIf(hurst < HurstThreshold, colorGreen, colorRed), styleHistogram | styleOwnScale);
// Plot(adf,   "ADF Stat (Stat < " + ADFCriticalValue + ")", IIf(adf < ADFCriticalValue, colorGreen, colorRed), styleHistogram | styleOwnScale);
// Plot(ADFCriticalValue, "ADF Critical", colorBlue, styleDashed | styleNoLabel | styleLeftAxisScale);

// Plot Buy/Sell/Short/Cover Arrows
PlotShapes(IIf(Buy,   shapeUpArrow,   shapeNone), colorGreen,     0, L, -20);
PlotShapes(IIf(Sell,  shapeDownArrow, shapeNone), colorRed,       0, H, -20);
PlotShapes(IIf(Short, shapeDownArrow, shapeNone), colorOrange,    0, H, 0);
PlotShapes(IIf(Cover, shapeUpArrow,   shapeNone), colorLightBlue, 0, L, 0);

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V
                       ) );

_SECTION_END();