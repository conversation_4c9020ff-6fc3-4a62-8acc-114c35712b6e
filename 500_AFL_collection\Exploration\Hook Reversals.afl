//------------------------------------------------------------------------------
//
//  Formula Name:    Hook Reversals
//  Author/Uploader: <PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-09-04 01:47:26
//  Origin:          
//  Keywords:        
//  Level:           basic
//  Flags:           exploration
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=107
//  Details URL:     http://www.amibroker.com/library/detail.php?id=107
//
//------------------------------------------------------------------------------
//
//  Find Hook reversals using automatic analysis
//
//  /*Hook Reversals Automatic Analysis
//
//  by <PERSON>*/
//
//  buy=O<(L+0.2*(H-L)) AND C>(H-0.2*(H-L)) AND H<ref(H,-1) AND L>ref(L,-1);
//
//  sell=O>(L+0.8*(H-L)) AND C<(H-0.8*(H-L)) AND H<ref(H,-1) AND L>ref(L,-1);
//
//------------------------------------------------------------------------------

/*Hook Reversals Automatic Analysis
by Larry Lovrencic*/

buy=O<(L+0.2*(H-L)) AND C>(H-0.2*(H-L)) AND H<ref(H,-1) AND L>ref(L,-1);
sell=O>(L+0.8*(H-L)) AND C<(H-0.8*(H-L)) AND H<ref(H,-1) AND L>ref(L,-1);