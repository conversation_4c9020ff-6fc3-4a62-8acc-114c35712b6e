//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------

#include <ReactionTrendSystem.afl>
SetChartBkColor( colorWhite ) ;

Periods		= Param( "Periods", 85, 50, 500, 5 ); //470
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 5.5 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
eqAtRisk	= 25000; //Optimize( "Risk", 30000, 20000, 100000, 5000 );
Periods 	= IIf( StrFind( Name(), "NIFTY" ) > 0, 250, Periods );
Periods 	= IIf( StrFind( Name(), "BANKNIFTY" ) > 0, 345, Periods );

//------------------------------------------------------


function VWMA2( array, period )
{
    MAV		= MA( V, period );
    SDAV	= StDev( V, 250 );
    AV2		= Min( V, MAV + 2 * SDAV );
    return Sum( array * AV2, period ) / Sum( AV2, period );
}

Vw 		= myround( VWMA2( C, Periods ) );
top 	= Max( LinearReg( High, Periods ), LinearReg( High, ceil( Periods / 2 ) ) );
bottom 	= Min( LinearReg( Low , Periods ), LinearReg( Low , ceil( Periods / 2 ) ) );
Lr 		= mybox( ( LinearReg( C, Periods ) + H + L ) / 3 );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( Lr > Vw + boxSize, Lr < Vw - boxSize );
Sd = myBox( StDev( C, Periods ) );

SetChartOptions( 0, chartShowArrows | chartShowDates );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

f = 1.8; //Optimize("F", 2, 1.8, 4, 0.1);

up   	= Lr + f * SD;
down 	= Lr - f * SD;
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

pp = OptimizeNot( "L", 25, 20, 30, 1 );
C_D = HHV( C, 39 );
L_5 = myboxF( LLV( L, pp ) );
C_5 = LLV( C, 5 );
H_5 = myboxC( HHV( H, pp ) );
H_5_1 = Ref( H_5, -1 );
L_5_1	= Ref( L_5, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
sRisk 	= Min( Vw_1, up_1 ) - ( ( IIf( L_5_1 > O, L_5_1, C ) + C ) / 2 );
bRisk 	= ( ( IIf( H_5 > 0 AND H_5_1 < O, H_5_1, C ) + C ) / 2 ) - Max( Vw_1, down_1 );
max_sl	= myRound( eqAtRisk / RoundLotSize );
curr_sl	= 0;
FirstExit = 0;

BuyCondition   =     Ref( mode, - 1 ) AND L > Lr AND TradingZone AND C > S1;
ShortCondition = NOT Ref( mode, - 1 ) AND H < Lr AND TradingZone AND C < B1;
InBuy = 0;
InShort = 0;
BuyLots = 0;
max_lots = 4;
max_lots = IIf( StrFind( Name(), "TATAMOTORS" ) > 0, 3, max_lots );
max_lots = IIf( StrFind( Name(), "BANKNIFTY" ) > 0, 5, max_lots );

MPThit = False;
PT1hit = False;
TradeProfit	= 35000;
TradeProfit = IIf( StrFind( Name(), "TATAMOTORS" ) > 0, 30000, TradeProfit );
TradeProfit = IIf( StrFind( Name(), "RELIANCE" ) > 0, 65000, TradeProfit );
pp2 = OptimizeNot( "pp2", 5, 1, 30, 1 );
L_PT1 = myboxF( LLV( Ref( L, -1 ), pp2 ) );
H_PT1 = myboxC( HHV( Ref( H, -1 ), pp2 ) );

HBOP = myboxC( HBOP );
S1 = myboxC( S1 );
B1 = myboxF( B1 );
LBOP = myboxF( LBOP );

for( i = 1; i < BarCount; i++ )
{
    if( Buyflag )
    {
        InBuy[i] = 1;
        t_exit =  Max( Vw[i - 1], down[i - 1] );

        if( H[i] >= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Max( down[i - 1], C[i - 1] * ( 1 - StopLoss ) );
        }

        if( O[i] > t_exit )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        if( firstBarOfTheDay[i] AND exit[i] > L[i] )
        {
            exit[i] = L[i];
        }

        PT[i]   = PT[i - 1];
        exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];

        PT1hit[i] = PT1hit[i - 1];

        if( !PT1hit[i] AND BuyLots * RoundLotSize * ( C[i] - BuyP ) > TradeProfit )
        {
            PT1hit[i] = True;
        }
    }

    if( Shortflag )
    {
        InShort[i] = 1;
        t_exit = Max( Vw[i - 1], up[i - 1] );

        if( L[i] <= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Min( up[i - 1], C[i - 1] * ( 1 + StopLoss ) );
        }

        if( O[i] < t_exit )
        {
            exit[i] = Min( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        PT[i]   = PT[i - 1];
        exit[i] = ceil( exit[i] / boxSize[i] ) * boxSize[i];

        PT1hit[i] = PT1hit[i - 1];

        if( !PT1hit[i] AND Shortlots * RoundLotSize * ( ShortP - C[i] ) > TradeProfit )
        {
            PT1hit[i] = True;
        }
    }

    if( Buyflag AND L[i] < exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Buyflag AND NOT mode[i - 1] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = C[i];
    }

    if( Shortflag AND H[i] > exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( ( O[i] < exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Shortflag AND mode[i - 1] )
    {
        Cover[i] 	= 1;
        Shortflag	= 0;
        CoverPrice[i] = C[i];
    }

    if( Buyflag AND !MPThit AND PT1hit[i] )
    {
        MPThit = True;
        PT1hit[i] = False;
        Buy[i] = sigScaleOut;
        BuyPrice[i] = C[i];
    }

    if( Shortflag AND !MPThit AND PT1hit[i] )
    {
        MPThit = True;
        Short[i] = sigScaleOut;
        ShortPrice[i] = C[i];
    }


    if( NOT Buyflag AND C[i] > Max( H_5_1[i], S1[i] ) AND BuyCondition[i] )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= Max( Vw[i - 1], down[i - 1] );
        Buyflag 	= 1;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i] 	= BuyP - exit[i];
        curr_sl		= bRisk[i];
        PT[i]		= BuyP * ( 1 + Target );
        PThit		= False;
        BuyLots		= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk[i], bRisk[i], 1 ) ) ) );
        BuyLots		= Min( max_lots, BuyLots );
        MPThit = False;
        PT1hit[i] = False;
    }

    if( NOT Shortflag AND C[i] < Min( L_5_1[i],  B1[i] ) AND ShortCondition[i] )
    {
        ShortP			= C[i];
        ShortPrice[i]	= ShortP;
        exit[i]			= Min( Vw[i - 1], up[i - 1] );
        Buyflag   		= 0;
        Shortflag 		= 1;
        Short[i]  		= 1;
        sRisk[i] 		= ( exit[i] - ShortP ); //eqAtRisk
        curr_sl			= sRisk[i];
        PT[i]			= ShortP * ( 1 - Target );
        PThit			= False;
        ShortLots		= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk[i], sRisk[i], 1 ) ) ) );
        ShortLots		= Min( max_lots, BuyLots );
        MPThit = False;
        PT1hit[i] = False;
    }
}

exit = myRound( exit );
PT	 = myRound( PT );
bLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

max_lots = 4;
max_lots = IIf( StrFind( Name(), "TATAMOTORS" ) > 0, 3, max_lots );
max_lots = IIf( StrFind( Name(), "BANKNIFTY" ) > 0, 5, max_lots );

bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );

bRisk = IIf( bRisk == 0 AND exit < C, C - exit, brisk );
sRisk = IIf( sRisk == 0 AND exit > C, exit - C, sRisk );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
SetPositionSize( 50, spsPercentOfPosition * ( Buy == sigScaleOut OR Short == sigScaleOut ) );

myvar  	+= StrFormat( ", buy pos = %0.0f, short pos = %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
myvar	+= ", PT = " + PT + ", ATR = " + p_ATR;

bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

// 1 = "B" day, 2 = "O" day, 3 = "S" day
PlotShapes( shapeDigit1 * ( hlp_phase == 1 ), IIF( hlp_b == 1, colorYellow, colorWhite ), 0, HBOP, 0, 0 );
PlotShapes( shapeDigit2 * ( hlp_phase == 2 ), colorWhite, 0, HBOP, 0, 0 );
PlotShapes( shapeDigit3 * ( hlp_phase == 3 ), IIF( hlp_s == 1, colorYellow, colorWhite ), 0, HBOP, 0, 0 );
// lightblue digits at days where original phase is used for the calculation.
PlotShapes( shapeDigit1 * ( hlp_phase_l1 == 1 ), colorLightBlue, 0, HBOP, -15 );
PlotShapes( shapeDigit3 * ( hlp_phase_l1 == 3 ), colorLightBlue, 0, HBOP, -15 );
// draw levels from Reaction Trend System
Plot( HBOP, "R2", colorOrange, styleStaircase | styleNoLabel );
Plot( S1, "R1", colorRed, styleStaircase | styleNoLabel );
Plot( B1, "S1", colorBrightGreen, styleStaircase | styleNoLabel );
Plot( LBOP, "S2", colorGreen, styleStaircase | styleNoLabel );

Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, bkcolor ), styleStaircase | styleDashed, Null, Null, Null, -1 );
Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( IIf( TradingZone AND mode AND NOT InBuy, Max( H_5_1, S1 ), null ), "", colorBlue, styleDashed, Null, Null, Null, 1 );
Plot( IIf( TradingZone AND NOT mode AND NOT InShort, Min( L_5_1, B1 ), null ), "", colorRed, styleDashed, Null, Null, Null, 1 );
Plot( Vw , "", IIf( mode, colorGreen, colorPink ), ParamStyle( "Style" ) );
Plot( Lr, "", colorGrey40, ParamStyle( "Style" ) | styleThick, Null, Null, Null, 2 );

myVar += ", boxSize = " + boxSize;

#include <Alert.afl>

_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "Open %0.2f, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 )
                       ) );