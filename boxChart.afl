
_SECTION_BEGIN( "myBoxChart" );
SetChartOptions( 0, chartShowArrows | chartShowDates | chartLogarithmic | chartWrapTitle );

SetPositionSize( RoundLotSize, spsShares );

// ORB calculations
firstBarOfDay = Day() != Ref( Day(), -1 );
ORbHigh = ValueWhen( firstBarOfDay, High );
ORbLow = ValueWhen( firstBarOfDay, Low );
ORbCenter = round( ( ORbHigh + ORbLow ) / 2 );

// Volatility
tr = round( 10 * ATR( 45 ) ) / 10;
myV = ( ATR( 45 ) / EMA( C, 2 * 45 ) ) * 100;
ORBAtr = ValueWhen( firstBarOfDay, tr );
Volatility = round( 10000 * ( tr / ORbCenter ) ) / 100;
boxSizePercent = Param( "Price Percent", 0.4, 0.1, 9, 0.1 ) *
                 LastValue( ORbCenter ) / 100;
boxSizeORb = Param( "ORB ATR factor", 0.6, 0.1, 9, 0.1 ) * ORBAtr;
boxSize = round( Max( boxSizePercent, boxSizeORb ) );
fixed = ParamToggle( "Fixed Box size", "on|off" );
szFac = Param( "Box Size Factor", 5, 1, 30, 0.5 );

if( fixed )
{
    boxSize = round( LastValue( H ) / 100 ) * szFac / 10;
}

rH = round( H / boxSize );
myH = boxSize * IIf( rH < H / boxSize, rH + 1, rH );

rL = round( L / boxSize );
myL = boxSize * IIf( rL > L / boxSize, rL - 1, rL );

rO = round( O / boxSize );
myO = boxSize * IIf( rO > O / boxSize, rO - 1, rO );

rC = round( C / boxSize );
myC = boxSize * IIf( rC > C / boxSize, rC - 1, rC );

myC = IIf( myH - C < boxSize / 3, myH, myC );
myC = IIf( C - myL < boxSize / 3, myL, myC );

numBox = ( myH - myL ) / boxSize;
fil = Param( "Filter", 1, 0, 25, 1, 0.5 );
Hfil = myH + fil;
Lfil = myL - fil;

paint = IIf( myL < Ref( myL, -1 ) AND myH <= Ref( myH, -1 ), colorRed,
             IIf( myH > Ref( myH, -1 ) AND myL >= Ref( myL, -1 ), colorGreen, colorBlack ) );

/*
boxOpen = boxSize * ( round( ( Open - ORbCenter ) / boxSize ) ) + ORbCenter ;
boxHighRaw = boxSize * ( round( ( High - ORbCenter ) / boxSize ) ) + ORbCenter ;
boxHigh = IIf( boxHighRaw < High, boxHighRaw + boxSize, boxHighRaw );

boxLowRaw = boxSize * ( round( ( Low - ORbCenter ) / boxSize ) ) + ORbCenter;
boxLow = IIf( boxLowRaw > Low, boxLowRaw - boxSize, boxLowRaw );

boxClose = boxSize * ( round( ( Close - ORbCenter ) / boxSize ) ) + ORbCenter;
*/
boxOpen = myO; boxHigh = myH; boxLow = myL; boxClose = myC;

///////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// Plotting the boxes
paint = IIf( boxLow < Ref( boxLow, -1 ) AND boxHigh <= Ref( boxHigh, -1 ), colorRed,
             IIf( boxHigh > Ref( boxHigh, -1 ) AND boxLow >= Ref( boxLow, -1 ),
                  colorGreen, colorDarkBlue ) );
PlotOHLC( boxOpen, boxHigh, boxLow, boxClose, "", colorBlack, styleCandle, Null, Null, Null );
// PlotOHLC( myO, myH, myL, myC, "BoxPrice", paint, styleNoTitle | GetPriceStyle() );
_N( Title = StrFormat( "{{NAME}} - {{INTERVAL}} {{DATE}}, BoxSize %g (%g box), Open %g, High %g, Low %g, Close %g (%.1f%%), Volume %gk, tr %g,OrbAtr %g, Volatality %g, {{VALUES}}"
                       , boxSize, round( ( boxHigh - boxLow ) / boxSize ), boxOpen, boxHigh, boxLow,
                       boxClose, SelectedValue( ROC( C, 1 ) ), Volume / 1000, tr, ORBAtr,
                       Volatility ) );

//////////////////////////////////////////////////////////////////////////////
// Average to get the Trend (A trend is price average over certain period of bars)
avp = EMA( boxClose, Param( "Average period", 51, 0, 120 ) );

// Candle Range Calculation
CandleRange = round( boxHigh - boxLow );
AbsoluteCandleRange = abs( boxOpen - boxClose );
avgACR = EMA( AbsoluteCandleRange, 51 );

// Plotting the averages
AverageDisplay = ParamToggle( "Display Average", "off|on" );
upTrendShade = ParamColor( "UpTrend Shade", ColorRGB( 187, 253, 187 ) );
downTrendShade = ParamColor( "DownTrend Shade", colorRose );

if( AverageDisplay )
{
    Plot( avp, "Avg", IIf( avp > boxClose, colorBrown,
                           colorDarkGreen ), styleNoLabel, Null, Null, Null, -8 );
    smallAvp = EMA( boxClose, 9 );
    Trend = smallAvp - avp;
    SetChartBkGradientFill( colorLavender, LastValue( IIf( Trend >
                            Ref( Trend, -1 ), upTrendShade, downTrendShade ) ) );
}

// Support and resistance
SRDisplay = ParamToggle( "Display Support/resistance", "off|on" );
srRange = Param( "Support/Resistance Period", 150, 3, 500, 1 );
SRFillColor = ParamColor( "Support/Resistance Color", colorLightBlue );
sens = Param( "Mud Sensitivity", 80, 1, 100, 1 );

if( SRDisplay )
{
    resistance = HHV( boxHigh, srRange );
    support = LLV( boxLow, srRange );

    Lresistance = LastValue( resistance );
    Lsupport = LastValue( support );

    step = ( Lresistance - Lsupport ) / 100;

    if( LastValue( Cum( 1 ) - 1 ) > 100 )
    {
        end = LastValue( Cum( 1 ) - 1 );
        start = end - srRange;

        if( start < 0 ) start = 0;

        profile = Null;

        for( pos = 0; pos < 100; pos ++ )
        {
            profile[pos] = 0;
            LocationH = Lresistance - pos * step;
            LocationL = Lresistance - ( pos + 1 ) * step;
            profileCount = 0;

            for( i = start; i <= end; i ++ )
            {
                if( boxLow[i] <= LocationH AND boxHigh[i] >= LocationL )
                {
                    profileCount ++;
                }
            }

            profile[pos] = profileCount;
        }

        top = ( sens * LastValue( Highest( profile ) ) ) / 100;

        for( pos = 0; pos < 100; pos ++ )
        {
            if( profile[pos] > top )
            {
                LocationH = Lresistance - pos * step;
                LocationL = Lresistance - ( pos + 1 ) * step;
                PlotOHLC( LocationH, LocationH, LocationL,
                          LocationL, "", SRFillColor, styleCloud | styleNoLabel, Null, Null, Null, -9 );
            }
        }
    }

    PlotOHLC( resistance - step, resistance - step, resistance + step,
              resistance + step, "", SRFillColor, styleCloud | styleNoLabel, Null,
              Null, Null, -9 );
    PlotOHLC( support - step, support - step, support + step, support +
              step, "", SRFillColor, styleCloud | styleNoLabel, Null, Null, Null, -9 );
}

// Donchian channel
donchianDisplay = ParamToggle( "Display Dochian", "off|on" );
range = Optimize( "Donchian Period", Param( "Donchian Period", 57, 3,
                  200, 1 ), 3, 200, 1 );
donchianHigh = HHV( boxHigh, range ) + 1;
donchianLow = LLV( boxLow, range ) - 1;
range2 = Optimize( "Donchian Period2", Param( "Donchian Period2", 36, 3,
                   200, 1 ), 3, 200, 1 );
donchianHigh3 = HHV( boxHigh, range2 ) + 1;
donchianLow3 = LLV( boxLow, range2 ) - 1;
donchianFillColor = ParamColor( "Donchian Fill Color", ColorRGB( 225, 255, 190 ) );

if( donchianDisplay )
{
    Plot( donchianHigh, "DH", colorDarkGreen, styleStaircase | styleThick, Null, Null, Null, -6 );
    Plot( donchianLow, "DL", colorBrown, styleStaircase | styleThick, Null, Null, Null, -6 );
    Plot( donchianHigh3, "", colorDarkGreen, styleStaircase | styleThick, Null, Null, Null, -6 );
    Plot( donchianLow3, "", colorBrown, styleStaircase | styleThick, Null, Null, Null, -6 );
    PlotOHLC( donchianHigh, donchianHigh, donchianLow, donchianLow, "",
              donchianFillColor, styleCloud | styleNoLabel, Null, Null, Null, -9 );
}

Buy = boxHigh > Ref( donchianHigh, -1 ) AND !firstBarOfDay;
Short = boxLow < Ref( donchianLow, -1 ) AND !firstBarOfDay;
Cover = ( boxHigh > Ref( donchianHigh3, -1 ) AND boxClose > avp ) AND
        !firstBarOfDay;
Sell = ( boxLow < Ref( donchianLow3, -1 ) AND boxClose < avp ) AND !firstBarOfDay;
Sell = Short;
Cover = Buy;
FilterSignals = ParamToggle( "Filter Signals", "off|on" );

if( FilterSignals )
{
    Buy = ExRem( Buy, Sell );
    Short = ExRem( Short, Cover );
    Sell = ExRem( Sell, Buy );
    Cover = ExRem( Cover, Short );
}

ShowSignals = ParamToggle( "Show Trading Signals", "off|on" );

if( ShowSignals )
{
    PlotShapes( shapeSmallDownTriangle * Cover,   colorYellow, 0, boxHigh + 8 );
    PlotShapes( shapeSmallUpTriangle * Sell, colorYellow, 0, boxLow - 8 );
    PlotShapes( shapeSmallDownTriangle * Buy,   colorGreen, 0, boxHigh + 8 );
    PlotShapes( shapeSmallUpTriangle * Short, colorRed, 0, boxLow - 8 );
}

_N( Title = StrFormat( "{{NAME}} - {{INTERVAL}} {{DATE}},  Open %g, " + EncodeColor( colorRed )
                       + " High %g" + EncodeColor( -1 ) + ", " + EncodeColor( colorGreen ) + "Low %g" + EncodeColor( -1 )
                       + ", Close %g (%.1f%%)," + EncodeColor( colorOrange ) + " Box size %g, Range %g boxes, "
                       + EncodeColor( -1 ) + " Filter %g, "
                       + EncodeColor( colorRed ) + "HighFilter %g, " + EncodeColor( colorDarkGreen ) + "LowFilter %g, "
                       + EncodeColor( -1 )
                       + "{{VALUES}}", myO, myH, myL, myC, SelectedValue( ROC( myC, 1 ) ), boxSize, numBox, fil, Hfil, Lfil ) );
_SECTION_END();