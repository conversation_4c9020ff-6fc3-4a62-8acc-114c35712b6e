//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi Arafat=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>
#include <Options.afl>

SetOption( "DisableRuinStop", True );

//=============================DISPLAY PARAMS======================================
atrMultiplier   = Optimize( "ATR_Multiplier", 2.4, 2, 10, 0.1 ); // 4.3
atrPeriod       = Param( "ATR_Period", 5, 5, 10, 1 );
vLots           = Param( "Vlots", 140, 10, 200, 10 ); // 0.5
equityAtRisk    = Param( "Risk", 10000, 20000, 100000, 5000 );
//=================================================================================

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;
T2 = TN[atrPeriod];
StartBar = tn == T2;

//=================================================================================
// Call
//=================================================================================
SetForeign( SymbolCE );
tr = ValueWhen(StartBar, ATR( atrPeriod ));
CalcPrice = ( H + L ) / 2;
calcTrend_proc( atrPeriod, tr, atrMultiplier, CalcPrice );

CEbuffer_line_up = buffer_line_up;
CEbuffer_line_down = buffer_line_down;

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | GetPriceStyle() );
Plot( buffer_line_up, "CEtu", ColorRGB( 68, 134, 238 ), styleThick );
Plot( buffer_line_down, " // CEtd", ColorRGB( 68, 134, 238 ), styleThick );

vlots = V / RoundLotSize;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -10 );

RestorePriceArrays();

//=================================================================================
// Put
//=================================================================================
SetForeign( SymbolPE );
tr = ValueWhen(StartBar, ATR( atrPeriod ));
CalcPrice = ( H + L ) / 2;
calcTrend_proc( atrPeriod, tr, atrMultiplier, CalcPrice );

PEbuffer_line_up = buffer_line_up;
PEbuffer_line_down = buffer_line_down;

Plot( C, "Close", colorWhite, styleNoTitle | GetPriceStyle() );
Plot( buffer_line_up, "PEtu", ColorRGB( 205, 51, 51 ), styleThick );
Plot( buffer_line_down, " // PEtd", ColorRGB( 205, 51, 51 ), styleThick );
vlots = V / RoundLotSize;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -10 );

RestorePriceArrays();


TradingZone = ( tn > T2 AND tn <= 150000 );
ShortCondition = ( StrFind( Name(), SymbolCE ) AND Cross( Max( PEbuffer_line_down, PEbuffer_line_up ), Max( CEbuffer_line_down, CEbuffer_line_up ) ) ) OR
                 ( StrFind( Name(), SymbolPE ) AND Cross( Max( CEbuffer_line_down, CEbuffer_line_up ), Max( PEbuffer_line_down, PEbuffer_line_up ) ) );
Short = Ref( ShortCondition, -1 ) AND TradingZone AND C > 10;
Cover = !TradingZone; /* OR
        ( StrFind( Name(), SymbolCE ) AND CEbuffer_line_up > 0 AND C > CEbuffer_line_up ) OR
        ( StrFind( Name(), SymbolPE ) AND PEbuffer_line_up > 0 AND C > PEbuffer_line_up ) ; */
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );
SetPositionSize( 10 * RLS, spsShares );

myVar += ", PE = " + Max( PEbuffer_line_down, PEbuffer_line_up ) + ", CE = " + Max( CEbuffer_line_down, CEbuffer_line_up ) + ", 1 = " + ( StrFind( Name(), SymbolPE ) AND Cross( Max( CEbuffer_line_down, CEbuffer_line_up ), Max( PEbuffer_line_down, PEbuffer_line_up ) ) );

#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );