/*************************************** 
** Strategy: Simple Line Momentum v1.3 
** Logic:    Enter long on 2 up-moves of 
** buffer_line_up without a 
** down-move in buffer_line_down. 
***************************************/ 
 
#include <Common.afl> 
// OptimizerSetEngine( "cmae" ); 
SetChartBkColor( colorWhite ); 
 
_SECTION_BEGIN( "Params" ); 
Periods		= OptimizeNot( "Periods", 88, 20, 200, 2 ); //64, 30 
eqAtRisk	= Param( "Risk", 25000, 20000, 45000, 5000 ); 
 
StopLoss	= 1.0 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100; 
Target		= 3.5 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100; 
 
// -- Custom Bar (Box) Parameters -- 
boxSizePercent = Optimize( "Box %", 0.1, 0.1, 1, 0.1 ); 
boxSizeORbFactor = Optimize( "ORB ATR factor", 0.9, 0.2, 1, 0.1 ); 
_SECTION_END(); 
 
 
_SECTION_BEGIN( "Custom Bar OHLC" ); 
firstBar = Day() != Ref( Day(), -1 ); 
ORbHigh = ValueWhen( firstBar, High ); 
ORbLow = ValueWhen( firstBar, Low ); 
ORbCenter = ValueWhen( firstBar, ( ORbHigh + ORbLow ) / 2 ); 
tr = ATR( 5 * Periods ); 
ORBAtr = ValueWhen( firstBar, tr ); 
 
boxSize = round( Max( boxSizePercent * ORbCenter / 100, boxSizeORbFactor * ORBAtr ) / 5 ) * 5; 
 
// Rounded Custom Bars 
rH = round( H / boxSize ); 
myH = boxSize * IIf( rH < H / boxSize, rH + 1, rH ); 
 
rL = round( L / boxSize ); 
myL = boxSize * IIf( rL > L / boxSize, rL - 1, rL ); 
 
rO = round( O / boxSize ); 
myO = boxSize * IIf( rO > O / boxSize, rO - 1, rO ); 
 
rC = round( C / boxSize ); 
myC = boxSize * IIf( rC > C / boxSize, rC - 1, rC ); 
 
// Adjust myC to wick edges 
myC = IIf( myH - C < boxSize / 3, myH, myC ); 
myC = IIf( C - myL < boxSize / 3, myL, myC ); 
_SECTION_END(); 
 
_SECTION_BEGIN( "Trade Logic" ); 
Vw 		= myround( VWMA2( myC, Periods ) ); 
MA_Vw	= KAMA( myC, Periods ); 
top 	= HMA( myH, ceil( Periods / 2 ) ); 
bottom 	= HMA( myL , ceil( Periods / 2 ) ); 
// Lr 		= mybox( ( LinearReg( myC, Periods ) + myH + myL ) / 3 ); 
Lr 		= ( top + bottom ) / 2; 
Vw_1 	= Ref( MA_Vw, -1 ); 
Lr_1	= Ref( Lr, -1 ); 
 
HHV_P = HHV( myH, Periods ); 
LLV_P = LLV( myL, Periods ); 
MID_P = ( HHV_P + LLV_P ) / 2; 
 
buffer_line_up = mybox(Vw_1) + 5 * boxSize; 
buffer_line_down = mybox(Vw_1) - 5 * boxSize; 
 
No_Above_MA = BarsSince(C < Vw_1); 
 
myVar += ", No_Above_MA = " + No_Above_MA; 
 
for( i = 1; i < BarCount; i++ ) 
{ 
    buffer_line_down[i] = IIf( myC[i] > buffer_line_down[i] && buffer_line_down[i] < buffer_line_down[i - 1], buffer_line_down[i - 1], buffer_line_down[i] ); 
    buffer_line_up[i] = IIf( myC	[i] < buffer_line_up[i - 1] && buffer_line_up[i] > buffer_line_up[i - 1], buffer_line_up[i - 1], buffer_line_up[i] ); 
} 
 
Plot( buffer_line_up, "PEtu", ColorRGB( 68, 134, 238 ), styleThick ); 
Plot( buffer_line_down, " // PEtd", ColorRGB( 205, 51, 51 ), styleThick ); 
 
 
 
mode = Flip( Lr > MA_Vw + boxSize, Lr < MA_Vw - boxSize ); 
Sd = myBox( StDev( C, Periods ) ); 
 
SetChartOptions( 0, chartShowArrows | chartShowDates ); 
lastTradeOfTheDayTime	= 145000; 
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime ); 
 
f = OptimizeNot( "F", 1.4, 1, 3, 0.1 ); // 10m - 4 
 
up = myC + f * SD; 
down = myC - f * SD; 
 
// --- Entry and Exit Conditions --- 
// --- State Machine Implementation --- 
Buy = Sell = Short = Cover = 0; // Initialize arrays 
 
// State variables for the loop 
long_state = 0;  // 0: idle, 1: saw first up-move, 2: saw flat move 
short_state = 0; // 0: idle, 1: saw first down-move, 2: saw flat move 
exit = brisk = srisk = 0; 
srisk = floor( Vw_1 / boxSize ) * boxSize - C; 
hhss = 0; 
state = 0; 
 
Buyflag = Shortflag = 0; 
p_no_above = OptimizeNot("No Above", 75, 5, 200, 5); 
candle_size = (myH - myL) / boxSize; 
 
for( i = 2; i < BarCount; i++ ) 
{ 

    // --- Short State Machine --- 
    // Reset condition: if buffer_line_up moves up, reset the short state. 
    if( C[i] > buffer_line_up[i] ) 
    { 
        short_state = 0; 
    } 
 
    if( Shortflag ) 
    { 
        InShort[i] = 1; 
/* 
        if( myH[i] > Vw_1[i] AND myH[i] > hhss ) 
        { 
            hhss = myH[i]; 
        } 
 
        if( hhss > 0 AND O[i] < hhss ) 
        { 
            exit[i] = Min( hhss + boxSize[i], exit[i - 1] ); 
            hhss = 0; 
        } 
        else 
        { 
            exit[i] = exit[i - 1]; 
        } 
*/ 
        if( O[i] < Vw_1[i] ) 
        { 
            exit[i] = Min( floor( Vw_1[i] / boxSize[i] ) * boxSize[i], exit[i - 1] ); 
        } 
        else 
        { 
            exit[i] = exit[i - 1]; 
        } 
    } 
 
 
    if( Shortflag AND C[i] > exit[i] ) 
    { 
        short_state = 1; // Reset state after Short signal 
        long_state = 0;  // Also reset long state to prevent conflict 
        Cover[i] = True; 
        Shortflag = 0; 
    } 
 
    // Sequence detection for Short Entry 
    if( short_state == 1 AND myC[i] < buffer_line_down[i - 1] AND myH[i] < Vw_1[i] AND candle_size[i] < 7 AND myC[i] < myO[i] ) 
    { 
        Short[i] = 1;    // Stage 3: Second down-move detected, trigger Short 
        Shortflag = 1; 
        short_state = 3; 
        long_state = 0;  // Also reset long state to prevent conflict 
        exit[i] = floor( Vw_1[i] / boxSize[i] ) * boxSize[i]; 
    } 
    else 
		if( short_state == 0 AND No_Above_MA[i-1] > p_no_above AND myC[i] < Vw_1[i] ) 
		{ 
			short_state = 1; // Stage 1: First down-move detected 
		} 
             
    state[i] = short_state; 
} 
 
exit = myRound( exit ); 
 
bLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) ); 
sLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) ); 
 
// max_lots = Param( "lots", 12, 4, 20, 2 ); 
max_lots = floor( 7500000 / ( RoundLotSize * C ) ); 
 
bLots	= Min( max_lots, bLots ); 
sLots	= Min( max_lots, sLots ); 
 
SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares ); 
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots ); 
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( myH + boxSize ), myboxF( myL - boxSize ) ); 
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk ); 
 
bkcolor = LastValue( IIf( mode, ColorBlend( colorCustom9, colorWhite ), ColorBlend( colorCustom12, colorWhite ) ) ); 
Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, bkcolor ), styleStaircase | styleDashed, Null, Null, Null, -1 ); 
//Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, 1 ); 
PlotOHLC( myO, myH, myL, myC, "", colorDefault, styleCandle ); 
 
MA_color = IIf( Lr_1 > Vw_1 + boxSize, colorGreen, IIf( Lr_1 < Vw_1 - boxSize, colorPink, colorWhite ) ); 
// Plot( Vw , "", MA_color, ParamStyle( "Style" ) | styleThick | styleNoLabel ); 
Plot( Lr_1, "", colorGrey40, ParamStyle( "Style" ) | styleThick, Null, Null, Null, 2 ); 
Plot( MA_Vw, "", MA_color, ParamStyle( "Style" ) | styleThick | styleNoLabel ); 
 
vlots = V / RoundLotSize; 
vCheck = vlots / MA( Ref( vlots, -1 ), 35 ); 
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, myL, -25 ); 
 
// draw levels from Reaction Trend System 
if( ParamToggle( "Show RT", "0|1" ) ) 
{ 
    Plot( S1, "R1", colorRed, styleStaircase | styleNoLabel ); 
    Plot( B1, "S1", colorBrightGreen, styleStaircase | styleNoLabel ); 
} 
 
myVar += ", state = " + state; 
 
PlotShapes( shapeSmallUpTriangle * Sell   , colorDarkGrey, 0, myL ); 
PlotShapes( shapeSmallDownTriangle * Cover, colorDarkGrey, 0, myH ); 
PlotShapes( shapeSmallUpTriangle * Short  , colorRed   , 0, myL ); 
PlotShapes( shapeSmallDownTriangle * (Buy == 1 AND Buy != sigScaleOut)  , colorGreen , 0, myH ); 
 
_SECTION_END(); 
 
_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, " 
                         + "Open %0.2f, " 
                         + "High %0.2f, " 
                         + "Low %0.2f, " 
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%), " 
                         + "Box %0.1f" 
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}" 
                         + EncodeColor( colorDarkGrey ) + myVar 
                         , Open, High, Low, Close, ROC( C, 1 ), boxSize 
                       ) ); 
 
//------------------------------------------------------ 
// AlgoFoxAuto Section 
//------------------------------------------------------ 
INSTR  = "FUTIDX"; 
SYS  = "FUT"; 
// #include<AlgoFoxAuto/AlgoFoxAuto.afl> 