//------------------------------------------------------------------------------
//
//  Formula Name:    Main price chart with Rainbow & SAR
//  Author/Uploader: <PERSON><PERSON>-<PERSON>ian<PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-11-04 21:55:45
//  Origin:          Rainbow Chart
//  Keywords:        rainbow chart, sar
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=131
//  Details URL:     http://www.amibroker.com/library/detail.php?id=131
//
//------------------------------------------------------------------------------
//
//  The price chart will change color according to sar().
//
//  The red line= 20ma and Blue line = 50MA
//
//  For better visual effect.
//
//  Could someone add volume chart in this chart(like the original price chart
//  does)?
//
//------------------------------------------------------------------------------

/*Main Chart with Rainbow and SAR*/


maxgraph = 15;

graph0 = close;
graph1 = Ma(C,2);
graph2 = Ma(Ma(C,2),2);
graph3 = Ma(Ma(Ma(C,2),2),2);
graph4 = Ma(Ma(Ma(Ma(C,2),2),2),2);
graph5 = Ma(Ma(Ma(Ma(Ma(C,2),2),2),2),2);
graph6 = Ma(Ma(Ma(Ma(Ma(Ma(C,2),2),2),2),2),2);
graph7 = Ma(Ma(Ma(Ma(Ma(Ma(Ma(C,2),2),2),2),2),2),2);
graph8 = Ma(Ma(Ma(Ma(Ma(Ma(Ma(Ma(C,2),2),2),2),2),2),2),2);
graph9 = Ma(Ma(Ma(Ma(Ma(Ma(Ma(Ma(Ma(C,2),2),2),2),2),2),2),2),2);
graph10 = Ma(Ma(Ma(Ma(Ma(Ma(Ma(Ma(Ma(Ma(C,2),2),2),2),2),2),2),2),2),2);

graph0style =64;
graph1style=graph2style=graph3style=graph4style=graph5style=graph6style=graph7style=graph8style=graph9style=graph10style = 1;
graph0barcolor=iif(sar(0.05)<C,2,1);
graph1color = 3;
graph2color = 5;
graph3color =7;
graph4color = 6;
graph5color =15;
graph6color = 8;
graph7color =16;
graph8color = 10;
graph9color = 11;
graph10color = 12;
graph11=ma(C,20);
graph11style=1;
graph11color=5;
graph12=ma(C,50);
graph12style=1;
graph12color=7;

title=name() +"  "+date()+"  Open="+writeval(O)+
        ",  High ="+writeval(H)+",  Low ="+writeval(L)+
        ",  Close="+writeval(C)+",  Change="+writeif((C-ref(C,-1))>0," +"," -")+
         writeval(C-ref(C,-1))
        +"  ("+writeif((C-ref(C,-1))>0," +"," -")+writeval((C-ref(C,-1))/ref(C,-1)*100)+"%  )";


