/******************************************************************************
**
**  Strategy: State-Based Breakout Swing Trading System
**  Author:   Quantitative Trading Systems Developer
**  Version:  1.1
**  Date:     July 17, 2024
**
**  Description:
**  This AFL script implements a long-only swing trading strategy based on a
**  multi-stage, state-dependent entry logic. It is designed to identify a
**  specific breakout candle, wait for a confirmation of the high, and then
**  enter on a pullback to a dynamic trigger level.
**
**  The strategy uses a Finite State Machine (FSM) to manage the complex
**  entry sequence, ensuring all conditions are met in the correct order.
**  Stop-loss and profit targets are calculated dynamically based on the
**  range of the breakout candle.
**
******************************************************************************/

_SECTION_BEGIN( "Strategy Parameters" );
// -- Breakout Identification Parameters --
BreakoutPeriod = Param( "Breakout Lookback Period", 20, 5, 200, 1 );
NumPrevCandles = Param( "Num Candles Below Breakout", 3, 1, 10, 1 );
eqAtRisk	= Param( "Risk", 25000, 15000, 35000, 5000 );

// -- Entry Logic Parameters --
TriggerFibo = Param( "Trigger Fibonacci Level %", 90, 10, 90, 1 ) / 100;

// -- Trade Management Parameters --
PT_Multiplier = Param( "Profit Target Multiplier", 3.0, 0.5, 10, 0.1 );
SL_Multiplier = Param( "Stop Loss Multiplier", 1.5, 0.1, 5, 0.1 );

f = Param( "F", 1, 1, 3, 0.1 ); // 10m - 4
_SECTION_END();


_SECTION_BEGIN( "Indicator and Setup Calculation" );

// -- Breakout Indicator --
// The line that price must break above. Default is the Donchian Channel High.
// This can be replaced with other indicators like MA(C, Period) or BBandTop(). [4, 5]
HighLowDiff = High - Low;
boxSize = Max( 1, round( 0.5 * Percentile( HighLowDiff, BreakoutPeriod, 95 ) ) );
BreakoutLine = HHV( Ref( H, -1 ), BreakoutPeriod )  + boxSize;
BreakoutChannel = LLV( Ref( L, -1 ), BreakoutPeriod )  - boxSize;

// -- Breakout Candle Setup Condition --
// Condition 1: The candle must be bullish (Close > Open).
Cond1_BullishCandle = C > O;

// Condition 2: The close of the candle must be above the breakout line.
Cond2_CloseAboveLine = C > BreakoutLine;

// Condition 3: The N previous candles must have closed below the breakout line.
// We use the Ref() function to look back at previous candle data. [11]
Cond3_PrevClosesBelow = ( Ref( C, -1 ) < Ref( BreakoutLine, -1 ) ) AND( Ref( C, -2 ) < Ref( BreakoutLine, -2 ) ) AND( Ref( C, -3 ) < Ref( BreakoutLine, -3 ) );


// Combine all conditions to define the final BreakoutSetup signal.
// This array will be 1 (True) on bars that qualify as a breakout candle.
BreakoutSetup = Cond1_BullishCandle AND Cond2_CloseAboveLine AND Cond3_PrevClosesBelow;
_SECTION_END();


_SECTION_BEGIN( "State Machine and Trade Logic" );
// -- State Machine Implementation --
// We use static variables to store the state and related data across bars. [12, 13]
// FSM_State: 0=IDLE, 1=ARMED, 2=HIGH_TOUCHED
CapturedBreakoutHigh = CapturedBreakoutLow =  CalculatedTriggerPrice = 0;
CalculatedStopLoss =  CalculatedProfitTarget = 0;
CalculatedHighPlusRange = 0 ;
BreakoutHighTrigger = H + HighLowDiff;
BreakoutLowTrigger = L;
FSM_State_Triggered = 0;
FSM_State_history = 0;
exit = L - HighLowDiff;
brisk = 0;

Buy = Sell = Short = Cover = 0;

for( i = 3; i < BarCount; i++ )
{
    FSM_State_history[i] = FSM_State_history[i - 1];
    // --- STATE 0: IDLE ---
    // In the IDLE state, we are waiting for a new BreakoutSetup signal.
    if( FSM_State_history[i] == 0 )
    {
        if( BreakoutSetup[i] )
        {
            // A breakout candle is found. Transition to ARMED state.
            FSM_State_history[i] = 1; // State becomes ARMED

            // Capture the breakout candle's data. [14]
            // We store these values in static variables to use on future bars.
            CapturedBreakoutLow = L[i];
            CapturedBreakoutHigh = H[i];

            // Calculate the dynamic levels based on the breakout candle's range.
            BreakoutRange = CapturedBreakoutHigh - CapturedBreakoutLow;
            BreakoutHighTrigger[i] = CalculatedHighPlusRange = CapturedBreakoutHigh + ( BreakoutRange * TriggerFibo );
            BreakoutLowTrigger[i]  = CalculatedTriggerPrice = CapturedBreakoutHigh - ( BreakoutRange * TriggerFibo );
            exit[i] = CalculatedStopLoss = CapturedBreakoutLow - ( BreakoutRange * SL_Multiplier );
            CalculatedProfitTarget = CapturedBreakoutHigh + ( BreakoutRange * PT_Multiplier );
        }
    }
    // --- STATE 1: ARMED ---
    // In the ARMED state, we are waiting for the high to be touched.
    else
    {
        BreakoutHighTrigger[i] = BreakoutHighTrigger[i - 1];
        BreakoutLowTrigger[i]  = BreakoutLowTrigger[i - 1];
        exit[i] = exit[i - 1];

        if( FSM_State_history[i] == 1 )
        {

            // Check for invalidation first: if the trigger is hit before the high.
            //        if( L[i] <= CalculatedTriggerPrice )
            //        {
            // Invalidation condition met. Reset to IDLE state.
            //            FSM_State_history[i] = 0;
            //        }
            // Check for confirmation: if the breakout high is touched.
            //else
            if( H[i] >= CalculatedHighPlusRange )
            {
                // High has been touched. Transition to HIGH_TOUCHED state.
                FSM_State_history[i] = 2;
                FSM_State_Triggered[i] = True;
            }
        }
        // --- STATE 2: HIGH_TOUCHED ---
        // In the HIGH_TOUCHED state, we are waiting for a pullback to the trigger level.
        else
            if( FSM_State_history[i] == 2  AND L[i] <= CalculatedTriggerPrice )
            {
                FSM_State_history[i] = 3;
            }
            else
                if( FSM_State_history[i] == 3 )
                {
                    // Check for the entry condition.
                    if( C[i] > CalculatedTriggerPrice AND  C[i - 1] < CalculatedTriggerPrice )
                    {
                        // Pullback to trigger level occurred. Generate a Buy signal for this bar.
                        Buy[i] = 1;

                        // Set the entry price to be the trigger price for the backtest.
                        BuyPrice[i] = CalculatedTriggerPrice;
                        bRisk[i] = CalculatedTriggerPrice - CalculatedStopLoss;

                        // After buying, reset the state machine to IDLE to look for the next setup.
                        FSM_State_history[i] = 4;
                    }

                    // If price makes a new high, we remain in this state, waiting for the pullback.
                }
                else
                    if( FSM_State_history[i] == 4 )
                    {
                        // Check for the entry condition.
                        if( C[i] > CapturedBreakoutHigh )
                        {
                            // Pullback to trigger level occurred. Generate a Buy signal for this bar.
                            Sell[i] = 1;

                            // Set the entry price to be the trigger price for the backtest.
                            SellPrice[i] = CapturedBreakoutHigh;

                            // After buying, reset the state machine to IDLE to look for the next setup.
                            FSM_State_history[i] = 0;
                        }

                        if( C[i] < CalculatedStopLoss )
                        {
                            // Pullback to trigger level occurred. Generate a Buy signal for this bar.
                            Sell[i] = 1;

                            // Set the entry price to be the trigger price for the backtest.
                            SellPrice[i] = CalculatedStopLoss;

                            // After buying, reset the state machine to IDLE to look for the next setup.
                            FSM_State_history[i] = 0;
                        }

                    }
    }
}

myVar = ", FSM_State_history = " + FSM_State_history;

_SECTION_END();

bLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );

max_lots = Param( "Max Lots", 12, 1, 20, 1 );
max_lots = ceil( 4000000 / C / RoundLotSize );

bLots	= Min( max_lots, bLots );

SetPositionSize( IIf( Buy, bLots, 0 )*RoundLotSize, spsShares );

_SECTION_BEGIN( "Chart Plotting and Visualizations" );
// -- Plot Main Price Chart and Breakout Indicator --
Plot( C, "Close", colorDefault, styleCandle );
Plot( BreakoutLine, "Breakout Line", colorBlue, styleLine );
Plot( IIf( FSM_State_history > 0, exit, Null ), "Channel Low", colorPink, styleLine );

Plot( IIf( FSM_State_history == 1, BreakoutHighTrigger, Null), "Armed High", colorGreen, styleDashed );
Plot( IIf( FSM_State_history == 2, BreakoutLowTrigger, Null), "Trigger Price", colorGreen, styleDashed );
PlotShapes( BreakoutSetup * shapeSmallCircle, colorBlue, 0, H + 10 );
PlotShapes( FSM_State_Triggered * shapeSmallCircle, colorRed, 0, L - 10 );

// -- Plot Buy/Sell Arrows --
// Use PlotShapes to place arrows on the chart for trade signals.
PlotShapes( IIf( Buy, shapeUpArrow, shapeNone ), colorGreen, 0, L, -20 );
PlotShapes( IIf( Sell, shapeDownArrow, shapeNone ), colorRed, 0, H, -20 );

// -- Chart Title Information --
_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );
_SECTION_END();