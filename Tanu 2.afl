//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi <PERSON>fat=====================
//########################################################################
#include <Kolier.afl>
#include <Common.afl>
// OptimizerSetEngine( "cmae" );
SetOption( "DisableRuinStop", True );

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;

//=============================DISPLAY PARAMS======================================
atrMultiplier   = Param( "ATR_Multiplier", 2.2, 1.5, 2.5, 0.1 ); // 2
atrPeriod       = Param( "ATR_Period", 22, 5, 25, 1 ); // 15
equityAtRisk    = Param( "Risk", 10000, 20000, 100000, 5000 );
//=================================================================================

T1 = 95400;
T2 = 122800;

TradingZone = ( tn > T1 AND tn <= 150000 );
boxSize = Min( Max( floor( MA( C, 15 ) / 10 ), 1 ), 5 );

Buy = Sell = Short = Cover = 0;
max_lots = bLots = bRisk = 0;
Buyflag = 0;

tr = ATR( atrPeriod );
CalcPrice = ( H + L ) / 2;
calcTrend_proc( atrPeriod, tr, atrMultiplier, CalcPrice );

buffer_line_up = mybox( buffer_line_up );
buffer_line_down = mybox( buffer_line_down );

vlots = V / RLS;
vlotsMA = MA( Ref( vlots, -1 ), OptimizeNot( "vp", 12, 1, 50, 1 ) );
vCheck = vlots / vlotsMA;

GC = C > O + boxSize;
RC = C < O - boxSize;
ShortP = BuyP = brisk = srisk = 0;
myVar += ", boxSize = " + boxSize;

// define start/end hours in TimeNum format
StartTime = T1;
Endtime = T2;
StartBar = tn == StartTime;
EndBar = tn == Endtime;
UpB = IIf( buffer_line_up > 0 && C > buffer_line_up, ( H - buffer_line_up ) / tr, 0 );
LoB = IIf( buffer_line_down > 0 && C < buffer_line_down, ( buffer_line_down - L ) / tr, 0 );
myvar  	+= StrFormat( ", , UpB = %0.1f, LoB =  %0.1f", UpB, LoB );

pp = Optimize("pp", 2.4, 0.5, 4, 0.1);
PlotShapes(IIf(LoB > 2.7, shapeSmallCircle, shapeNone), colorPaleGreen, 0, L);
LobLBroken = LowestSince(LoB > 2.7, L) < ValueWhen(LoB > 2.7, L);
IsLoBBig = ValueWhen(LoB > 2.7, big);

myVar += ", wick_size = " + wick_size;
hBar = lBar = StartBar;

UB = LB = 0;
Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
blots = slots = 0;
Buy		= Short = Cover = Sell = 0;
buyExit = 0;
PT 		= Null;
PThit	= False;
exit	= 0;

L5 = LLV( Ref( L, -1 ), 5 );
BFbar = 0;

CP = TimeFrameGetPrice( "C", inDaily, 1, expandFirst );
OP = TimeFrameGetPrice( "O", inDaily, 1, expandFirst );
// ThreeBlackCrows		= ( blackbody ) AND Ref( blackbody, -1 ) AND Ref( blackbody, -2 ) AND O < Ref( O, -1 ) AND Ref( O, -1 ) < Ref( O, -2 ) AND ( big OR Ref( big, -1 )OR Ref( big, -2 ) );

BuyCondition = TradingZone AND vlotsMA > 100 AND vCheck > 1.1 AND Ref( L, -1 ) > 20 AND CP > 4 AND NOT LongWick;

for( i = 10; i < BarCount; i++ )
{

    UB[i] = IIf( TradingZone[i], UB[i - 1], Null );
    LB[i] = IIf( TradingZone[i], LB[i - 1], Null );

    if( TradingZone[i] AND UpB[i] > 2.7 )
    {
        UB[i] = H[i];
    }

    if( TradingZone[i] > 0 AND LoB[i] > 2.7 )
    {
        LB[i] = L[i];
    }

    if( Buyflag >= 3 )
    {
        exit[i] = Max( exit[i - 1], Max( LB[i] - boxSize[i], buyExit ) );

        if( ( vCheck[i] > 2 AND L[i] < L5[i] ) OR ( H[i] > UB[i - 1] - boxSize[i] AND LongWick[i]) )
        {
            exit[i] = Max( exit[i], L5[i] - boxSize[i] );
        }

        if( vCheck[i - 1] > 2 AND tn[i - 1] >= 150000 AND L[i] < L[i - 1] )
        {
            Sell[i] = 1;
            SellPrice[i] = L[i - 1];
            Buyflag = 0;
        }

        if( L[i] < exit[i] AND O[i] > exit[i] )
        {
            Sell[i] = 1;
            SellPrice[i] = exit[i];
            Buyflag = 0;
        }
    }

    if( tn[i] >= 151800 )
    {
        Sell[i] = 1;
        SellPrice[i] = C[i];
        Buyflag = 0;
        exit[i] = 0;
    }
/*
    if( Buyflag == 3 AND H[i] > UB[i - 1] )
    {
        Buyflag = 4;
    }


    if( Buyflag == 4 AND( ThreeBlackCrows[i] OR C[i] < buffer_line_down[i] ) )
    {
        Sell[i] = 1;
        SellPrice[i] = C[i];
        Buyflag = 1;
        BFbar = i;
    }
*/
    if( TradingZone[i] AND BuyFlag <= 1 AND LoB[i] > 2.7 )
    {
        Buyflag = 1;
        BFbar = i;
    }

    if( BuyFlag == 1 AND i - BFbar > 5 )
    {
        Buyflag = 0;
    }

    if( TradingZone[i] AND BuyFlag == 1 AND BigWhite[i] )
    {
        Buyflag = 2;
        buyExit	= L[i - 3];
    }

    if( Buyflag == 2 AND H[i] > UB[i - 1] )
    {
        Buyflag = 0;
    }

    if( Buyflag == 2 AND BuyCondition[i] AND L[i] > LB[i] - boxSize[i] AND NOT LobLBroken[i] AND Max( LB[i], buyExit ) - boxSize[i] < L[i] )
    {
        BuyPrice[i] = C[i];
        exit[i]		= Max( LB[i], buyExit ) - boxSize[i];
        buyExit		= exit[i];
        Buyflag 	= 3;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i]	= ( BuyPrice[i] - exit[i] );
        PThit		= False;
    }
}

myVar += ", BuyCondition = " + BuyCondition + ", exit = " + exit + ", BigWhite = " + big;

Plot( LB, "LB", colorPaleGreen, styleDashed );
Plot( IIf( TradingZone OR UB <= 0.1, UB,  Null ), "UB", ColorRGB( 68, 134, 238 ), styleDashed );

bLots	= Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) );
bLots	= Min( 10, bLots );
SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleNoTitle | ParamStyle( "Style" ) | GetPriceStyle() );
// PlotShapes( IIf( vCheck > 3, shapeDigit3, IIf( vCheck > 2, shapeDigit2, shapeNone ) ), colorBlack, 0, L, -25 );
Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, colorLightGrey ), styleStaircase | styleDashed |  styleNoLabel * IIf( LastValue( exit ), 0, 1 ), Null, Null, Null, -1 );

Plot( IIf( TradingZone, buffer_line_up, Null ), "", ColorRGB( 68, 134, 238 ), styleThick );
Plot( IIf( TradingZone, buffer_line_down, Null ), "", ColorRGB( 205, 51, 51 ), styleThick );

#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{DATE}}, "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), vCheck
                       ) );

_SECTION_END();