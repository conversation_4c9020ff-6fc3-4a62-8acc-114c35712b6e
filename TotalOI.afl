// Downloaded From https://www.WiseStockTrader.com
//Author :  <PERSON><PERSON>
//Credits to all authors , code adopted from open source authors and modified as per real time needs!
_SECTION_BEGIN( "Total OI" );
#include <Common.afl>

SetChartBkColor( colorWhite ); // color of outer border
SetOption( "DisableRuinStop", True );

tn = TimeNum();
TradingZone = ( tn > 93200 AND tn <= 150600 );

V1 = Ref( V, -1 );
MAV = MA( V1, 5 );
C1 = Ref( C, -1 );
OI1 = Ref( OI, -1 );
pp = Optimize( "pp", 3, 1, 10, 1 );
ROC_price = ROC( C, 5 );
ROC_vol = V / V1;
ROC_oi = MA( OI, 15 ) / MA( OI1, 15 );
HV = HHV( V1, 2 );
HI = HHV( IIf( OI1 == OI, 0, OI1 ), pp );

vol_up = ROC_vol > 1.015 AND V > HV;
price_up = ROC_price > 8 AND ROC_price < 34 AND C > C1;
oi_up = ROC_oi > 0.98;
vol_dn = ROC_vol < 0.935;
price_dn = ROC_price < -6 AND C < C1;
oi_dn = ROC_oi < 1;
ACDUp 	= myboxC( fcl + Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );
ACDDown	= myboxF( fch - Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );

long_buildup = price_up AND vol_up AND oi_up;
short_buildup = price_dn AND vol_up AND oi_up;
long_unwinding = price_up AND vol_dn AND oi_dn;
short_covering = price_dn AND vol_dn AND oi_dn;
trendSTATUS =
    WriteIf( long_buildup, "long_buildup",
             WriteIf( short_buildup, "short_buildup",
                      WriteIf( long_unwinding, "long_unwinding",
                               WriteIf( short_covering, "short_covering", " " ) ) ) );
trendCOLOR = IIf( long_buildup, colorGreen,
                  IIf( short_buildup, colorRed,
                       IIf( long_unwinding, colorBlue,
                            IIf( short_covering, colorGold, colorWhite ) ) ) );


vlots = V / RLS;

HHV5 = HHV( Ref( H, -1 ), 5 );
LLV5 = LLV( Ref( L, -1 ), 5 );

BuyExit = myboxF( LLV5 - 2 * boxSize );
ShortExit = myBoxC( HHV5  + 2 * boxSize );
InACD = C < ACDUp AND C > ACDDown;
Buy   = long_buildup  AND L > 11 AND H < 300 AND TradingZone AND vlots > 100 AND BuyExit > LLV( Ref( BuyExit, -1 ), 5 );// AND NOT InACD;
Short = short_buildup AND L > 11 AND H < 300 AND TradingZone AND vlots > 100 AND ShortExit < HHV( Ref( ShortExit, -1 ), 5 );
Sell = Cover = 0;
InBuy = InShort = 0;
BuyPrice = SellPrice = ShortPrice = CoverPrice = C;
BuyP  = ShortP = 0;
ShortI  = BuyI = 0;


for( i = 10; i < BarCount; i++ )
{
    if( Buy[i] AND NOT InBuy )
    {
        InBuy = 1;
        BuyExit[i] = Max( floor( L[i] / boxSize[i] ) * boxSize[i] - 3 * boxSize[i], BuyExit[i] );
        BuyP = C[i];
        BuyI = i;
    }

    if( InBuy AND O[i] > BuyExit[i] )
    {
        if( i > BuyI + 22 )
            BuyExit[i] = Max( BuyExit[i] + boxSize[i], BuyExit[i - 1] );
        else
            if( long_unwinding[i] )
                BuyExit[i] = Max( L[i] - 1.5 * boxSize[i], BuyExit[i - 1] );
            else
                BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
    }

    if( L[i] < BuyExit[i] OR !TradingZone[i] OR( H[i] > BuyP * 1.15 AND C[i] < C[i - 1] ) OR ROC_price[i] < -6 )
    {
        Sell[i] = 1;
        InBuy = 0;

        if( L[i] < BuyExit[i] AND O[i] > BuyExit[i] )
            SellPrice[i] = BuyExit[i];
    }

    if( Short[i] AND NOT InShort )
    {
        InShort = 1;
        ShortExit[i] = Min( ceil( H[i] / boxSize[i] ) * boxSize[i] + 2.5 * boxSize[i], ShortExit[i] );
        ShortP = C[i];
        ShortI = i;
    }

    if( InShort AND O[i] < ShortExit[i] )
    {
        ShortExit[i] = Min( ShortExit[i], ShortExit[i - 1] );
    }

    if( H[i] > ShortExit[i] OR !TradingZone[i] OR C[i] < 5 OR( L[i] < ShortP * 0.85 AND V[i] < MAV[i] AND H[i] > H[i - 1] + boxSize[i] AND C[i] > C[i - 1] ) OR ROC_price[i] > 4.5 )
    {
        Cover[i] = 1;
        InShort = 0;

        if( H[i] > ShortExit[i] AND O[i] < ShortExit[i] )
            CoverPrice[i] = ShortExit[i];
    }
}

Plot( C, "Close", ParamColor( "Color", colorBlack ), styleCandle ); 
Plot( ShortExit, "SE", colorGrey40, styleDashed ); 
Plot( BuyExit, "BE", colorGrey40, styleDashed ); 
Plot( OI , "ROI", trendCOLOR , styleHistogram | styleLeftAxisScale | styleThick ); 

Sell = Sell OR Short;
Cover = Cover OR Buy;

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );
equityAtRisk = 6000;

bRisk = BuyPrice - BuyExit;
bLots	= floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) ) );

sRisk = ShortExit - ShortPrice;
sLots	= floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) ) );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", SellPrice, CoverPrice );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
#include <Alert.afl>

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{DATE}}, "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.1f, "
                         + "OI %0.1f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , O, H, L, C, ROC( C, 1 ), V, OI
                       ) );


_SECTION_END();