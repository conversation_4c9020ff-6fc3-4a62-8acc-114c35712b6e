exportDir = "C:\\DataExport\\";

if( fmkdir( exportDir ) != 0 )
{
    Error( "Error: Could not create the export folder." );
}

// open file for writing
// file name depends on currently processed ticker
fh = fopen( StrFormat( "%s%s.txt", exportDir, Name() ), "w" );

if( fh == NULL )
{
    Error( "Error: Could not create the output file." );
}

// proceed if file handle is correct
if( fh )
{
    fputs( "Ticker,Date,Time,Open,High,Low,Close,Volume,OI \n", fh );
    y = Year();
    m = Month();
    d = Day();
    r = Hour();
    e = Minute();
    n = Second();

    for( i = 1; i < BarCount; i++ )
    {
        if( y[i] > 2023 )
        {
            fputs( Name() + ",", fh );
            ds = StrFormat( "%02.0f-%02.0f-%02.0f,", y[i], m[i], d[i] );
            fputs( ds, fh );

            ts = StrFormat( "%02.0f:%02.0f:%02.0f,", r[i], e[i], n[i] );
            fputs( ts, fh );

            qs = StrFormat( "%.4f,%.4f,%.4f,%.4f,%.0f,%.0f\n", O[i], H[i], L[i], C[i], V[i], OI[i] );
            fputs( qs, fh );
        }
    }

    if( fclose( fh ) != 0 )
    {
        Error( "Error: Could not close the output file.\n" );
    }
}

// line required by SCAN option
Buy = 0;
