//------------------------------------------------------------------------------
//
//  Formula Name:    Absolute Strength Index
//  Author/Uploader: Marcelin 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2005-03-09 15:41:05
//  Origin:          
//  Keywords:        
//  Level:           basic
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=439
//  Details URL:     http://www.amibroker.com/library/detail.php?id=439
//
//------------------------------------------------------------------------------
//
//  It's similar with RSI, but has an advantage: in the strong trend market,
//  the indicator don't stay at 0 or 100 (the limits). Try it!!! Cheers...
//
//------------------------------------------------------------------------------

   /*Developed by Tudor Marcelin - Art Invest*/

n=10; /*period*/

    A=0;
    M=0;
    D=0;

  for( i = 0; i < 10; i=i+1 ) 
{

    A=IIf(Ref(Close, - i)>Ref(Close, - i-1), A+(Ref(Close, - i)/Ref(Close, - i-1))-1,A);

    M=IIf(Ref(Close, - i)==Ref(Close, - i-1), M+1/n,M);

    D=IIf(Ref(Close, - i)<Ref(Close, - i-1), D+(Ref(Close, - i-1)/Ref(Close, - i))-1,D);
        
 }  
 ASI=IIf (D+M/2==0, 100, 100-100/(1+(A+M/2)/(D+M/2)));


  Plot(ASI,"ASI10",colorDarkBlue); 
  
  Plot(50,"",colorBlack,styleLine);
  PlotGrid(70,colorGreen);    
  PlotGrid(30,colorRed);  

GraphXSpace = 3;