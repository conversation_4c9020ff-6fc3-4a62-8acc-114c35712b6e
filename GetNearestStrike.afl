function GetNearestITMStrike(spotPrice, optionType)
{
    strikeInterval = 50; // NIFTY options are in multiples of 50
    
    // Round down to nearest strikeInterval
    baseStrike = floor(spotPrice / strikeInterval) * strikeInterval;
    
    if(optionType == "CE")
    {
        // For CE, nearest ITM strike is the highest strike below spot price
        return baseStrike;
    }
    else // PE
    {
        // For PE, nearest ITM strike is the lowest strike above spot price
        return baseStrike + strikeInterval;
    }
}

/* Example usage:
NIFTYC = Foreign("NIFTY 50.NSE_IDX", "C");

// Get nearest ITM strikes
nearestITM_CE = GetNearestITMStrike(NIFTYC, "CE");
nearestITM_PE = GetNearestITMStrike(NIFTYC, "PE");

// Example output for NIFTYC = 21367:
// CE ITM Strike = 21350 (highest strike below spot)
// PE ITM Strike = 21400 (lowest strike above spot)
*/
