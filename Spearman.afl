#include <Spearman.afl>

// Plot Spearman
Plot( SpearP, "Spearman Price" + _PARAM_VALUES() + ", Probability(" + ProbStr +      ")", colorGreen, styleLine | styleThick );


Buy = Cross(SpearP, 0) AND C > O;
Sell = Cross(SpearP, CritVal);
Short = Cross(0, SpearP) AND C < O AND SPDR;
Cover = Cross(-CritVal,SpearP);

// Plot Critical Values
Plot( CritVal, "", IIf(SPOB, colorBlue, colorOrange), styleDashed );
Plot( 0, "", colorOrange, styleDashed );
Plot( -CritVal, "", colorOrange, styleDashed );

SetPositionSize( RoundLotSize, spsShares );

PlotShapes( shapeSmallUpTriangle * Sell   , colorDarkGrey );
PlotShapes( shapeSmallDownTriangle * Cover, colorDarkGrey );
PlotShapes( shapeSmallUpTriangle * Short  , colorRed );
PlotShapes( shapeSmallDownTriangle * Buy  , colorPaleGreen );

