//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Tata Motors
//-----------------------------------------------------

#include <ReactionTrendSystem.afl>
SetChartBkColor( colorWhite ) ;

Periods		= Param( "Periods", 55, 50, 500, 5 ); //470
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 6.0 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
eqAtRisk	= Param( "Risk", 25000, 20000, 100000, 5000 );
Periods 	= IIf( StrFind( Name(), "RELIANCE" ) > 0, Param( "RP", 120, 50, 500, 5 ), Periods );

//------------------------------------------------------


Vw 		= myround( EMA( C, Periods ) );
VwLT 	= MA( Vw, 50 );
top 	= Max( LinearReg( High, Periods ), LinearReg( High, ceil( Periods / 2 ) ) );
bottom 	= Min( LinearReg( Low , Periods ), LinearReg( Low , ceil( Periods / 2 ) ) );
Lr 		= mybox( ( LinearReg( C, Periods ) + H + L ) / 3 );
H_1		= Ref( H, -1 );
L_1		= Ref( L, -1 );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( Vw > VwLT AND Lr > Vw + boxSize, Vw < VwLT AND Lr < Vw - boxSize );
Sd = StDev( C, floor( Periods / 2 ) );

SetChartOptions( 0, chartShowArrows | chartShowDates );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

ATR_mul = 1.4;
pp = Optimize( "L", 5, 1, 30, 1 );
ATR_mul = 1.4;
f = OptimizeNot( "F", 2.4, 1.0, 6, 0.1 );
ATR_f = ATR( floor( Periods / 2 ) );

up   	= Lr + f * Max( ATR_f * ATR_mul, Sd );
down 	= Lr - f * Min( ATR_f * ATR_mul, Sd );
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

L_5 = myboxF( LLV( L, 5 ) );
H_5 = myboxC( HHV( H, 25 ) );
H_3 = myboxC( HHV( H, 3 ) );
H_3_1 = Ref( H_3, -1 );
H_5_1 = Ref( H_5, -1 );
L_5_1	= Ref( L_5, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
sRisk 	= Min( Vw_1, up_1 ) - ( ( IIf( L_5_1 > O, L_5_1, C ) + C ) / 2 );
bRisk 	= ( ( IIf( H_5 > 0 AND H_5_1 < O, H_5_1, C ) + C ) / 2 ) - Max( Vw_1, down_1 );
max_sl	= myRound( eqAtRisk / RoundLotSize );

BuyCondition   =     Ref( mode, - 1 ) AND H_1 < Lr_1 AND H > Lr AND H > H_5_1 AND TradingZone;
ShortCondition = Vw < VwLT AND Lr < Vw - boxSize AND H < Lr_1 AND TradingZone;
ShortExit = myboxC(Min( H_3, HighestSince( C < Lr, H ) ));
// SPrice  = myboxF( ValueWhen( ShortCondition AND NOT Ref( ShortCondition, -1 ), L_1 ) );
// SEPrice = myboxC( ValueWhen( ShortCondition AND NOT Ref( ShortCondition, -1 ), Ref( ShortExit, -1 ) ) );

SEPrice = 0;
shortCondition1 = Vw > VwLT AND Lr > Vw - boxSize;

// myVar += ", SPrice = " + SPrice + ", ShortCondition = " + Lr_1;

// Plot( IIf( H > Lr, ShortExit, Null ), "", colorBlack, styleLine | styleDashed );
SEP = 0;
InBuy = 0;
InShort = 0;
ShortStage = 0;
SS = SPrice = HH0 = 0 ;

for( i = 1; i < BarCount; i++ )
{
    if( Shortflag )
    {
        InShort[i] = 1;
        t_exit = IIf( C[i - 1] > Lr_1[i - 1] AND C[i] < Lr_1[i], Min( ShortExit[i], exit[i - 1] ), exit[i - 1] );

        if( L[i] <= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Min( up[i - 1], C[i - 1] * ( 1 + StopLoss ) );
        }

        if( O[i] < t_exit )
        {
            exit[i] = Min( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        PT[i]   = PT[i - 1];
        exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Shortflag AND H[i] > exit[i] AND O[i] < exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( ( O[i] < exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
        ShortStage = 0;
    }

    if( Shortflag AND C[i] > exit[i] AND O[i] > exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( ( O[i] < exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
        ShortStage = 0;
    }

    if( NOT shortCondition1[i] || ( L[i] < Vw[i - 1] AND ShortStage < 2 ) || ( H[i] > HH0 AND ShortStage != 3 ) )
    {
        ShortStage = 0;
    }

    if( shortCondition1[i] )
    {
        if( ShortStage == 0 )
        {
			HH0 = Max(HH0, H[i]);
            if( L[i] > Lr_1[i] )
            {
                SPrice = H[i];
            }
        }

        if( ShortStage == 0 AND H[i] < Lr_1[i] AND L[i] > Vw[i] )
        {
            ShortStage = 1;
        }

        if( ShortStage == 1 )
        {
            SPrice = Min( SPrice, floor( L[i] / boxSize[i] ) * boxSize[i] );
            SEPrice = L[i];

            if( H[i] > Lr_1[i] )
            {
                ShortStage = 2;
            }
        }

        if( ShortStage == 2 )
        {
            SEPrice = Max( SEPrice, ceil( H[i] / boxSize[i] ) * boxSize[i] );
        }

        if( NOT Shortflag AND ShortStage == 2 AND C[i] < SPrice AND C[i] < Lr_1[i] )
        {
            ShortStage 		= 3;
            ShortP			= C[i];
            ShortPrice[i]	= ShortP;
            exit[i]			= SEPrice;
            Buyflag   		= 0;
            Shortflag 		= 1;
            Short[i]  		= 1;
            sRisk[i] 		= ( exit[i] - ShortP ); //eqAtRisk
            PT[i]			= ShortP * ( 1 - Target );
            PThit			= False;
            HH0				= 0;
        }
    }

    SS[i] = ShortStage;
    SEP[i] = SPrice;
}

myVar += ", SS = " + SS + ", SEP = " + SEP + ", Short = " + Short;

exit = myRound( exit );
PT	 = myRound( PT );
max_lots = Min( floor( 500000 / ( RoundLotSize * C * 0.2 ) ), 20 );
bLots	= Min( max_lots, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Min( max_lots, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

if( ParamToggle( "Show RT", "0|1" ) )
{
// draw levels from Reaction Trend System
    Plot( B1, "", colorBrightGreen, styleStaircase | styleNoLabel );
    Plot( S1, "", colorRed, styleStaircase | styleNoLabel );
    Plot( HBOP, "", colorOrange, styleStaircase | styleNoLabel );
    Plot( LBOP, "", colorGreen, styleStaircase | styleNoLabel );
}

Plot( IIf( exit > 0, exit, C ), "exit", IIf( exit, colorBrown, bkcolor ), styleStaircase | styleDashed, Null, Null, Null, -1 );
Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( SEP, "", colorBlue, styleDashed, Null, Null, Null, 1 );
// Plot( IIf( TradingZone AND NOT mode AND NOT InShort, Ref( L_5, -1 ), null ), "", colorRed, styleDashed, Null, Null, Null, 1 );
Plot( Vw , "", IIf( mode, colorGreen, colorPink ), ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( VwLT , "", colorBlack, ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( Lr_1, "", colorGrey40, ParamStyle( "Style" ) | styleThick, Null, Null, Null, 2 );

#include <Alert.afl>

_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );


//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTSTK";
SYS  = "FUT";
#include<AlgoFoxAuto/AlgoFoxAuto.afl>
