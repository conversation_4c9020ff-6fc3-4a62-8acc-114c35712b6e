// Expiry Day
_SECTION_BEGIN( "Total OI" );
#include <Common.afl>
#include <Options.afl>
// #include <AlgoFoxAuto/AlgoFoxButton.afl>

SetChartBkColor( colorWhite ); // color of outer border
SetOption( "DisableRuinStop", True );

tn = TimeNum();
TradingZone = ( tn >= 100400 AND tn <= 151000 );

equityAtRisk = 3000;

MAV = MA( V1, 15 );
HV = HHV( Ref( V, -1 ), 15 );

// DayVolMul = round( TimeFrameGetPrice( "V", inDaily, 0, expandLast ) / HV );
DayVolMul = round( HighestSince( firstBarOfTheDay, HV ) / HV );

myVar += ", Day_V " + DayVolMul;
Liquid = V / RLS > 1000;

vol_up = V > MAV AND DayVolMul < 8 AND Liquid;
vol_up_1 = Ref( vol_up, -1 );

OtherSymbol = GetPutName( Name() );

if( StrFind( GetPutName( Name() ), Name() ) )
{
    OtherSymbol = GetCallName( Name() );
}

SetForeign( OtherSymbol );

oi_increasing_3_flip = OI > Ref( OI, -1 );
oi_increasing_4_flip = OI > Ref( OI, -2 );
oi_increasing_5_flip = OI > Ref( OI, -3 );
oi_increasing_6_flip = OI > Ref( OI, -4 );

price_stable_or_increasing_3_flip = C >= Ref( C, -1 ) AND Ref( Ref( C, -1 ) > O, -1 );
price_stable_or_increasing_4_flip = C >= Ref( C, -2 ) AND price_stable_or_increasing_3_flip AND Ref( Ref( C, -1 ) > O, -2 );
price_stable_or_increasing_5_flip = C >= Ref( C, -3 ) AND price_stable_or_increasing_4_flip AND Ref( Ref( C, -1 ) > O, -3 );
price_stable_or_increasing_6_flip = C >= Ref( C, -4 ) AND price_stable_or_increasing_5_flip AND Ref( Ref( C, -1 ) > O, -4 );

oi_not_increasing_3_flip = NOT oi_increasing_3_flip;
oi_not_increasing_4_flip = NOT oi_increasing_4_flip;
oi_not_increasing_5_flip = NOT oi_increasing_5_flip;
oi_not_increasing_6_flip = NOT oi_increasing_6_flip;

price_not_stable_or_increasing_3_flip = C < Ref( C, -1 ) AND Ref( Ref( C, -1 ) < O, -1 );
price_not_stable_or_increasing_4_flip = C < Ref( C, -2 ) AND price_not_stable_or_increasing_3_flip AND Ref( Ref( C, -1 ) < O, -2 );
price_not_stable_or_increasing_5_flip = C < Ref( C, -3 ) AND price_not_stable_or_increasing_4_flip AND Ref( Ref( C, -1 ) < O, -3 );
price_not_stable_or_increasing_6_flip = C < Ref( C, -4 ) AND price_not_stable_or_increasing_5_flip AND Ref( Ref( C, -1 ) < O, -4 );


total_oi_not_increasing_flip = oi_not_increasing_3_flip + oi_not_increasing_4_flip + oi_not_increasing_5_flip + oi_not_increasing_6_flip;
total_price_not_stable_flip = price_not_stable_or_increasing_3_flip + price_not_stable_or_increasing_4_flip + price_not_stable_or_increasing_5_flip + price_not_stable_or_increasing_6_flip;

total_oi_increasing_flip = oi_increasing_3_flip + oi_increasing_4_flip + oi_increasing_5_flip + oi_increasing_6_flip;
total_price_stable_flip = price_stable_or_increasing_3_flip + price_stable_or_increasing_4_flip + price_stable_or_increasing_5_flip + price_stable_or_increasing_6_flip;

new_condition_flip = ( total_oi_increasing_flip >= 2 ) AND( total_price_stable_flip >= 2 );
reverse_condition_flip = ( total_oi_not_increasing_flip >= 2 ) AND( total_price_not_stable_flip >= 2 ); // Example condition

RestorePriceArrays();

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = OptimizeNot( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn = Flip( oi_Below, oi_Above );
oi_up = Flip( oi_Above, oi_Below );
HHV10 = HHV( H1, 30 );
LLV10 = LLV( L1, 30 );

CP_IH	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( H - C ) / ( .001 + H - L ) > 0.6 ) AND( ( H - O ) / ( .001 + H - L ) > 0.6 ) );
CP_H	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( C - L ) / ( .001 + H - L ) > 0.6 ) AND( ( O - L ) / ( .001 + H - L ) > 0.6 ) );
CP_EH	= ( ( C1 > O1 ) AND( O > C ) AND( O <= C1 ) AND( O1 <= C ) AND( ( O - C ) < ( C1 - O1 ) ) );
CP_UH 	= ( ( O1 > C1 ) AND( C > O ) AND( C <= O1 ) AND( C1 <= O ) AND( ( C - O ) < ( O1 - C1 ) ) );
CP_LC	= ( abs( C - O ) / ( .001 + H - L ) > .6 );
CP_LC1	= Ref( CP_LC, -1 );

price_up = H > HHV10;
price_dn = L < LLV10;
SPD12	= Sum( price_dn, 8 );
SPU12	= Sum( price_up, 8 );

BuyExit = myboxF( LLV10 - 1.5 * boxSize );
BE1 = Ref( BuyExit, -1 );
ShortExit = myboxC( HHV10 + boxSize );
SE1 = Ref( ShortExit, -1 );
ROC_price = ROC( C, 3 );
ROC1 = ROC( C, 1 );

total_oi_increasing = 0;
total_price_stable = 0;

pp = 1 + Optimize( "pp", 3, 1, 30, 1 ) / 100;
oi_increasing_3 = OI > Ref( OI, -1 ) * pp;
oi_increasing_4 = OI > Ref( OI, -2 ) * pp;
oi_increasing_5 = OI > Ref( OI, -3 ) * pp;
oi_increasing_6 = OI > Ref( OI, -4 ) * pp;

price_stable_or_increasing_3 = C >= Ref( C, -1 ) AND Ref( WhiteBody, -1 );
price_stable_or_increasing_4 = C >= Ref( C, -2 ) AND price_stable_or_increasing_3 AND Ref( WhiteBody, -2 );
price_stable_or_increasing_5 = C >= Ref( C, -3 ) AND price_stable_or_increasing_4 AND Ref( WhiteBody, -3 );
price_stable_or_increasing_6 = C >= Ref( C, -4 ) AND price_stable_or_increasing_5 AND Ref( WhiteBody, -4 );

total_oi_increasing = oi_increasing_3 + oi_increasing_4 + oi_increasing_5 + oi_increasing_6;
total_price_stable = price_stable_or_increasing_3 + price_stable_or_increasing_4 + price_stable_or_increasing_5 + price_stable_or_increasing_6;

new_condition = (
                    ( oi_up ) AND( total_price_stable >= 2 )
                    OR
                    ( total_price_stable == 4 AND oi_dn )
                );
                
// Create reverse conditions using NOT
oi_not_increasing_3 = NOT oi_increasing_3;
oi_not_increasing_4 = NOT oi_increasing_4;
oi_not_increasing_5 = NOT oi_increasing_5;
oi_not_increasing_6 = NOT oi_increasing_6;

price_not_stable_or_increasing_3 = C < Ref( C, -1 ) AND Ref( BlackBody, -1 );;
price_not_stable_or_increasing_4 = C < Ref( C, -2 ) AND price_not_stable_or_increasing_3 AND Ref( BlackBody, -2 );
price_not_stable_or_increasing_5 = C < Ref( C, -3 ) AND price_not_stable_or_increasing_4 AND Ref( BlackBody, -3 );
price_not_stable_or_increasing_6 = C < Ref( C, -4 ) AND price_not_stable_or_increasing_5 AND Ref( BlackBody, -4 );

// Combine the conditions to sum the occurrences over the periods
total_oi_not_increasing = oi_not_increasing_3 + oi_not_increasing_4 + oi_not_increasing_5 + oi_not_increasing_6;
total_price_not_stable = price_not_stable_or_increasing_3 + price_not_stable_or_increasing_4 + price_not_stable_or_increasing_5 + price_not_stable_or_increasing_6;

// Create a new condition based on the totals for the reverse logic
reverse_condition = ( total_oi_not_increasing >= 3 ) AND( total_price_not_stable >= 3 ) AND C < stMA; // Example condition

myVar += ", total_oi_increasing = " + total_oi_increasing + ", total_price_stable = " + total_price_stable;

fc = Foreign( OtherSymbol, "C" );
tooFar = C > ( BuyExit + floor( equityAtRisk / RLS ) );
ShortCondition	= 0;
BuyCondition	= new_condition AND TradingZone AND WhiteBody AND NOT price_up AND NOT new_condition_flip AND NOT IsNull( fc[BarCount - 1] ) AND L > 20;
myVar += ", BuyCondition = " + ( new_condition AND TradingZone AND WhiteBody AND NOT price_up );

BuyP = ShortP = 0;
InShort = InBuy = 0;
ShortI = BuyI = BuyL = ShortL = 0;
Short = Cover = 0 ;
Buy = Sell = 0;

BuyFlag = ShortFlag = False;
BF = SF = 0;
SPT = LPT = 0;
HatBC = LatSC = LatBC = 0;
SellPrice = CoverPrice = C;
BTC = STC = 0;
SOI = BOI = 0;

for( i = 10; i < BarCount; i++ )
{
    if( InBuy )
    {

        if( C[i] > LPT AND O[i] > Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] ) )
        {
            BuyExit[i] = Max( C[i] - 2 * floor( equityAtRisk / RLS ), BuyExit[i] );
        }

        BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
    }

    if( InBuy AND OI[i] < BOI )
    {
        BTC = True;
    }

    BOI = Max( BOI, OI[i] );

    if( L[i] < BuyExit[i] AND InBuy )
    {
        Sell[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
        BuyI = i;
        SellPrice[i] = BuyExit[i];
    }

    if( ( !TradingZone[i]
            OR( vol_up[i - 1] AND( LongWick[i - 1] OR CP_IH[i - 1] ) AND C[i] < C1[i] )
            OR( ( price_up[i] OR price_up[i - 1] ) AND( LongWick2[i] OR LongWick[i] ) AND ( vol_up[i] OR vol_up[i - 1] ) )
            OR( i > BuyI + 18 AND H[i - 1] > BuyP AND C[i] < BuyP )
            OR( i > BuyI + 6 AND oi_up[i] AND C[i] < BuyP )
        ) AND InBuy )
    {
        BuyExit[i] = Max( BuyExit[i], floor( ( L[i] - boxSize[i] ) / boxSize[i] ) * boxSize[i] );
    }

    if( H[i] > BuyP + 40 AND InBuy )
    {
        BuyExit[i] = Max( BuyExit[i], floor( L[i] / boxSize[i] ) * boxSize[i] );
    }

    if( BuyFlag )
        HatBC[i] = Min( HatBC[i - 1], H[i] );

    if( BuyCondition[i] AND NOT InBuy AND NOT InShort )
    {
        if( NOT BuyFlag )
        {
            HatBC[i] = H[i];
            LatBC = floor( ( L[i] - boxSize[i] ) / boxSize[i] ) * boxSize[i];
        }

        ShortFlag = False;
        BuyFlag = True;
        BuyI = i;
        BuyL = Min( L[i - 1], L[i] );
    }

    if( NOT TradingZone[i] OR i > BuyI + 8 OR L[i] < LatBC )
    {
        BuyFlag = False;
    }
    
        BF[i] = BuyP;


    if( BuyFlag AND NOT CP_IH[i] AND H[i] >= HatBC[i] AND wick_size[i] <= 0.4 AND WhiteBody[i] AND (C[i] > BuyP) )
    {
        Buy[i] = 1;
        BuyP = C[i];
        BuyI = i;
        BuyExit[i] = Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] );
        InBuy = 1;
        InShort = 0;
        BuyFlag = False;
        ShortFlag = False;
        LPT = C[i] + 0.7 * floor( equityAtRisk / RLS );
        BTC = False;
        BOI = OI[i];
    }
 
    if( NOT TradingZone[i] OR stMA[i] > stMA[i  - 1] )
    {
        BuyP = 0;
    }
}

myVar += ", HatBC " + HatBC + ", LatSC = " + LatSC;
myVar += ", BF = " + BF + ", BC = " + BuyCondition;
myVar += ", SF = " + SF + ", SC = " + ShortCondition;
myVar += ", LongWick = " + wick_size + ", LongWick2 = " + wick_size2;

Sell = Sell OR NOT TradingZone OR Short;
Cover = Cover OR NOT TradingZone OR C < 5 OR Buy;
// Buy = Buy AND isITM AND ITMRange == 1 AND daysToExpiry < 7;
// Short = Short AND isITM AND ITMRange == 2 AND daysToExpiry < 7;
Buy = Buy AND daysToExpiry < 7;
// Short = Short AND isITM AND daysToExpiry < 7;
InBuy = InShort = 0;
BuyPrice = ShortPrice = C;

SetChartBkColor( LastValue( IIf( isITM AND ITMRange == 2, colorLightGrey, colorWhite ) ) );

Plot( C, "", ParamColor( "Color", colorBlack ), styleCandle );
Plot( ShortExit, "SE", IIf( SF, colorRed, colorGrey40 ), styleDashed );
Plot( BuyExit, "BE", IIf( BF, colorLime, colorGrey40 ), styleDashed );
PlotShapes( BuyCondition*shapeSmallCircle, colorGreen, 0, L - 1 );
PlotShapes( reverse_condition*shapeSmallCircle, colorRed, 0, H + 10 );
Plot( stMA, "", IIf( m_Above, colorLime, IIf( m_Below, colorBrown, colorWhite ) ), styleLine | styleThick );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

bRisk = BuyPrice - BuyExit;
bLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) ) ) );

sRisk = ShortExit - ShortPrice;
sLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) ) ) );
myvar 	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", BuyExit, ShortExit );
myvar 	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
#include <Alert.afl>

_N( Title = StrFormat( EncodeColor( colorDarkGrey )
                       + "H %0.2f, "
                       + "L %0.2f, "
                       + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                       + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                       + EncodeColor( colorDarkGrey ) + myVar
                       , H, L, C, ROC( C, 1 )
                     ) );

_SECTION_END();
