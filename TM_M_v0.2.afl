//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Tata Motors
//-----------------------------------------------------

#include <ReactionTrendSystem.afl>
// OptimizerSetEngine( "cmae" );

SetChartOptions( 0, chartShowArrows | chartShowDates );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

//----------------------------------------------------------------
// SECTION: PARAMETERS
//----------------------------------------------------------------
_SECTION_BEGIN( "Parameters" );
Periods		= Optimize( "Periods", 35, 10, 200, 5 ); //470
StopLoss	= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 6.0 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
eqAtRisk	= Param( "Risk", 25000, 20000, 100000, 5000 );

// -- Short Strategy Params --
Periods_Short = Param( "Periods (Short)", 62, 20, 200, 2 );  // 50 - 70
boxSizePercent = Param( "Box %", 1, 0.1, 2, 0.1 );
boxSizeORbFactor = Param( "ORB ATR factor", 1.2, 0.2, 3, 0.1 );
p_no_above = Param( "No Above MA (Short)", 85, 5, 200, 5 );
_SECTION_END();

//----------------------------------------------------------------
// SECTION: CUSTOM BAR & INDICATORS
//----------------------------------------------------------------
_SECTION_BEGIN( "Calculations" );

// -- Custom Bar OHLC (for Short Logic) --
firstBar = Day() != Ref( Day(), -1 );
ORbHigh = ValueWhen( firstBar, High );
ORbLow = ValueWhen( firstBar, Low );
ORbCenter = ValueWhen( firstBar, ( ORbHigh + ORbLow ) / 2 );
tr = ATR( 5 * Periods );
ORBAtr = ValueWhen( firstBar, tr );
ORBAtr = IIf( ORBAtr == 0, ATR( 36 ), ORBAtr );
s_box_size = round( Max( 1, Max( boxSizePercent * ORbCenter / 100, boxSizeORbFactor * ORBAtr ) / 0.5 ) ) * 0.5;
myVar += ", box = " + ( s_box_size );

rH = round( H / s_box_size );
myH = s_box_size * IIf( rH < H / s_box_size, rH + 1, rH );
rL = round( L / s_box_size );
myL = s_box_size * IIf( rL > L / s_box_size, rL - 1, rL );
rO = round( O / s_box_size );
myO = s_box_size * IIf( rO > O / s_box_size, rO - 1, rO );
rC = round( C / s_box_size );
myC = s_box_size * IIf( rC > C / s_box_size, rC - 1, rC );

myC = IIf( myH - C < s_box_size / 3, myH, myC );
myC = IIf( C - myL < s_box_size / 3, myL, myC );

// -- Short Logic Indicators --
Vw_Short 		= KAMA( C, Periods_Short );
Vw_Short_1 		= Ref( Vw_Short, -1 );
top_Short = HMA( myH, ceil( Periods_Short / 2 ) );
bottom_Short = HMA( myL, ceil( Periods_Short / 2 ) );
Lr_Short = ( top_Short + bottom_Short ) / 2;
buffer_line_up	= ( round( Vw_Short_1 / s_box_size ) + 5 ) * s_box_size;
buffer_line_down = ( round( Vw_Short_1 / s_box_size ) - 5 ) * s_box_size;
No_Above_MA 	= BarsSince( C < Vw_Short_1 );
candle_size 	= ( myH - myL ) / s_box_size;
s_mode = Flip( Lr_Short > Vw_Short + s_box_size, Lr_Short < Vw_Short - s_box_size );

// -- Long Logic Indicators --
Vw_H 		= mybox( VWMA( myH, Periods ) );
Vw_L 		= mybox( VWMA( myL, Periods ) );
Vw_Long 	= ( Vw_H + Vw_L ) / 2;
top 		= Max( LinearReg( myH, Periods ), LinearReg( myH, ceil( Periods / 2 ) ) );
bottom 		= Min( LinearReg( myL , Periods ), LinearReg( myL , ceil( Periods / 2 ) ) );
Lr_Long 	= mybox( ( top + bottom ) / 2 );
Vw_Long_1 	= Ref( Vw_Long, -1 );
Lr_Long_1	= Ref( Lr_Long, -1 );

mode = Flip( Lr_Long > Vw_Long + boxSize, Lr_Long < Vw_Long - boxSize );
Sd = StDev( C, floor( Periods / 2 ) );

ATR_mul = 1.4;
ATR_mul = 1.4;
f = OptimizeNot( "F", 2.4, 1.0, 6, 0.1 );
ATR_f = ATR( floor( Periods / 2 ) );

up   	= Lr_Long + f * Max( ATR_f * ATR_mul, Sd );
down 	= Lr_Long - f * Min( ATR_f * ATR_mul, Sd );
up_1 	= Ref( up, -1 );
down_1 	= Ref( down, -1 );

pp = OptimizeNot( "pp", 8, 2, 30, 1 );
L_5 = myboxF( LLV( myL, pp ) );
H_5 = myboxC( HHV( myH, pp ) );
H_3 = myboxC( HHV( H, 10 ) );
H_3_1 = Ref( H_3, -1 );
H_5_1 = Ref( H_5, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;
state =  0;
s_exit = ceil( Max( Vw_Short_1, H_3_1 ) / s_box_size ) * s_box_size;
b_exit = floor( Vw_Long / s_box_size ) * s_box_size;
max_sl_Long = myboxC( eqAtRisk / RoundLotSize );
srisk = s_exit - C;
bRisk 	= C - b_exit;

short_state = 0; // State machine for short logic

bl_up_dn = bl_up_up = bl_down_dn = bl_down_up = 0;

_SECTION_END();

//----------------------------------------------------------------
// SECTION: TRADING LOGIC
//----------------------------------------------------------------
_SECTION_BEGIN( "Trade Execution" );

BuyCondition = Ref( mode, - 1 ) AND(
                   ( L > Lr_Long AND myH > H_5_1 AND Ref( myH, -1 ) < H_5_1 )  OR( myH > Ref( buffer_line_up, -1 ) AND Ref( myH, -1 ) <= Ref( buffer_line_up, -1 ) )
               ) AND TradingZone AND WhiteBody AND brisk < max_sl_Long;
InBuy = 0;
InShort = 0;

for( i = 1; i < BarCount; i++ )
{
	// --- Ratchet buffer lines for short logic ---
    buffer_line_down[i] = IIf( C[i] > buffer_line_down[i - 1]   AND buffer_line_down[i] < buffer_line_down[i - 1], buffer_line_down[i - 1], buffer_line_down[i] );
    buffer_line_up[i]   = IIf( C[i] < buffer_line_up[i - 1] AND buffer_line_up[i]   > buffer_line_up[i - 1]  , buffer_line_up[i - 1]  , buffer_line_up[i] );

    bl_down_dn[i] = IIf( buffer_line_down[i] > buffer_line_down[i - 1], False, IIf( buffer_line_down[i] < buffer_line_down[i - 1], True, bl_down_dn[i - 1] ) );
    bl_up_dn[i] = IIf( buffer_line_up[i] > buffer_line_up[i - 1], False, IIf( buffer_line_up[i] < buffer_line_up[i - 1], True, bl_up_dn[i - 1] ) );

    bl_down_up[i] = IIf( buffer_line_down[i] < buffer_line_down[i - 1], False, IIf( buffer_line_down[i] > buffer_line_down[i - 1], True, bl_down_up[i - 1] ) );
    bl_up_up[i] = IIf( buffer_line_up[i] < buffer_line_up[i - 1], False, IIf( buffer_line_up[i] > buffer_line_up[i - 1], True, bl_up_up[i - 1] ) );

    ShortTrend[i] = bl_down_dn[i] && bl_up_dn[i];
    LongTrend[i] = bl_down_up[i] && bl_up_up[i];

    if( Buyflag )
    {
        InBuy[i] = 1;
        t_exit =  IIf( Buyflag == 1 OR L_5[i] > b_exit[i], b_exit[i], L_5[i] );

        if( O[i] > t_exit AND( buffer_line_up[i] > buffer_line_up[i - 1] ) OR( Buyflag == 2 ) )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        exit[i] = floor( exit[i] / boxSize[i] ) * boxSize[i];
    }

    if( Buyflag AND L[i] < exit[i] AND O[i] > exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( ( O[i] > exit[i] ) AND NOT firstBarOfTheDay[i], exit[i], C[i] );
    }

    if( Buyflag == 1 AND NOT mode[i - 1] AND myL[i] < myL[i - 1]  )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = C[i];
    }

    // --- SHORT POSITION MANAGEMENT ---
    if( Shortflag )
    {
        exit[i] = exit[i - 1];

        if( O[i] < Vw_Short_1[i] )
        {
            exit[i] = Min( s_exit[i], exit[i - 1] );
        }
    }

    // COVER (Exit Short Position)
    if( Shortflag AND C[i] > exit[i] )
    {
        Cover[i] = True;
        Shortflag = 0;
        short_state = 0; // Re-arm short state
    }

    if( NOT Buyflag AND( BuyCondition[i]
                         OR( mode[i - 1 ] AND C[i] > buffer_line_up[i - 1] AND C[i - 1] < buffer_line_up[i - 1] AND TradingZone[i] AND WhiteBody[i] AND brisk[i] < max_sl_Long[i] )
                       ) AND LongTrend[i] )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= b_exit[i];
        Buyflag 	= 1;
        Shortflag  	= 0;
        short_state = 0;
        Buy[i] 		= 1;
        bRisk[i] 	= BuyP - exit[i];
        PT[i]		= BuyP * ( 1 + Target );
        PThit		= False;
    }

    if( NOT Buyflag AND myH[i] < buffer_line_down[i] )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= L_5[i];
        Buyflag 	= 2;
        Shortflag  	= 0;
        short_state = 0;
        Buy[i] 		= 1;
        bRisk[i] 	= BuyP - exit[i];
        PT[i]		= BuyP * ( 1 + Target );
        PThit		= False;
    }

    // --- SHORT ENTRY LOGIC ---
    if( C[i] > buffer_line_up[i] ) short_state = 0;

    if( short_state == 2 AND myC[i] < Vw_Short_1[i] AND candle_size[i] < 6 AND myC[i] < myO[i] AND NOT Shortflag )
    {
        Short[i] = 1;
        Shortflag = True;
        short_state = 3;
        exit[i] = s_exit[i];
    }
    else
        if( short_state == 1 AND Lr_Short[i] > Vw_Short_1[i] )
        {
            short_state = 2; // Pull back
        }
        else
            if( NOT Buyflag AND short_state == 0 AND myC[i] < buffer_line_down[i - 1] AND( Lr_Short[i] < Vw_Short_1[i] OR candle_size[i] > 5 ) )
            {
                short_state = 1; // Armed state
            }

    state[i] = short_state;
}

// Cover = Cover OR Buy;

exit = myRound( exit );
PT	 = myRound( PT );
max_lots = Min( floor( 500000 / ( RoundLotSize * C * 0.2 ) ), 20 );
bLots	= Min( max_lots, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Min( max_lots, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );
bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( state == 3, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

myVar += ", LongTrend = " + LongTrend;

_SECTION_END();

//----------------------------------------------------------------
// SECTION: PLOTTING
//----------------------------------------------------------------
_SECTION_BEGIN( "Chart Display" );

// -- Price and Exit Levels --
PlotOHLC( myO, myH, myL, myC, "", colorDefault, styleCandle );
Plot( IIf( exit, exit, Null ), "exit", colorBrown, styleStaircase | styleDashed, Null, Null, Null, -1 );

// -- Indicators from Short Logic --
Plot( buffer_line_up, "", ColorRGB( 68, 134, 238 ) );
Plot( buffer_line_down, "", ColorRGB( 205, 51, 51 ) );
Plot( Lr_Short, "", colorGrey40 );
Plot( Vw_Short, "VWMA (Short)", IIf( mode, colorGreen, colorPink ), styleThick | styleNoLabel );

// -- Trade Shapes --
PlotShapes( shapeSmallUpTriangle * Buy, colorGreen, 0, myL, -10 );
PlotShapes( shapeSmallDownTriangle * Sell, colorPaleGreen, 0, myH, 10 );
PlotShapes( shapeSmallDownTriangle * Short, colorRed, 0, myH, 10 );
PlotShapes( shapeSmallUpTriangle * Cover, colorPink, 0, myL, -10 );
_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , High, Low, Close, ROC( C, 1 )
                       ) );

_SECTION_END();
//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTSTK";
SYS  = "FUT";
// #include<AlgoFoxAuto/AlgoFoxAuto.afl>
