//########################################################################
//================= www.stockbangladesh.com ===============
//=======================Mehedi <PERSON>t=====================
//########################################################################
#include <Kolier.afl>
#include_once <Common.afl>

// OptimizerSetEngine( "cmae" );
SetChartBkColor( colorWhite ) ;

//=============================DISPLAY PARAMS======================================
atrMultiplier	= Param( "ATR_Multiplier", 4.3, 3, 9, 0.1 );
atrPeriod       = Param( "ATR_Period", 5, 5, 10, 1 );
vLots           = Param( "Vlots", 140, 10, 200, 10 ); // 0.5

Periods			= Param( "Periods", 150, 50, 400, 10 ); //470
Periods 		= IIf( StrFind( Name(), "RELIANCE" ) > 0, 350, Periods );
Periods 		= IIf( StrFind( Name(), "BANKNIFTY" ) > 0, 280, Periods );
StopLoss		= 0.5 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target			= 6 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;
equityAtRisk	= Param( "Risk", 25000, 15000, 35000, 5000 );
pp 				= Param( "L", 15, 5, 30, 1 );
msl 			= Param( "MSL", 350, 50, 350, 10 );

//=================================================================================



tr = ATR( atrPeriod );
CalcPrice = ( H + L ) / 2;
calcTrend_proc( atrPeriod, tr, atrMultiplier, CalcPrice );

Buy = Sell = Short = Cover = 0;

#include <BNF - LR v2.1.afl>

lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

Short = Buy = Sell = Cover = 0;
CV = ( V / RoundLotSize ) >= Vlots;
OBuy 	= !IsEmpty( Ref( buffer_line_up, -1 ) )   && Ref( L, -1 ) >  Ref( buffer_line_up, -1 )   AND H > HHV( Ref( H, -1 ), 12 ) && TradingZone AND C > O; // AND CV ;
OSell 	= !IsEmpty( Ref( buffer_line_down, -1 ) ) && C < Ref( buffer_line_down, -1 ) && !firstBarOfTheDay && C < Ref( C, -1 );
InOBuy	= Flip( OBuy, OSell );

myVar += ", InBuy = " + InBuy + ",  InShort = " + InShort + ", F = " + IIf(StrFind(Name(), "CE"), InBuy, InShort);

Buy = InOBuy AND IIf(StrFind(Name(), "CE"), InBuy, InShort);
Sell = OSell;

SellPrice = C;
// myVar += ", C1 = " + ( !IsEmpty( Ref( buffer_line_down, -1 ) ) ) + ", C2 = " + ( C < Ref( buffer_line_down, -1 ) ) + ", C3 = " + ( !firstBarOfTheDay ) + ", C4 = " + ( CL );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );

bRisk = IIf( buffer_line_up, ( C - buffer_line_up ), 0 );
bLots	= Max( 0, floor( equityAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
max_lots = Min( floor( 500000 / RoundLotSize*C ), 20 );
bLots	= Min( max_lots, bLots );
myvar  	+= ", lots = " + bLots;
myvar	+= ", risk = " + round( RoundLotSize*bRisk );

SetPositionSize( bLots*RoundLotSize, spsShares );

bkcolor = LastValue( IIf( BuyCondition, ColorBlend( colorCustom9, colorWhite ), IIf( ShortCondition, ColorBlend( colorCustom12, colorWhite ), colorLightGrey ) ) );
SetChartBkColor( bkcolor );

Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, -1 );
Plot( buffer_line_up, "tu", ColorRGB( 68, 134, 238 ), styleThick );
Plot( buffer_line_down, " // td", ColorRGB( 205, 51, 51 ), styleThick );

#include <Alert.afl>

_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + " - "
                         + "O %0.2f, "
                         + "H %0.2f, "
                         + "L %0.2f, "
                         + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                         + "Vol %0.f, "
                         + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 ), V / RoundLotSize
                       ) );

