//AFL Point & Figure Overlay on Candlestick Chart
//Displays normal candlestick chart with P&F levels and signals overlaid
//Based on PFC v1.1.afl by <PERSON>
//Modified for overlay display

Version( 4.40 );
SetBarsRequired( 100000, 100000 );

// === CANDLESTICK CHART (Base Chart) ===
// Plot the normal candlestick chart first
Plot( C, "Close", colorBlack, styleCandle );

// === POINT & FIGURE CALCULATION ===
//Size for P&F boxes
Box = boxsize = Param("Box Size", 3, 0.1, 50, 0.1);
HX = round( ( H / box ) * 10 ) / 10;
LX = round( ( L / box ) * 10 ) / 10;
RH = floor( HX );
FL = ceil( LX );

// initialize first element
j = 0;
myReverse = Param("Reverse", 3, 1, 10, 1);                      // reversal requirement

PFC[j] = FL[0];
PFO[j] = PFC[j] + 1;
down = 1;                  // By default the first bar is a down bar.
up = 0;
swap = 0;

// perform the loop that produces PF Chart
for( i = 1; i < BarCount; i++ )
{
    if( FL[i] <= PFC[j] - 1 && down )      //continue down
    {
        PFC[j] = FL[i];
        PFO[j] = PFC[j] + 1;
    }
    else
    {
        if( RH[i] >= PFC[j] + myReverse && down ) //Change direction to up
        {
            j++;
            swap = 1;
            PFC[j] = RH[i];
            PFO[j] = PFC[j] - 1;
        }
    }

    if( RH[i] >= PFC[j] + 1 && up )        //Continue up
    {
        PFC[j] = RH[i];
        PFO[j] = PFC[j] - 1;
    }
    else
    {
        if( FL[i] <= PFC[j] - myReverse && up )  //Change direction to down
        {
            j++;
            PFC[j] = FL[i];
            PFO[j] = PFC[j] + 1;
            swap = 1;
        }
    }

    if( swap )
    {
        swap = 0;

        if( up )
        {
            up = 0;
            down = 1;
        }
        else
        {
            up = 1;
            down = 0;
        }
    }
}

delta = BarCount - j - 1;

PFO = Ref( PFO, -delta );
PFC = Ref( PFC, -delta );

// Calculate P&F OHLC values
PF_H = IIf( Ref( PFC, -1 ) > Ref( PFO, -1 ), Ref( HHV( PFC, 1 ), -1 ) - 1, Max( PFO, PFC ) ) * Box;
PF_L = IIf( Ref( PFC, -1 ) < Ref( PFO, -1 ), Ref( LLV( PFC, 1 ), -1 ) + 1, Min( PFO, PFC ) ) * Box;
PF_O = IIf( Ref( PFC, -1 ) > Ref( PFO, -1 ), Ref( HHV( PFC, 1 ), -1 ) - 1, IIf( Ref( PFC, -1 ) < Ref( PFO, -1 ), Ref( LLV( PFC, 1 ), -1 ) + 1, PFO ) ) * Box;
PF_C = PF_O + Box * IIf( PFC > PFO, 1, -1 );

// === P&F OVERLAY ELEMENTS ===

// --- Identify new up and down columns
NewUp   = PFC > Ref(PFC, -1) AND PFC > PFO;
NewDown = PFC < Ref(PFC, -1) AND PFC < PFO;

// --- Support/Resistance levels at reversals
SuppLevel = IIf(NewUp,   PFO * Box, Null);   // low of new up column
ResLevel  = IIf(NewDown, PFO * Box, Null);   // high of new down column

// --- Fill forward the most recent levels
Support    = ValueWhen(NewUp,   SuppLevel, 1);
Resistance = ValueWhen(NewDown, ResLevel, 1);

// === OVERLAY PLOTS ===

// Plot P&F Support and Resistance levels
ShowSR = Param("Show Support/Resistance", 1, 0, 1, 1);
if( ShowSR )
{
    Plot( Support, "P&F Support", colorGreen, styleLine | styleThick | styleNoRescale );
    Plot( Resistance, "P&F Resistance", colorRed, styleLine | styleThick | styleNoRescale );
}

// Plot P&F column changes as vertical lines
ShowColumns = Param("Show P&F Columns", 1, 0, 1, 1);
if( ShowColumns )
{
    // Mark new P&F columns with vertical lines
    PF_NewColumn = NewUp OR NewDown;
    Plot( IIf(PF_NewColumn, H * 1.02, Null), "P&F Column", colorBlue, styleHistogram | styleOwnScale );
}

// === P&F SIGNALS ===
ShowSignals = Param("Show P&F Signals", 1, 0, 1, 1);

if( ShowSignals )
{
    // --- Signal conditions ---
    PrevRes  = Ref(Resistance, -1);
    PrevSupp = Ref(Support, -1);

    Buy   = NewUp   AND Support > PrevSupp;
    Short = NewDown AND Resistance < PrevRes;

    // Exits
    Sell  = L < Support OR Short;                 // sell if support broken or short triggered
    Cover = H > Resistance OR Buy;                // cover if resistance broken or buy triggered

    // --- Price assignment for markers ---
    BuyPrice   = ValueWhen(Buy,   C);
    ShortPrice = ValueWhen(Short, C);
    SellPrice  = ValueWhen(Sell,  Support);
    CoverPrice = ValueWhen(Cover, Resistance);

    // --- Clean signals ---
    Buy   = ExRem(Buy, Sell);
    Sell  = ExRem(Sell, Buy);
    Short = ExRem(Short, Cover);
    Cover = ExRem(Cover, Short);

    // --- Plot signals on candlestick chart ---
    PlotShapes( Buy   * shapeUpArrow,     colorLime, 0, L * 0.98,  -15 );
    PlotShapes( Short * shapeDownArrow,   colorRed,   0, H * 1.02, -15 );
    PlotShapes( Sell  * shapeDownTriangle,colorOrange, 0, H * 1.02, -10 );
    PlotShapes( Cover * shapeUpTriangle,  colorLightBlue, 0, L * 0.98, -10 );
}

// === P&F BOX OVERLAY ===
ShowPFBoxes = Param("Show P&F Boxes", 0, 0, 1, 1);
if( ShowPFBoxes )
{
    // Plot P&F boxes as thin lines overlaid on candlesticks
    Plot( PF_C, "P&F Close", IIf( PFC > PFO, colorBlue, colorRed ), 
          styleLine | styleThick | styleNoLabel | styleNoRescale );
}

// === TITLE AND DISPLAY ===
GraphXSpace = 2;
Title = Name() + " - Candlestick with P&F Overlay" +
        "\nBox: " + NumToStr(box, 1.2) + 
        ", Reversal: " + myReverse +
        "\nSupport: " + WriteVal(Support, 1.2) + 
        ", Resistance: " + WriteVal(Resistance, 1.2) +
        "\n{{VALUES}}";

// === PARAMETER PANEL ===
_SECTION_BEGIN("Point & Figure Overlay Settings");
// Box size and reversal already defined above
_SECTION_END();

_SECTION_BEGIN("Display Options");
// ShowSR, ShowColumns, ShowSignals, ShowPFBoxes already defined above
_SECTION_END();
