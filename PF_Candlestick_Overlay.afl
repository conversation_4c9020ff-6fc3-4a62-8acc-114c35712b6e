//AFL Point & Figure Overlay on Candlestick Chart
//Displays normal candlestick chart with P&F levels and signals overlaid
//Based on PFC v1.1.afl by <PERSON>
//Modified for proper time-aligned overlay display

// === CANDLESTICK CHART (Base Chart) ===
// Plot the normal candlestick chart first
Plot( C, "Close", colorBlack, styleCandle );

// === POINT & FIGURE CALCULATION ===
//Size for P&F boxes
Box = boxsize = Param("Box Size", 3, 0.1, 50, 0.1);
HX = round( ( H / box ) * 10 ) / 10;
LX = round( ( L / box ) * 10 ) / 10;
RH = floor( HX );
FL = ceil( LX );

// Arrays to track P&F state for each bar
PF_Close = Null;     // P&F close level for each time bar
PF_Open = Null;      // P&F open level for each time bar
PF_Direction = Null; // 1 for up column, -1 for down column
PF_NewColumn = False; // True when a new P&F column starts

// initialize first element
j = 0;
myReverse = Param("Reverse", 6, 1, 10, 1);

// Temporary arrays for P&F calculation
PFC_temp = Null;
PFO_temp = Null;

PFC_temp[j] = FL[0];
PFO_temp[j] = PFC_temp[j] + 1;
down = 1;
up = 0;
swap = 0;

// Set initial values for first bar
PF_Close[0] = PFC_temp[j] * Box;
PF_Open[0] = PFO_temp[j] * Box;
PF_Direction[0] = -1; // Start with down
PF_NewColumn[0] = True;

// perform the loop that produces time-aligned P&F data
for( i = 1; i < BarCount; i++ )
{
    // Copy previous values as default
    PF_Close[i] = PF_Close[i-1];
    PF_Open[i] = PF_Open[i-1];
    PF_Direction[i] = PF_Direction[i-1];
    PF_NewColumn[i] = False;

    if( FL[i] <= PFC_temp[j] - 1 && down )      //continue down
    {
        PFC_temp[j] = FL[i];
        PFO_temp[j] = PFC_temp[j] + 1;
        // Update current bar values
        PF_Close[i] = PFC_temp[j] * Box;
        PF_Open[i] = PFO_temp[j] * Box;
    }
    else
    {
        if( RH[i] >= PFC_temp[j] + myReverse && down ) //Change direction to up
        {
            j++;
            swap = 1;
            PFC_temp[j] = RH[i];
            PFO_temp[j] = PFC_temp[j] - 1;
            // New column starts
            PF_NewColumn[i] = True;
            PF_Close[i] = PFC_temp[j] * Box;
            PF_Open[i] = PFO_temp[j] * Box;
        }
    }

    if( RH[i] >= PFC_temp[j] + 1 && up )        //Continue up
    {
        PFC_temp[j] = RH[i];
        PFO_temp[j] = PFC_temp[j] - 1;
        // Update current bar values
        PF_Close[i] = PFC_temp[j] * Box;
        PF_Open[i] = PFO_temp[j] * Box;
    }
    else
    {
        if( FL[i] <= PFC_temp[j] - myReverse && up )  //Change direction to down
        {
            j++;
            PFC_temp[j] = FL[i];
            PFO_temp[j] = PFC_temp[j] + 1;
            swap = 1;
            // New column starts
            PF_NewColumn[i] = True;
            PF_Close[i] = PFC_temp[j] * Box;
            PF_Open[i] = PFO_temp[j] * Box;
        }
    }

    if( swap )
    {
        swap = 0;
        if( up )
        {
            up = 0;
            down = 1;
            PF_Direction[i] = -1;
        }
        else
        {
            up = 1;
            down = 0;
            PF_Direction[i] = 1;
        }
    }
}

// === TIME-ALIGNED P&F OVERLAY ELEMENTS ===

// --- Identify new up and down columns (now time-aligned)
NewUp   = PF_NewColumn AND PF_Direction == 1;
NewDown = PF_NewColumn AND PF_Direction == -1;

// --- Support/Resistance levels at reversals
SuppLevel = IIf(NewUp, PF_Close, Null);     // low of new up column
ResLevel  = IIf(NewDown, PF_Close, Null);   // high of new down column

// --- Fill forward the most recent levels
Support    = ValueWhen(NewUp,   SuppLevel, 1);
Resistance = ValueWhen(NewDown, ResLevel, 1);

// === OVERLAY PLOTS ===

// Plot P&F Support and Resistance levels
ShowSR = Param("Show Support/Resistance", 1, 0, 1, 1);
if( ShowSR )
{
    Plot( Support, "P&F Support", colorGreen, styleLine | styleThick | styleNoRescale );
    Plot( Resistance, "P&F Resistance", colorRed, styleLine | styleThick | styleNoRescale );
}

// Plot P&F column changes as vertical lines
ShowColumns = Param("Show P&F Columns", 1, 0, 1, 1);
if( ShowColumns )
{
    // Mark new P&F columns with vertical lines at exact time
    Plot( IIf(PF_NewColumn, H * 1.02, Null), "P&F Column", colorBlue, styleHistogram | styleOwnScale );
}

// === P&F PRICE LEVELS OVERLAY ===
ShowPFLevels = Param("Show P&F Price Levels", 1, 0, 1, 1);
if( ShowPFLevels )
{
    // Plot current P&F close and open levels as horizontal lines
    Plot( PF_Close, "P&F Close Level", IIf(PF_Direction > 0, colorBlue, colorRed),
          styleLine | styleThick | styleNoRescale );
    Plot( PF_Open, "P&F Open Level", IIf(PF_Direction > 0, colorLightBlue, colorPink),
          styleDots | styleNoRescale );
}

// === P&F SIGNALS ===
ShowSignals = Param("Show P&F Signals", 1, 0, 1, 1);

if( ShowSignals )
{
    // --- Signal conditions ---
    PrevRes  = Ref(Resistance, -1);
    PrevSupp = Ref(Support, -1);

    Buy   = NewUp   AND Support > PrevSupp;
    Short = NewDown AND Resistance < PrevRes;

    // Exits
    Sell  = L < Support OR Short;                 // sell if support broken or short triggered
    Cover = H > Resistance OR Buy;                // cover if resistance broken or buy triggered

    // --- Price assignment for markers ---
    BuyPrice   = ValueWhen(Buy,   C);
    ShortPrice = ValueWhen(Short, C);
    SellPrice  = ValueWhen(Sell,  Support);
    CoverPrice = ValueWhen(Cover, Resistance);

    // --- Clean signals ---
    Buy   = ExRem(Buy, Sell);
    Sell  = ExRem(Sell, Buy);
    Short = ExRem(Short, Cover);
    Cover = ExRem(Cover, Short);

    // --- Plot signals on candlestick chart ---
    PlotShapes( Buy   * shapeUpArrow,     colorLime, 0, L * 0.98,  -15 );
    PlotShapes( Short * shapeDownArrow,   colorRed,   0, H * 1.02, -15 );
    PlotShapes( Sell  * shapeDownTriangle,colorOrange, 0, H * 1.02, -10 );
    PlotShapes( Cover * shapeUpTriangle,  colorLightBlue, 0, L * 0.98, -10 );
}

// === TITLE AND DISPLAY ===
GraphXSpace = 2;
Title = Name() + " - Candlestick with P&F Overlay" +
        "\nBox: " + NumToStr(box, 1.2) +
        ", Reversal: " + myReverse +
        "\nP&F Close: " + WriteVal(PF_Close, 1.2) +
        ", P&F Open: " + WriteVal(PF_Open, 1.2) +
        "\nSupport: " + WriteVal(Support, 1.2) +
        ", Resistance: " + WriteVal(Resistance, 1.2) +
        "\n{{VALUES}}";

// === PARAMETER PANEL ===
_SECTION_BEGIN("Point & Figure Overlay Settings");
// Box size and reversal already defined above
_SECTION_END();

_SECTION_BEGIN("Display Options");
// ShowSR, ShowColumns, ShowSignals, ShowPFLevels already defined above
_SECTION_END();
