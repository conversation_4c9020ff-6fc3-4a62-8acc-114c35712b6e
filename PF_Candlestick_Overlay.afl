//AFL Point & Figure Overlay on Candlestick Chart
//Displays normal candlestick chart with P&F levels and signals overlaid
//Based on PFC v1.1.afl by <PERSON>
//Modified for proper time-aligned overlay display

// === CANDLESTICK CHART (Base Chart) ===
// Plot the normal candlestick chart first
SetBarsRequired( 10000 );
Plot( C, "Close", colorBlack, styleCandle );

// === POINT & FIGURE CALCULATION ===
//Size for P&F boxes
Box = boxsize = Optimize( "Box Size", 5, 1, 50, 0.5 );
myReverse = Optimize( "Reverse", 1, 1, 10, 1 ) * boxsize;

HX = round( ( H / box ) * 10 ) / 10;
LX = round( ( L / box ) * 10 ) / 10;
RH = floor( HX );
FL = ceil( LX );

// Arrays to track P&F state for each bar
PF_Close = Null;     // P&F close level for each time bar
PF_Open = Null;      // P&F open level for each time bar
PF_High = Null;      // P&F high level for each time bar
PF_Low = Null;       // P&F low level for each time bar
PF_Direction = Null; // 1 for up column, -1 for down column
PF_NewColumn = False; // True when a new P&F column starts
PF_PrevHigh = Null;  // Previous column's high (for resistance)
PF_PrevLow = Null;   // Previous column's low (for support)

// initialize first element
j = 0;

// Temporary arrays for P&F calculation
PFC_temp = Null;
PFO_temp = Null;

PFC_temp[j] = FL[0];
PFO_temp[j] = PFC_temp[j] + 1;
down = 1;
up = 0;
swap = 0;

// Set initial values for first bar
PF_Close[0] = PFC_temp[j] * Box;
PF_Open[0] = PFO_temp[j] * Box;
PF_High[0] = Max( PFC_temp[j], PFO_temp[j] ) * Box;
PF_Low[0] = Min( PFC_temp[j], PFO_temp[j] ) * Box;
PF_Direction[0] = -1; // Start with down
PF_NewColumn[0] = True;
PF_PrevHigh[0] = Null;
PF_PrevLow[0] = Null;

// perform the loop that produces time-aligned P&F data
for( i = 1; i < BarCount; i++ )
{
    // Copy previous values as default
    PF_Close[i] = PF_Close[i - 1];
    PF_Open[i] = PF_Open[i - 1];
    PF_High[i] = PF_High[i - 1];
    PF_Low[i] = PF_Low[i - 1];
    PF_Direction[i] = PF_Direction[i - 1];
    PF_NewColumn[i] = False;
    PF_PrevHigh[i] = PF_PrevHigh[i - 1];
    PF_PrevLow[i] = PF_PrevLow[i - 1];

    if( FL[i] <= PFC_temp[j] - 1 && down )      //continue down
    {
        PFC_temp[j] = FL[i];
        PFO_temp[j] = PFC_temp[j] + 1;
        // Update current bar values
        PF_Close[i] = PFC_temp[j] * Box;
        PF_Open[i] = PFO_temp[j] * Box;
        PF_High[i] = Max( PFC_temp[j], PFO_temp[j] ) * Box;
        PF_Low[i] = Min( PFC_temp[j], PFO_temp[j] ) * Box;
    }
    else
    {
        if( RH[i] >= PFC_temp[j] + myReverse && down ) //Change direction to up
        {
            // Store previous column's low before starting new column
            PF_PrevLow[i] = PF_Low[i - 1]; // This will be support level

            j++;
            swap = 1;
            PFC_temp[j] = RH[i];
            PFO_temp[j] = PFC_temp[j] - 1;
            // New column starts
            PF_NewColumn[i] = True;
            PF_Close[i] = PFC_temp[j] * Box;
            PF_Open[i] = PFO_temp[j] * Box;
            PF_High[i] = Max( PFC_temp[j], PFO_temp[j] ) * Box;
            PF_Low[i] = Min( PFC_temp[j], PFO_temp[j] ) * Box;
        }
    }

    if( RH[i] >= PFC_temp[j] + 1 && up )        //Continue up
    {
        PFC_temp[j] = RH[i];
        PFO_temp[j] = PFC_temp[j] - 1;
        // Update current bar values
        PF_Close[i] = PFC_temp[j] * Box;
        PF_Open[i] = PFO_temp[j] * Box;
        PF_High[i] = Max( PFC_temp[j], PFO_temp[j] ) * Box;
        PF_Low[i] = Min( PFC_temp[j], PFO_temp[j] ) * Box;
    }
    else
    {
        if( FL[i] <= PFC_temp[j] - myReverse && up )  //Change direction to down
        {
            // Store previous column's high before starting new column
            PF_PrevHigh[i] = PF_High[i - 1]; // This will be resistance level

            j++;
            PFC_temp[j] = FL[i];
            PFO_temp[j] = PFC_temp[j] + 1;
            swap = 1;
            // New column starts
            PF_NewColumn[i] = True;
            PF_Close[i] = PFC_temp[j] * Box;
            PF_Open[i] = PFO_temp[j] * Box;
            PF_High[i] = Max( PFC_temp[j], PFO_temp[j] ) * Box;
            PF_Low[i] = Min( PFC_temp[j], PFO_temp[j] ) * Box;
        }
    }

    if( swap )
    {
        swap = 0;

        if( up )
        {
            up = 0;
            down = 1;
            PF_Direction[i] = -1;
        }
        else
        {
            up = 1;
            down = 0;
            PF_Direction[i] = 1;
        }
    }
}

// === TIME-ALIGNED P&F OVERLAY ELEMENTS ===

// --- Identify new up and down columns (now time-aligned)
NewUp   = PF_NewColumn AND PF_Direction == 1;
NewDown = PF_NewColumn AND PF_Direction == -1;

// --- Support/Resistance levels at reversals
SuppLevel = IIf( NewUp, PF_PrevLow, Null );    // low of PREVIOUS column when new up starts (support)
ResLevel  = IIf( NewDown, PF_PrevHigh, Null ); // high of PREVIOUS column when new down starts (resistance)

// --- Fill forward the most recent levels
Support    = ValueWhen( NewUp,   SuppLevel, 1 ) - myReverse;
Resistance = ValueWhen( NewDown, ResLevel, 1 ) + myReverse;

// === OVERLAY PLOTS ===

// Plot P&F Support and Resistance levels
if( ParamToggle( "Show Support/Resistance", "No|Yes", 0 ) )
{
    Plot( Support, "P&F Support", IIf( PF_Direction > 0, colorPaleGreen, colorGreen ), styleLine | styleThick | styleNoRescale );
    Plot( Resistance, "P&F Resistance", IIf( PF_Direction < 0, colorPink, colorRed ), styleLine | styleThick | styleNoRescale );
}

// Plot P&F column changes as vertical lines
if( ParamToggle( "Show P&F Columns", "No|Yes", 0 ) )
{
    Plot( IIf( PF_NewColumn, H * 1.02, Null ), "P&F Column", colorBlue, styleHistogram | styleOwnScale );
}

// === P&F PRICE LEVELS OVERLAY ===
if( ParamToggle( "Show P&F Price Levels", "No|Yes", 0 ) )
{
    Plot( PF_High, "P&F Close Level", IIf( PF_Direction > 0, colorBlue, colorRed ), styleLine | styleThick | styleNoRescale );
    Plot( PF_Low, "P&F Open Level", IIf( PF_Direction > 0, colorLightBlue, colorPink ), styleDots | styleNoRescale );
}

// === P&F SIGNALS ===
// --- Previous P&F levels for comparison ---
PrevPF_High = Ref( PF_High, -1 );
PrevPF_Low = Ref( PF_Low, -1 );
PrevRes = Ref( Resistance, -1 );
PrevSupp = Ref( Support, -1 );

// --- Double Top/Bottom Patterns ---
// Look back to find previous up/down columns for comparison
PrevUpHigh = ValueWhen( Ref( NewUp, -1 ), Ref( PF_High, -1 ), 1 );
PrevDownLow = ValueWhen( Ref( NewDown, -1 ), Ref( PF_Low, -1 ), 1 );

// === BUY SIGNALS ===
// 1. Double Top Breakout - new up column exceeds previous up column high
DoubleTopBuy = NewUp AND PF_High > PrevUpHigh AND False;

// 2. Triple Top Breakout - breaking above established resistance from previous down columns
// Resistance is established by the high of down columns
LastDownResistance = ValueWhen( NewDown, PF_High, 1 );
TripleTopBuy = NewUp AND PF_High > LastDownResistance AND NOT IsNull(LastDownResistance);
myVar = ", PF_High = " + PF_High + ", LastDownResistance " + LastDownResistance;

// 3. Support Line Break - new up column with higher support
SupportBreakBuy = NewUp AND Support > PrevSupp AND False;

// Combined Buy Signal
Buy = DoubleTopBuy OR TripleTopBuy OR SupportBreakBuy;

// === SHORT SIGNALS ===
// 1. Double Bottom Breakdown - new down column falls below previous down column low
DoubleBottomShort = NewDown AND PF_Low < PrevDownLow;

// 2. Triple Bottom Breakdown - breaking below established support from previous up columns
// Support is established by the low of up columns
LastUpSupport = ValueWhen( NewUp, PF_Low, 1 );
TripleBottomShort = NewDown AND PF_Low < LastUpSupport AND NOT IsNull(LastUpSupport);

// 3. Resistance Line Break - new down column with lower resistance
ResistanceBreakShort = NewDown AND Resistance < PrevRes;

// Combined Short Signal
Short = DoubleBottomShort OR TripleBottomShort OR ResistanceBreakShort;
Short = Short AND False;

// === EXIT SIGNALS ===
// SELL (Exit Long) - Support break or reversal to down
SupportBreakSell = L < ( Support - Box ); // Price breaks support with buffer
ReversalSell = NewDown;  // New down column starts
Sell = SupportBreakSell OR ReversalSell OR Short;

// COVER (Exit Short) - Resistance break or reversal to up
ResistanceBreakCover = H > ( Resistance + Box ); // Price breaks resistance with buffer
ReversalCover = NewUp;  // New up column starts
Cover = ResistanceBreakCover OR ReversalCover OR Buy;

// --- Price assignment for markers ---
BuyPrice = ValueWhen( Buy, C );
ShortPrice = ValueWhen( Short, C );
SellPrice = ValueWhen( Sell, C );
CoverPrice = ValueWhen( Cover, C );

// --- Clean signals to prevent simultaneous signals ---
Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

// --- Plot signals on candlestick chart ---
if( ParamToggle( "Show P&F Signals", "No|Yes", 0 ) )
{
    PlotShapes( Buy * shapeUpArrow, colorLime, 0, L * 0.98, -15 );
    PlotShapes( Short * shapeDownArrow, colorRed, 0, H * 1.02, -15 );
    PlotShapes( Sell * shapeDownTriangle, colorOrange, 0, H * 1.02, -10 );
    PlotShapes( Cover * shapeUpTriangle, colorLightBlue, 0, L * 0.98, -10 );

    // --- Plot signal labels ---
    /*
    PlotText( "BUY\n" + WriteVal(BuyPrice, 1.2), BarIndex(), BuyPrice, colorLime );
    PlotText( "SHORT\n" + WriteVal(ShortPrice, 1.2), BarIndex(), ShortPrice, colorRed );
    PlotText( "SELL\n" + WriteVal(SellPrice, 1.2), BarIndex(), SellPrice, colorOrange );
    PlotText( "COVER\n" + WriteVal(CoverPrice, 1.2), BarIndex(), CoverPrice, colorLightBlue );
    */
}

SetPositionSize( RoundLotSize, spsShares );

// === TITLE AND DISPLAY ===
Title = Name() + " - Candlestick with P&F Overlay" +
        "\nBox: " + NumToStr( box, 1.2 ) +
        ", Reversal: " + myReverse +
        "\nP&F Close: " + WriteVal( PF_Close, 1.2 ) +
        ", P&F Open: " + WriteVal( PF_Open, 1.2 ) +
        "\nSupport: " + WriteVal( Support, 1.2 ) +
        ", Resistance: " + WriteVal( Resistance, 1.2 ) +
        "\n{{VALUES}}" + myVar;

