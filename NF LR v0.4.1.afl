//------------------------------------------------------
//
//  Formula Name:    Linear Regression
//  Author/Uploader: Trading Tuitions
//  E-mail:          <EMAIL>
//  Website:         www.tradingtuitions.com
//------------------------------------------------------
// Nifty - box size adjusment
//-----------------------------------------------------

#include <Kolier.afl>
#include <Common.afl>

Periods		= Param( "Periods", 65, 20, 120, 1 ); //35
eqAtRisk	= Param( "Risk", 25000, 20000, 45000, 5000 );

HighLowDiff = High - Low;
boxSize = round(0.5 * Percentile(HighLowDiff, Periods, 95));

StopLoss	= 1.0 / 100; // Optimize( "stop", 0.5, 0.5, 5, 0.5 ) / 100;
Target		= 3.5 / 100; //Optimize( "Target", 5.5, 4, 7, 0.5 ) / 100;

Vw 		= myround( VWMA2( C, Periods ) );
top 	= Max( LinearReg( High, Periods ), LinearReg( High, ceil( Periods / 2 ) ) );
bottom 	= Min( LinearReg( Low , Periods ), LinearReg( Low , ceil( Periods / 2 ) ) );
Lr 		= mybox( ( LinearReg( C, Periods ) + H + L ) / 3 );
Vw_1 	= Ref( Vw, -1 );
Lr_1	= Ref( Lr, -1 );

mode = Flip( Lr > Vw + boxSize, Lr < Vw - boxSize );
Sd = myBox( StDev( C, Periods ) );

SetChartOptions( 0, chartShowArrows | chartShowDates );
lastTradeOfTheDayTime	= 145000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

f = OptimizeNot( "F", 1.4, 1, 3, 0.1 ); // 10m - 4

up = C + f * SD;
down = C - f * SD;

//------------------------------------------------------
// psignal function
//------------------------------------------------------
function fPSignal( ser, integer )
{
    nStDev = StDev( ser, integer );
    nSma = MA( ser, integer );
    return IIf( nStDev > 0, Erf( nSma / nStDev / sqrt( 2 ) ), sign( nSma ) * 1.0 );
}

nPoints		= OptimizeNot( "Number of Bars", 48, 20, 100, 2 ); // 10m - 66
ohlc4		= ( O + H + L + C ) / 4;
nIntr		= nPoints - 1;
nPSignal	= MA( fPSignal( Ref( ohlc4, 0 ) - Ref( ohlc4, -1 ), nIntr ), nIntr );
ndPSignal	= sign( nPSignal - Ref( nPSignal, -1 ) );

pBuy	= nPSignal < 0 AND ndPSignal > 0;
pSell	= nPSignal > 0 AND ndPSignal < 0;

H_16	= myboxC( HHV( H, 16 ) );
L_16	= myboxF( LLV( L, 16 ) );
H_5_1	= Ref( H_16, -1 );
L_5_1	= Ref( L_16, -1 );

pp		= OptimizeNot( "L", 30, 10, 300, 10 );
LT_H	= myboxC( HHV( H, pp ) );
LT_L	= myboxF( LLV( L, pp ) );
LT_H_1	= Ref( LT_H, -1 );
LT_L_1	= Ref( LT_L, -1 );

Buyflag = Shortflag = 0;
BuyP 	= ShortP = 0;
Buy		= Short = Cover = Sell = 0;
exit 	= Null;
PT 		= Null;
PThit	= False;

buyExit = Max( Vw_1, down );
bRisk = ( C - buyExit );
shortExit = Vw_1;
sRisk = ( shortExit - C );
max_sl	= OptimizeNot( "MSL", 120, 50, 250, 10 ); // 10m - 110
curr_sl	= 0;

wick_size = IIf( ( H - L ) == 0, 0, ( H - Max( O, C ) ) / ( H - L ) );
myVar = ", wick_size = " + wick_size;
myVar += ", boxSize = " + boxSize;
LongWick = wick_size > 0.6;

BuyFilter 	= 	  Ref( mode, -1 ) AND L > Lr_1 AND C > H_5_1 AND TradingZone AND C > O AND fch > C1D AND NOT LongWick;
ShortFilter = NOT Ref( mode, -1 ) AND H < Lr_1 AND C < L_5_1 AND TradingZone AND C < O;
InBuy = InShort = 0;

for( i = 1; i < BarCount; i++ )
{
    if( Buyflag > 0 )
    {
		boxSize[i] = boxSize[i-1]; 
        InBuy[i] = 1;
        t_exit =  buyExit[i];

        if( H[i] >= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Max( down[i - 1], C[i - 1] * ( 1 - StopLoss ) );
        }

        if( O[i] > t_exit )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        if( Buyflag == 3 )
        {
            t_exit = C[i - 1] - max_sl[i];
            exit[i] = Max( t_exit, exit[i] );
        }

        PT[i]   = PT[i - 1];
        exit[i] = Max(exit[i - 1], floor( exit[i] / boxSize[i] ) * boxSize[i]);

    }

    if( Shortflag )
    {
        InShort[i] = 1;
        t_exit = up[i - 1];
		boxSize[i] = boxSize[i-1]; 

        if( L[i] <= PT[i - 1] OR PThit )
        {
            PThit = True;
            t_exit = Min( up[i - 1], C[i - 1] * ( 1 + StopLoss ) );
        }

        if( O[i] < t_exit )
        {
            exit[i] = Min( t_exit, exit[i - 1] );
        }
        else
        {
            exit[i] = exit[i - 1];
        }

        if( Shortflag == 3 )
        {
            t_exit = C[i - 1] + max_sl[i];
            exit[i] = Min( t_exit, exit[i] );
        }

        PT[i]   = PT[i - 1];
        exit[i] = Min(exit[i - 1], ceil( exit[i] / boxSize[i] ) * boxSize[i]);
    }


    if( Buyflag AND L[i] < exit[i] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = IIf( O[i] > exit[i], exit[i], C[i] );
    }

    if( Buyflag AND NOT mode[i - 1] )
    {
        Sell[i] = 1;
        Buyflag = 0;
        SellPrice[i] = C[i];
    }

    if( Shortflag AND H[i] > exit[i] )
    {
        Cover[i]	= 1;
        Shortflag	= 0;
        CoverPrice[i] = IIf( O[i] < exit[i], exit[i], C[i] );
    }

    if( Shortflag AND mode[i - 1] )
    {
        Cover[i] 	= 1;
        Shortflag	= 0;
        CoverPrice[i] = C[i];
    }

    if( NOT Buyflag AND BuyFilter[i] AND bRisk[i] < max_sl[i] AND pBuy[i] )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= buyExit[i];
        Buyflag 	= 3;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        curr_sl		= bRisk[i];
        PT[i]		= C[i] * ( 1 + Target );
        PThit		= False;
    }

    if( NOT Buyflag AND BuyFilter[i] AND pBuy[i] )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        buyExit[i]	= C[i] - max_sl[i];
        exit[i]		= buyExit[i];
        Buyflag 	= 1;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i]	= C[i] - buyExit[i];
        curr_sl		= bRisk[i];
        PT[i]		= C[i] * ( 1 + Target );
        PThit		= False;
    }

    if( NOT Buyflag AND BuyFilter[i] AND NOT firstBarOfTheDay[i] AND C[i] > fch[i] )
    {
        BuyP 		= C[i];
        BuyPrice[i] = BuyP;
        exit[i]		= IIf( H[i] < LT_H_1[i], buyExit[i], Max( buyExit[i], L[i] - 2 * boxSize[i] ) );
        Buyflag 	= 1;
        Shortflag  	= 0;
        Buy[i] 		= 1;
        bRisk[i]	= ( C[i] - buyExit[i] );
        curr_sl		= bRisk[i];
        PT[i]		= C[i] * ( 1 + Target );
        PThit		= False;
    }

    if( NOT Shortflag AND ShortFilter[i] )
    {
        ShortP			= C[i];
        ShortPrice[i]	= ShortP;
        exit[i]			= shortExit[i];
        Buyflag   		= 0;
        Shortflag 		= 1;
        Short[i]  		= 1;
        sRisk[i]		= exit[i] - C[i];
        curr_sl			= sRisk[i];
        PT[i]			= C[i] * ( 1 - Target );
        PThit			= False;
    }
}

exit = myRound( exit );
PT	 = myRound( PT );
bLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( bRisk, bRisk, 1 ) ) ) );
sLots	= Max( 0, floor( eqAtRisk / ( RoundLotSize * IIf( sRisk, sRisk, 1 ) ) ) );

// max_lots = Param( "lots", 12, 4, 20, 2 );
max_lots = floor( 7500000 / ( RoundLotSize * C ) );

bLots	= Min( max_lots, bLots );
sLots	= Min( max_lots, sLots );

bRisk = IIf( exit < C, C - exit, brisk );
sRisk = IIf( exit > C, exit - C, sRisk );

SetPositionSize( IIf( Buy, bLots, sLots )*RoundLotSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*bRisk ) + ", " + round( RoundLotSize*sRisk );

bkcolor = LastValue( IIf( mode, ColorBlend( colorCustom9, colorWhite ), ColorBlend( colorCustom12, colorWhite ) ) );
Plot( IIf( exit, exit, C ), "exit", IIf( exit, colorBrown, bkcolor ), styleStaircase | styleDashed, Null, Null, Null, -1 );
Plot( Close, "", colorDarkGrey, styleNoTitle | GetPriceStyle(), Null, Null, Null, 1 );
Plot( Vw , "", IIf( mode, colorGreen, colorPink ), ParamStyle( "Style" ) | styleThick | styleNoLabel );
Plot( Lr_1, "", colorGrey40, ParamStyle( "Style" ) | styleThick, Null, Null, Null, 2 );

vlots = V / RoundLotSize;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -25 );

// draw levels from Reaction Trend System
if( ParamToggle( "Show RT", "0|1" ) )
{
    Plot( S1, "R1", colorRed, styleStaircase | styleNoLabel );
    Plot( B1, "S1", colorBrightGreen, styleStaircase | styleNoLabel );
}

#include <Alert.afl>

_SECTION_END();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "Open %0.2f, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 )
                       ) );

//------------------------------------------------------
// AlgoFoxAuto Section
//------------------------------------------------------
INSTR  = "FUTIDX";
SYS  = "FUT";
// #include<AlgoFoxAuto/AlgoFoxAuto.afl>
