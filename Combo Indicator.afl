// Downloaded From https://www.WiseStockTrader.com
Title = "Combo";
GfxSetBkMode( 1 );
GraphXSpace = 8;
RequestTimedRefresh( 1, False );

_SECTION_BEGIN( "Price" );
// Price
Chart = ParamList( "Chart Style", "Candles|BarsColor|BarsBlack", 0 );

if( Chart == "Candles" )
{
    BarColor = IIf( C == O, colorBlueGrey, IIf( C > O, colorLime, colorRed ) );
    SetBarFillColor( BarColor );
    Plot( Close, "", colorGrey50, styleCandle | styleNoLabel );
}
else
    if( Chart == "BarsColor" )
    {
        BarColor = IIf( C == O, colorBlueGrey, IIf( C > O, colorGreen, colorRed ) );
        Plot( Close, "", BarColor, styleBar | styleNoLabel, Null, Null, 0, 0, 2 );
    }

if( Chart == "BarsBlack" )
{
    BarColor = IIf( C > O, colorBlack, colorBlack );
    Plot( Close, "", BarColor, styleBar | styleNoLabel, Null, Null, 0, 0, 2 );
}

Plot( LastValue( Close ), "Close", LastValue( BarColor ), styleLine, Null, Null, 10 );

// Accumulation/Distribution
ShowAD = ParamToggle( "Accumulation/Distribution", "Show|Hide", 1 );
ACDPer   = Param( "Periods", 6, 1, 72, 1 );
ACD      = EMA( AccDist(), ACDPer );

if( ShowAD == 0 )
{
    Plot( ACD, _DEFAULT_NAME(), colorGold, styleThick | styleOwnscale );
}

//Time to Go
function GetSecondNum()
{
    Time = Now( 4 );
    Seconds = int( Time % 100 );
    Minutes = int( Time / 100 % 100 );
    Hours   = int( Time / 10000 % 100 );
    SecondNum = int( Hours * 60 * 60 + Minutes * 60 + Seconds );
    return SecondNum;
}

TimeFrame = Interval();
SecNumber = GetSecondNum();
Newperiod = SecNumber % TimeFrame == 0;
SecsLeft  = SecNumber - int( SecNumber / TimeFrame ) * TimeFrame;
SecsToGo  = TimeFrame - SecsLeft;

GfxSetTextColor( colorBlack );
GfxSelectPen( colorBlack, 1 );
GfxSelectSolidBrush( colorLightYellow );
GfxRoundRect( 12, 24, 230, 50, 6, 6 );
GfxDrawText( "Time to Go   " + SecsToGo + "   sec", 12, 24, 230, 50, 1 | 4 | 16 | 32 );

// Daily HI LO
DH = TimeFrameGetPrice( "H", inDaily );
DL = TimeFrameGetPrice( "L", inDaily );
HL = DH - DL;
CloudColor = colorDarkGrey;

if( ParamToggle( "Daily High Low Levels", "Hide|Show", 0 ) )
{
    Plot( DH, "", colorOrange, styleStaircase | styleNoRescale, Null, Null, 0, 0, width = -60 );
    Plot( DL, "", colorGreen,  styleStaircase | styleNoRescale, Null, Null, 0, 0, width = -60 );
    PlotOHLC( DL, DH, DL, DH, "", colorLavender,  styleCloud | styleNoRescale, Null, Null, 0, -2 );
}

GfxSetTextColor( colorBlack );
GfxSelectPen( colorBlack, 1 );
GfxSelectSolidBrush( colorLightYellow );
GfxRoundRect( 12, 54, 230, 80, 6, 6 );
GfxDrawText( "Daily Levels = " + WriteVal( HL, 1 ), 12, 54, 230, 80, 1 | 4 | 16 | 32 );

// Moving averages
ShowMA = ParamToggle( "Moving averages", "Show|Hide", 1 );
Fast = Param( "Period Fast",  9,  5,  50, 1 );
Slow = Param( "Period Slow", 27, 13, 300, 1 );

if( ShowMA == 0 )
{
    Plot( EMA( C, Fast ), "", colorBlue,   styleLine | styleNoRescale );
    Plot( EMA( C, Slow ), "", colorBrown,  styleLine | styleNoRescale );
}

// LWMA Linear Welghted MA
function LWMA( P, per )
{
    local s, pa, i;
    s = 0;
    pa = 0;

    for( i = 0; i < per; i++ )
    {
        s  += Ref( P, -i ) * ( per - i );
        pa += per - i;
    }

    return ( s / pa );
}

ShowLWMA = ParamToggle( "LWMA", "Hide|Show", 1 );
LWMAPer  = Param( "LWMA Period", 30, 8, 144, 2 );

if( ShowLWMA == 1 )
{
    Plot( LWMA( C, LWMAPer ), "", colorLightBlue, styleNoRescale, Null, Null, 0, 0, width = 3 );
}

_SECTION_END();


_SECTION_BEGIN( "SUP_RES" );
SRShow   = ParamToggle( "Sup_Res Levels", "Hide|Show", 1 );
ColorType = ParamToggle( "Color Scheme", "Dark|Light", 0 );
SRBack   = Param( "Levels Num", 5, 1, 20, 1 );
SRPer    = Param( "Accuracy", 0.5, 0.1, 5, 0.1 );
SupColor = colorTan;
ResColor = colorLightBlue;

if( ColorType == 0 )
{
    SupColor = colorTan;
    ResColor = colorLightBlue;
}
else
{
    SupColor = colorPink;
    ResColor = colorPaleGreen;
}


function GetXSupport( Lo, Percentage, Back )
{
    return ( ( BarCount - 1 ) - LastValue( TroughBars( Lo, Percentage, Back ) ) );
}
function GetYSupport( Lo, Percentage, Back )
{
    return  round( ( LastValue( Trough( Lo, Percentage, back ) ) ) / 10 ) * 10 ;
}

function GetXResistance( Hi, Percentage, Back )
{
    return ( ( BarCount - 1 ) - LastValue( PeakBars( Hi, Percentage, Back ) ) );
}

function GetYResistance( Hi, Percentage, Back )
{
    return  round( ( LastValue( Peak( Hi, Percentage, Back ) ) ) / 10 ) * 10;
}

if( SRShow )
{
    for( i = 1; i <= SRBack; i++ )
    {
        x0 = GetXSupport( L, SRPer, i );
        x1 = BarCount - 1;
        y0 = GetYSupport( L, SRPer, i );
        x = LineArray( x0, y0, x1, y0, 0 );
        Plot( x, "", ResColor, styleNoRescale, Null, Null, 0, 0, width = -60 );

        x0 = GetXResistance( H, SRPer, i );
        y0 = GetYResistance( H, SRPer, i );
        x = LineArray( x0, y0, x1, y0, 0 );
        Plot( x, "", SupColor, styleNoRescale, Null, Null, 0, 0, width = -60 );
    }
}
else { }

_SECTION_END();


_SECTION_BEGIN( "VAP" );
ShowVAP  = ParamToggle( "Volume at Price", "Show|Hide", 1 );
Density  = Param( "Lines Num", 200, 100, 1000, 10 );
Width    = Param( "Width", 30, 2, 100, 2 );
Side     = ParamToggle( "Side", "Left|Right", 0 );
VAPColor = ParamColor( "VAP Color", colorDarkGrey );

if( ShowVAP == 0 )
{
    PlotVAPOverlay( Density, Width, VAPColor, Side | 2 * 1 );
}

_SECTION_END();


_SECTION_BEGIN( "BOLL" );
Periods = Param( "BB Periods", 16,    2, 32, 1 );
Width   = Param( "BB Width",   1.35, 0, 6,  0.05 );
ShowBB = ParamToggle( "Bollinger Bands", "Show|Hide", 1 );
bbt = BBandTop( Close, Periods, Width );
bbb = BBandBot( Close, Periods, Width );

if( ShowBB == 0 )
{
    Plot( bbt, "BBTop" + _PARAM_VALUES(), colordarkGrey, styleNoRescale );
    Plot( bbb, "BBBot" + _PARAM_VALUES(), colordarkGrey, styleNoRescale );
    PlotOHLC( bbt, bbt, bbb, bbb, "",     colorLavender, styleCloud | styleNoLabel | styleNoRescale, Null, Null, Null, -1 );
}

_SECTION_END();


_SECTION_BEGIN( "Ticker Name" );
GfxSelectFont( "Verdana", 64, 600 );
GfxTextOut( Name(), 280, 0 );
_SECTION_END();