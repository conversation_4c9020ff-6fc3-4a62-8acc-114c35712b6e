// Expiry Day
_SECTION_BEGIN( "Total OI" );
#include <Options.afl>
// #include <AlgoFoxAuto/AlgoFoxButton.afl>

SetChartBkColor( colorWhite ); // color of outer border
SetOption( "DisableRuinStop", True );

tn = TimeNum();
TradingZone = ( tn >= 100400 AND tn <= 151000 );

equityAtRisk = 3000;

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = Optimize( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn_flip = Flip( oi_Below, oi_Above );
oi_up_flip = Flip( oi_Above, oi_Below );
ROC_price = ROC( C, 4 );

price_stable_or_increasing_3_flip= C >= Ref(C, -1) AND Ref(Ref(C, -1) > O, -1);
price_stable_or_increasing_4_flip= C >= Ref(C, -2) AND price_stable_or_increasing_3_flip AND Ref(Ref(C, -1) > O, -2);
price_stable_or_increasing_5_flip= C >= Ref(C, -3) AND price_stable_or_increasing_4_flip AND Ref(Ref(C, -1) > O, -3);
price_stable_or_increasing_6_flip= C >= Ref(C, -4) AND price_stable_or_increasing_5_flip AND Ref(Ref(C, -1) > O, -4);

price_not_stable_or_increasing_3_flip= NOT price_stable_or_increasing_3_flip;
price_not_stable_or_increasing_4_flip= NOT price_stable_or_increasing_4_flip;
price_not_stable_or_increasing_5_flip= NOT price_stable_or_increasing_5_flip;
price_not_stable_or_increasing_6_flip= NOT price_stable_or_increasing_6_flip;

total_price_not_stable_flip= price_not_stable_or_increasing_3_flip+ price_not_stable_or_increasing_4_flip+ price_not_stable_or_increasing_5_flip+ price_not_stable_or_increasing_6_flip;
total_price_stable_flip= price_stable_or_increasing_3_flip+ price_stable_or_increasing_4_flip+ price_stable_or_increasing_5_flip+ price_stable_or_increasing_6_flip;
wick_size = round(IIf( ( H - L ) == 0, 0, ( H - Max( O, C ) ) / ( H - L ) ) * 10)/ 10;
HV = HHV( Ref( V, -1 ), 15 );
DN                      = DateNum();
firstBarOfTheDay        = DN != Ref( DN, -1 );
RLS = 25;
DayVolMul = round( HighestSince( firstBarOfTheDay, HV ) / HV );
Liquid = V / RLS > 1000;
vol_hi = V > HV AND DayVolMul < 8 AND Liquid;

new_condition_flip = (
                         ( oi_up_flip ) AND( total_price_stable_flip >= 2 )
                         OR
                         ( total_price_stable_flip == 4 AND oi_dn_flip )
                     ) AND ROC_price > 9;
reverse_condition_flip = oi_dn_flip AND( total_price_not_stable_flip >= 2 );

OtherSymbol = GetPutName(Name());
if(StrFind(GetPutName(Name()), Name())) {
	OtherSymbol = GetCallName(Name());
}

SetForeign(OtherSymbol);
myVar = "";
DN                      = DateNum();
firstBarOfTheDay        = DN != Ref( DN, -1 );
boxSize = 6;
function myboxC( number )
{
    return ceil( number / boxsize ) * boxsize;
}
function myboxF( number )
{
    return floor( number / boxsize ) * boxsize;
}
fcl  = ValueWhen( firstBarOfTheDay, L );
fch  = ValueWhen( firstBarOfTheDay, H );
WhiteBody	= C > O;
RLS  = 25;
BigWhite	= ( Close - Open ) / Open > 0.015 AND( Close - Open ) * 2 > High - Low;
BlackBody	= C < O;
BigBlack	= ( Open - Close ) / Open > 0.015 AND( Open - Close ) * 2 > High - Low;
Big			= abs( H - L ) > boxSize;
UpTrend		= ( H > Ref( H, -1 ) AND L > Ref( L, -1 ) );
ThreeBlackCrows		= ( big AND blackbody ) AND Ref( big AND blackbody, -1 ) AND Ref( big AND blackbody, -2 ) AND O < Ref( O, -1 ) AND Ref( O, -1 ) < Ref( O, -2 );
ThreeWhiteSoldiers	= ( whitebody AND big ) AND Ref( whitebody AND big, -1 ) AND Ref( whitebody AND big, -2 ) AND O > Ref( O, -1 ) AND Ref( O, -1 ) > Ref( O, -2 );
// ThreeWhiteSoldiers	= whitebody AND Ref( whitebody, -1 ) AND Ref( whitebody, -2 ) AND( big OR Ref( big, -1 )OR Ref( big, -2 ) );

period = 15;
top 	= Max( LinearReg( High, period ), LinearReg( High, ceil( period / 2 ) ) );
bottom 	= Min( LinearReg( Low , period ), LinearReg( Low , ceil( period / 2 ) ) );
stMA 	= ( top + bottom ) / 2;

V1 = Ref( V, -1 );
MAV = MA( V1, 15 );
HV = HHV( Ref( V, -1 ), 15 );
C1 = Ref( C, -1 );
H1 = Ref( H, -1 );
L1 = Ref( L, -1 );
O1 = Ref( O, -1 );

// DayVolMul = round( TimeFrameGetPrice( "V", inDaily, 0, expandLast ) / HV );
DayVolMul = round( HighestSince( firstBarOfTheDay, HV ) / HV );

myVar += ", Day_V " + DayVolMul;
Liquid = V / 25 > 1000;

pp = Optimize( "pp", 90, 10, 200, 10 );
vol_up = V > MAV AND DayVolMul < 5 AND Liquid;
vol_up_1 = Ref( vol_up, -1 );

HHV10 = HHV( H1, 30 );
LLV10 = LLV( L1, 30 );

ACDUp 	= myboxC( fcl + Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );
ACDDown	= myboxF( fch - Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );

CP_IH	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( H - C ) / ( .001 + H - L ) > 0.6 ) AND( ( H - O ) / ( .001 + H - L ) > 0.6 ) );
CP_H	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( C - L ) / ( .001 + H - L ) > 0.6 ) AND( ( O - L ) / ( .001 + H - L ) > 0.6 ) );
CP_EH	= ( ( C1 > O1 ) AND( O > C ) AND( O <= C1 ) AND( O1 <= C ) AND( ( O - C ) < ( C1 - O1 ) ) );
CP_UH 	= ( ( O1 > C1 ) AND( C > O ) AND( C <= O1 ) AND( C1 <= O ) AND( ( C - O ) < ( O1 - C1 ) ) );
CP_LC	= ( abs( C - O ) / ( .001 + H - L ) > .6 );
CP_LC1	= Ref( CP_LC, -1 );

price_up = H > HHV10;
price_dn = L < LLV10;
SPD12	= Sum( price_dn, 8 );
SPU12	= Sum( price_up, 8 );

BuyExit = myboxF( LLV10 - boxSize );
BE1 = Ref( BuyExit, -1 );
ShortExit = myboxC( HHV10 + boxSize );
SE1 = Ref( ShortExit, -1 );
ROC_price = ROC( C, 3 );
ROC1 = ROC( C, 1 );

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = Optimize( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn = Flip( oi_Below, oi_Above );
oi_up = Flip( oi_Above, oi_Below );
HHV10 = HHV( H1, 30 );
LLV10 = LLV( L1, 30 );

total_price_stable = 0;

price_stable_or_increasing_3 = C >= Ref(C, -1) AND Ref(WhiteBody, -1);
price_stable_or_increasing_4 = C >= Ref(C, -2) AND price_stable_or_increasing_3 AND Ref(WhiteBody, -2);
price_stable_or_increasing_5 = C >= Ref(C, -3) AND price_stable_or_increasing_4 AND Ref(WhiteBody, -3);
price_stable_or_increasing_6 = C >= Ref(C, -4) AND price_stable_or_increasing_5 AND Ref(WhiteBody, -4);

total_price_stable = price_stable_or_increasing_3 + price_stable_or_increasing_4 + price_stable_or_increasing_5 + price_stable_or_increasing_6;

price_not_stable_or_increasing_3 = NOT price_stable_or_increasing_3;
price_not_stable_or_increasing_4 = NOT price_stable_or_increasing_4;
price_not_stable_or_increasing_5 = NOT price_stable_or_increasing_5;
price_not_stable_or_increasing_6 = NOT price_stable_or_increasing_6;

// Combine the conditions to sum the occurrences over the periods
total_price_not_stable = price_not_stable_or_increasing_3 + price_not_stable_or_increasing_4 + price_not_stable_or_increasing_5 + price_not_stable_or_increasing_6;

new_condition = (
                    oi_up AND( total_price_stable >= 2 )
                    OR
                    ( total_price_stable == 4 AND oi_dn )
                ) AND ROC_price > 9;

reverse_condition1 = ( oi_dn AND( total_price_not_stable >= 2 ) AND ROC_price < -6 );
reverse_condition2  = ( ( wick_size >= 0.5 AND Big) OR CP_IH ) AND vol_hi ;
reverse_condition = reverse_condition1 OR reverse_condition2;

myVar += ", new_condition_flip = " + new_condition_flip;

ShortCondition	= 0;
BuyCondition	= new_condition AND vol_up AND TradingZone AND WhiteBody AND NOT price_up AND NOT new_condition_flip;
myVar += ", BuyCondition = " + vol_up;

BuyP = ShortP = 0;
InShort = InBuy = 0;
ShortI = BuyI = BuyL = ShortL = 0;
Short = Cover = 0 ;
Buy = Sell = 0;

BuyFlag = ShortFlag = False;
BF = SF = 0;
SPT = LPT = 0;
HatBC = LatSC = LatBC = 0;
SellPrice = CoverPrice = C;
BTC = STC = 0;
SOI = BOI = 0;

for( i = 10; i < BarCount; i++ )
{
     if( InBuy AND O[i] > Max( BuyExit[i], BuyExit[i - 1] ) )
    {

        if( C[i] > LPT AND O[i] > Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] ) )
        {
            BuyExit[i] = Max( C[i] - 2 * floor( equityAtRisk / RLS ), BuyExit[i] );
        }

        BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
    }

    if( InBuy AND OI[i] < BOI )
    {
        BTC = True;
    }
    BOI = Max(BOI, OI[i]);

    if( L[i] < BuyExit[i] AND InBuy )
    {
        Sell[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
        BuyI = i;
        SellPrice[i] = BuyExit[i];
    }

    if( ( !TradingZone[i]
            OR( V[i] > HV[i] AND H[i] > BuyP AND BlackBody[i] )
            OR( V[i - 1] > HV[i - 1] AND price_up[i - 1] AND vol_up[i - 1] AND C[i] < C1[i] )
        ) AND InBuy )
    {
        Sell[i] = 1;
        InShort = 0;
        InBuy = 0;
        BuyFlag = False;
        ShortFlag = False;
        BuyI = i;
    }

    if( BuyFlag )
        HatBC[i] = Min( HatBC[i - 1], H[i] );

    if( BuyCondition[i] AND NOT InBuy AND NOT InShort )
    {
        if( NOT BuyFlag )
        {
            HatBC[i] = H[i];
            LatBC = floor( ( L[i] - boxSize[i] ) / boxSize[i] ) * boxSize[i];
        }

        ShortFlag = False;
        BuyFlag = True;
        BuyI = i;
        BuyL = Min( L[i - 1], L[i] );
    }

    if( NOT TradingZone[i] OR i > BuyI + 12 OR L[i] < LatBC )
    {
        BuyFlag = False;
    }

    if( BuyFlag AND NOT CP_IH[i] )
    {
        Buy[i] = 1;
        BuyP = C[i];
        BuyI = i;
        BuyExit[i] = Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] );
        InBuy = 1;
        InShort = 0;
        BuyFlag = False;
        ShortFlag = False;
        LPT = C[i] + 0.7 * floor( equityAtRisk / RLS );
        BTC = False;
        BOI = OI[i];
    }
    BF[i] = BuyFlag;
}

myVar += ", HatBC " + HatBC + ", LatSC = " + LatSC;
myVar += ", BF = " + BF + ", BC = " + BuyCondition;
myVar += ", SF = " + SF + ", SC = " + ShortCondition;

Sell = Sell OR NOT TradingZone OR Short;
Cover = Cover OR NOT TradingZone OR C < 5 OR Buy;
// Buy = Buy AND isITM AND ITMRange == 2 AND daysToExpiry < 7;
// Short = Short AND isITM AND ITMRange == 2 AND daysToExpiry < 7;
Buy = Buy AND daysToExpiry < 7;
// Short = Short AND isITM AND daysToExpiry < 7;
InBuy = InShort = 0;
BuyPrice = ShortPrice = C;

SetChartBkColor( LastValue(IIf(isITM AND ITMRange == 2, colorLightGrey, colorWhite)));

Plot( C, "", ParamColor( "Color", colorBlack ), styleCandle );
Plot( ShortExit, "SE", IIf( SF, colorRed, colorGrey40 ), styleDashed );
Plot( BuyExit, "BE", IIf( BF, colorLime, colorGrey40 ), styleDashed );
PlotShapes( new_condition*shapeSmallCircle, colorGreen, 0, L - 1 );
PlotShapes( reverse_condition*shapeSmallCircle, colorRed, 0, H + 10 );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

bRisk = BuyPrice - BuyExit;
bLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) ) ) );

sRisk = ShortExit - ShortPrice;
sLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) ) ) );
myvar 	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", BuyExit, ShortExit );
myvar 	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
#include <Alert.afl>

_N( Title = StrFormat( EncodeColor( colorDarkGrey )
                       + "%s, "
                       + "H %0.2f, "
                       + "L %0.2f, "
                       + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                       + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                       + EncodeColor( colorDarkGrey ) + myVar
                       , OtherSymbol, H, L, C, ROC( C, 1 )
                     ) );

_SECTION_END();
RestorePriceArrays();
