// Expiry Day
_SECTION_BEGIN( "Total OI" );
#include <Common.afl>
#include <Options.afl>
// #include <AlgoFoxAuto/AlgoFoxButton.afl>

SetChartBkColor( colorWhite ); // color of outer border
SetOption( "DisableRuinStop", True );

tn = TimeNum();
TradingZone = ( tn >= 091600 AND tn <= 151000 );

equityAtRisk = 3000;

MAV = MA( V1, 15 );
HV = HHV( Ref( V, -1 ), 15 );

// DayVolMul = round( TimeFrameGetPrice( "V", inDaily, 0, expandLast ) / HV );
DayVolMul = round( HighestSince( firstBarOfTheDay, HV ) / HV );

myVar += ", Day_V " + DayVolMul;
Liquid = V / RLS > 1000;

vol_up = V > MAV AND DayVolMul < 8 AND Liquid;
vol_up_1 = Ref( vol_up, -1 );

vol_hi = V > HV AND DayVolMul < 8 AND Liquid;
vol_hi_1 = Ref( vol_hi, -1 );

OtherSymbol = GetPutName( Name() );

if( StrFind( GetPutName( Name() ), Name() ) )
{
    OtherSymbol = GetCallName( Name() );
}

SetForeign( OtherSymbol );

price_increasing_3_flip = C > Ref( C, -1 ) AND Ref( Ref( C, -1 ) > O, -1 );
price_increasing_4_flip = C > Ref( C, -2 ) AND price_increasing_3_flip AND Ref( Ref( C, -1 ) > O, -2 );
price_increasing_5_flip = C > Ref( C, -3 ) AND price_increasing_4_flip AND Ref( Ref( C, -1 ) > O, -3 );
price_increasing_6_flip = C > Ref( C, -4 ) AND price_increasing_5_flip AND Ref( Ref( C, -1 ) > O, -4 );

price_decreasing_3_flip = C < Ref( C, -1 ) AND Ref( Ref( C, -1 ) < O, -1 );
price_decreasing_4_flip = C < Ref( C, -2 ) AND price_decreasing_3_flip AND Ref( Ref( C, -1 ) < O, -2 );
price_decreasing_5_flip = C < Ref( C, -3 ) AND price_decreasing_4_flip AND Ref( Ref( C, -1 ) < O, -3 );
price_decreasing_6_flip = C < Ref( C, -4 ) AND price_decreasing_5_flip AND Ref( Ref( C, -1 ) < O, -4 );

total_price_decreasing_flip = price_decreasing_3_flip + price_decreasing_4_flip + price_decreasing_5_flip + price_decreasing_6_flip;
total_price_increasing_flip = price_increasing_3_flip + price_increasing_4_flip + price_increasing_5_flip + price_increasing_6_flip;

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = OptimizeNot( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn_flip = Flip( oi_Below, oi_Above );
oi_up_flip = Flip( oi_Above, oi_Below );
ROC_price = ROC( C, 4 );

new_condition_flip = (
                         ( oi_up_flip ) AND( total_price_increasing_flip >= 2 )
                         OR
                         ( total_price_increasing_flip == 4 AND oi_dn_flip )
                     ) AND ROC_price > 9;
reverse_condition_flip = oi_dn_flip AND( total_price_decreasing_flip >= 2 );
total_reverse_condition_flip = reverse_condition_flip + Ref( reverse_condition_flip, -1 ) + Ref( reverse_condition_flip, -2 ) + Ref( reverse_condition_flip, -3 );
RestorePriceArrays();

OI1 = Ref( OI, -1 );
MAI3 = MA( OI, 3 );
MAI6 = MA( OI1, 6 );
m_fill = OptimizeNot( "fill", 5, 2, 20, 2 ) / 100;
oi_PL = Ref( MAI3, -1 ) * ( 1 - m_fill );
oi_PH = Ref( MAI3, -1 ) * ( 1 + m_fill );
oi_AH = LLV( oi_PH, 1 + Nz( BarsSince( MAI3 < Ref( MAI3, -1 ) ), 1 ) );
oi_AL = HHV( oi_PL, 1 + Nz( BarsSince( MAI3 > Ref( MAI3, -1 ) ), 1 ) );
oi_Above = MAI3 > oi_AH;
oi_Below = MAI3 < oi_AL;
oi_dn = Flip( oi_Below, oi_Above );
oi_up = Flip( oi_Above, oi_Below );
HHV10 = HHV( H1, 30 );
LLV10 = LLV( L1, 30 );

ACDUp 	= myboxC( fcl + Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );
ACDDown	= myboxF( fch - Max( 4 * boxSize, 0.5 * ( fch - fcl ) ) );

CP_IH	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( H - C ) / ( .001 + H - L ) > 0.6 ) AND( ( H - O ) / ( .001 + H - L ) > 0.6 ) );
CP_H	= ( ( ( H - L ) > 3 * ( O - C ) ) AND( ( C - L ) / ( .001 + H - L ) > 0.6 ) AND( ( O - L ) / ( .001 + H - L ) > 0.6 ) );
CP_EH	= ( ( C1 > O1 ) AND( O > C ) AND( O <= C1 ) AND( O1 <= C ) AND( ( O - C ) < ( C1 - O1 ) ) );
CP_UH 	= ( ( O1 > C1 ) AND( C > O ) AND( C <= O1 ) AND( C1 <= O ) AND( ( C - O ) < ( O1 - C1 ) ) );
CP_LC	= ( abs( C - O ) / ( .001 + H - L ) > .6 );
CP_LC1	= Ref( CP_LC, -1 );

price_up = H > HHV10;
price_dn = L < LLV10;
SPD12	= Sum( price_dn, 8 );
SPU12	= Sum( price_up, 8 );

BuyExit = myboxF( LLV10 - 1.5 * boxSize );
BE1 = Ref( BuyExit, -1 );
ShortExit = myboxC( HHV10 + boxSize );
SE1 = Ref( ShortExit, -1 );
ROC_price = ROC( C, 4 );
ROC1 = ROC( C, 1 );

total_oi_increasing = 0;
total_price_increasing = 0;

pp = Optimize( "pp", 9, 10, 30, 2 );
price_increasing_3 = C > Ref( C, -1 ) AND Ref( WhiteBody, -1 );
price_increasing_4 = C > Ref( C, -2 ) AND price_increasing_3 AND Ref( WhiteBody, -2 );
price_increasing_5 = C > Ref( C, -3 ) AND price_increasing_4 AND Ref( WhiteBody, -3 );
price_increasing_6 = C > Ref( C, -4 ) AND price_increasing_5 AND Ref( WhiteBody, -4 );
total_price_increasing = price_increasing_3 + price_increasing_4 + price_increasing_5 + price_increasing_6;

price_decreasing_3 = C < Ref( C, -1 ) AND Ref( BlackBody, -1 );;
price_decreasing_4 = C < Ref( C, -2 ) AND price_decreasing_3 AND Ref( BlackBody, -2 );
price_decreasing_5 = C < Ref( C, -3 ) AND price_decreasing_4 AND Ref( BlackBody, -3 );
price_decreasing_6 = C < Ref( C, -4 ) AND price_decreasing_5 AND Ref( BlackBody, -4 );
total_price_decreasing = price_decreasing_3 + price_decreasing_4 + price_decreasing_5 + price_decreasing_6;

oi_increasing_3 = OI > Ref( OI, -1 );
oi_increasing_4 = OI > Ref( OI, -2 ) AND oi_increasing_3;
oi_increasing_5 = OI > Ref( OI, -3 ) AND oi_increasing_4;
oi_increasing_6 = OI > Ref( OI, -4 ) AND oi_increasing_5;

oi_decreasing_3 = OI < Ref( OI, -1 );
oi_decreasing_4 = OI < Ref( OI, -2 ) AND oi_decreasing_3;
oi_decreasing_5 = OI < Ref( OI, -3 ) AND oi_decreasing_4;
oi_decreasing_6 = OI < Ref( OI, -4 ) AND oi_increasing_5;
total_oi_decreasing = oi_decreasing_3 + oi_decreasing_4 + oi_decreasing_5 + oi_decreasing_6;

new_condition = (
                    oi_up AND( total_price_increasing >= 2 )
                    OR
                    ( total_price_increasing == 4 AND oi_dn )
                ) AND ROC_price > 9;






reverse_condition1 = ( oi_dn AND total_price_decreasing >= 2 AND ROC_price < -9 );
reverse_condition2  = (
                          ( wick_size >= 0.5 AND Big AND vol_hi )
                          OR
                          ( wick_size2 >= 0.7 AND Big AND Ref( Big, -1 ) AND vol_up AND vol_up_1 )
                          OR
                          ( CP_IH AND vol_hi )
                      );
reverse_condition = reverse_condition1 OR reverse_condition2;

myVar += ", reverse_condition1 = " + reverse_condition1 + ", reverse_condition2 = " + reverse_condition2 + ", Big = " + Big;

fc = Foreign( OtherSymbol, "C" );
tooFar  = C > ( BuyExit   + 2 * floor( equityAtRisk / RLS ) );
tooFarS = C < ( ShortExit - 2 * floor( equityAtRisk / RLS ) );

ShortCondition1 = reverse_condition1 AND  NOT total_reverse_condition_flip AND NOT IsNull( fc[BarCount - 1] );


ShortCondition  = ( ShortCondition1 OR reverse_condition2 ) AND TradingZone AND  L > 20 AND H < 500 AND NOT tooFarS;
BuyCondition	= new_condition      AND TradingZone AND WhiteBody AND NOT new_condition_flip     AND NOT IsNull( fc[BarCount - 1] ) AND L > 20 AND H < 200 AND NOT tooFar;

BuyP = ShortP = 0;
InShort = InBuy = 0;
ShortI = BuyI = BuyL = ShortL = 0;
Short = Cover = 0 ;
Buy = Sell = 0;

BuyFlag = ShortFlag = False;
BF = SF = 0;
SPT = LPT = 0;
HatBC = LatSC = LatBC = HatSC = 0;
SellPrice = CoverPrice = C;
BTC = STC = 0;
SOI = BOI = 0;

for( i = 10; i < BarCount; i++ )
{
    // Update BuyExit if in a Buy position and current open is greater than BuyExit
    if( InBuy )
    {
        if( O[i] > BuyExit[i] )
        {
            BuyExit[i] = Max( BuyExit[i], BuyExit[i - 1] );
        }

        // Check if the current low is less than BuyExit to trigger a Sell
        if( L[i] < BuyExit[i] )
        {
            Sell[i] = 1;
            InShort = 0;
            InBuy = 0;
            BuyFlag = False;
            ShortFlag = False;
            BuyI = i;
            SellPrice[i] = BuyExit[i];
        }

        // Check for conditions to adjust BuyExit
        if( !TradingZone[i] ||
                ( vol_up[i - 1] && ( LongWick[i - 1] || CP_IH[i - 1] ) && C[i] < C1[i] ) ||
                ( ( price_up[i] || price_up[i - 1] ) && ( LongWick2[i] || LongWick[i] ) && ( vol_up[i] || vol_up[i - 1] ) && C[i] > BuyP ) ||
                ( i > BuyI + 18 && H[i - 1] > BuyP && C[i] < BuyP ) ||
                ( i > BuyI + 6 && oi_up[i] && C[i] < BuyP && vol_up[i] ) ||
                ( m_Below[i] && C[i] < stMA[i] ) )
        {
            BuyExit[i] = Max( BuyExit[i], floor( ( L[i] - boxSize[i] ) / boxSize[i] ) * boxSize[i] );
        }


        if( H[i] > BuyP + 40 && BlackBody[i] )
        {
            BuyExit[i] = Max( BuyExit[i], floor( L[i] / boxSize[i] ) * boxSize[i] );
        }

    }

    if( BuyFlag )
    {
        HatBC[i] = Min( HatBC[i - 1], H[i] );
    }

    if( BuyCondition[i] && !InBuy && !InShort )
    {
        if( !BuyFlag )
        {
            HatBC[i] = H[i];
        }

        LatBC = floor( ( L[i] - boxSize[i] ) / boxSize[i] ) * boxSize[i];

        ShortFlag = False;
        BuyFlag = True;
        BuyI = i;
        BuyL = Min( L[i - 1], L[i] );
    }

    BF[i] = BuyFlag;

    if( !TradingZone[i] || i > BuyI + 8 || L[i] < LatBC )
    {
        BuyFlag = False;
    }

    // Execute Buy logic
    if( BuyFlag && !CP_IH[i] && H[i] >= HatBC[i] && wick_size[i] <= 0.4 && ( C[i] < stMA1[i] || m_Above[i] ) && vol_up[i] )
    {
        Buy[i] = 1;
        BuyP = C[i];
        BuyI = i;
        BuyExit[i] = Max( C[i] - floor( equityAtRisk / RLS ), BuyExit[i] );
        InBuy = 1;
        InShort = 0;
        BuyFlag = False;
        ShortFlag = False;
        LPT = C[i] + 0.7 * floor( equityAtRisk / RLS );
        BTC = False;
        BOI = OI[i];
    }

    // Logic for Short
    if( InShort )
    {
        if( O[i] < ShortExit[i] )
        {
            ShortExit[i] = Min( ShortExit[i], ShortExit[i - 1] );
        }

        if( H[i] > ShortExit[i] )
        {
            Cover[i] = 1;
            InBuy = 0;
            InShort = 0;
            ShortFlag = False;
            BuyFlag = False;
            ShortI = i;
            CoverPrice[i] = ShortExit[i];
        }

        if( !TradingZone[i]
                OR( vol_hi[i - 1] AND CP_H[i - 1] AND C[i] > C1[i] )
                OR( ( price_dn[i] OR price_dn[i - 1] ) AND( CP_H[i] OR CP_H[i] ) AND( vol_up[i] OR vol_up[i - 1] ) AND C[i] < ShortP )
                OR( i > ShortI + 18 AND L[i - 1] < ShortP AND C[i] > ShortP )
          )
        {
            ShortExit[i] = Min( ShortExit[i], floor( ( H[i] + boxSize[i] ) / boxSize[i] ) * boxSize[i] );
        }
    }

    if( ShortFlag )
    {
        HatSC[i] = Max( HatSC[i - 1], L[i] );
    }

    // Check for ShortCondition to enter a Short position
    if( ShortCondition[i] && !InShort )
    {
        if( !ShortFlag )
        {
            HatSC[i] = L[i];
        }

        LatSC = floor( ( H[i] + boxSize[i] ) / boxSize[i] ) * boxSize[i];

        BuyFlag = False;
        ShortFlag = True;
        ShortI = i;
        ShortL = Max( H[i - 1], H[i] );
    }

    SF[i] = ShortFlag;

    // Reset ShortFlag if conditions are met
    if( !TradingZone[i] || i > ShortI + 8 || ( H[i] > LatSC AND NOT CP_IH[i] ) )
    {
        ShortFlag = False;
    }




    // Execute Short logic
    if( ShortFlag && C[i] < HatSC[i] && BlackBody[i] && ( C[i] > stMA1[i] || m_Below[i] ) )
    {
        Short[i] = 1;
        ShortP = C[i];
        ShortI = i;
        ShortExit[i] = IIF( CP_IH[i],  C[i] + floor( equityAtRisk / RLS ), Min( C[i] + floor( equityAtRisk / RLS ), ShortExit[i] ) );
        InShort = 1;
        InBuy = 0;
        ShortFlag = False;
        BuyFlag = False;
    }

    // Reset Buy conditions if not in TradingZone
    if( !TradingZone[i] )
    {
        BuyP = 0;
        ShortP = 0;
        InBuy = 0;
        InShort = 0;
    }
}


myVar += ", HatSC " + HatSC;
myVar += ", BF = " + BF + ", BC = " + BuyCondition;
myVar += ", SF = " + SF + ", SC = " + ShortCondition;
myVar += ", LongWick = " + wick_size + ", LongWick2 = " + wick_size2;



Sell = Sell OR NOT TradingZone OR Short;
Cover = Cover OR NOT TradingZone OR C < 5 OR Buy;
// Buy = Buy AND isITM AND ITMRange == 1 AND daysToExpiry < 7;
// Short = Short AND isITM AND ITMRange == 2 AND daysToExpiry < 7;
Buy = Buy AND daysToExpiry < 7;
// Short = Short AND isITM AND daysToExpiry < 7;
InBuy = InShort = 0;
BuyPrice = ShortPrice = C;

SetChartBkColor( LastValue( IIf( isITM AND ITMRange == 2, colorLightGrey, colorWhite ) ) );

Plot( C, "", ParamColor( "Color", colorBlack ), styleCandle );
Plot( ShortExit, "SE", IIf( SF, colorRed, colorGrey40 ), styleDashed );
Plot( BuyExit, "BE", IIf( BF, colorLime, colorGrey40 ), styleDashed );
PlotShapes( BF*shapeSmallCircle, colorGreen, 0, L - 1 );
PlotShapes( SF*shapeSmallCircle, colorRed, 0, H + 20 );
Plot( stMA, "", IIf( m_Above, colorLime, IIf( m_Below, colorBrown, colorWhite ) ), styleLine | styleThick );

Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );

bRisk = BuyPrice - BuyExit;
bLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( bRisk, bRisk, 1 ) ) ) ) ) );

sRisk = ShortExit - ShortPrice;
sLots	= Min( 4, floor( Max( 0, floor( equityAtRisk / ( RLS * IIf( sRisk, sRisk, 1 ) ) ) ) ) );
myvar 	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar	+= ", risk = " + round( RLS*bRisk ) + ", " + round( RLS*sRisk );
myvar	+= StrFormat( ", exit = %0.0f, %0.0f", BuyExit, ShortExit );
myvar 	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );

SetPositionSize( IIf( Buy, bLots, sLots )*RLS, spsShares );
#include <Alert.afl>

_N( Title = StrFormat( EncodeColor( colorDarkGrey )
                       + "H %0.2f, "
                       + "L %0.2f, "
                       + EncodeColor( colorBlack ) + "C %0.2f (%.2f%%), "
                       + EncodeColor( colorDarkGrey ) + "{{VALUES}}"
                       + EncodeColor( colorDarkGrey ) + myVar
                       , H, L, C, ROC( C, 1 )
                     ) );

_SECTION_END();