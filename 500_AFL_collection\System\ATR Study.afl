//------------------------------------------------------------------------------
//
//  Formula Name:    ATR Study
//  Author/Uploader: steve wiser 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-11-23 18:09:31
//  Origin:          
//  Keywords:        ATR
//  Level:           medium
//  Flags:           system,exploration,indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=135
//  Details URL:     http://www.amibroker.com/library/detail.php?id=135
//
//------------------------------------------------------------------------------
//
//  This was simply a study at seeing how I could use the ATR function in a
//  trading system.
//
//  System settings:
//
//  25 $ per trade
//
//  Buy and sell on signal one day delay for both
//
//  Start equity of $10000
//
//  Long trades only on daily selection
//
//  Stops controlled by code
//
//------------------------------------------------------------------------------

//* ATR Study:  */

Exclude = MA(V,50)<200 ;
MaxGraph = 12;
Graph0 = C;
Graph0Color = 1;
Graph0Style = 64;

BuyOffSet = 18;//Optimize("BuyOffSet",18,15,20,1);
SellOffset = BuyOffSet;//Optimize("SellOffset",2,2,14,2);
RegLength = 5;//Optimize("RegLength",5, 2,11,2);
BuyATRPeriod = 2;//Optimize("BuyATRPeriod",2,2,5,1);
SellATRPeriod = BuyATRPeriod;//Optimize("SellATRPeriod",4,2,11,2);
ATRMultiplier = 1;//Optimize("ATRMultiplier",1,0.7,1.25,.05);


Graph8 = HHV(H-ATRMultiplier*ATR(BuyATRPeriod),BuyOffset);   /* RED */
Graph9 = LLV(L+ATRMultiplier*ATR(SellATRPeriod),SellOffset);  /*  GREEN */

Graph8Style=Graph9Style = 5;
Graph9Color=  5;  /* 5 is green */
Graph8Color = 4;  /* 4 is red */

ticker = 0.0;//Optimize("Tickerk",0,0,1,0.125);

Buy = Cross(C,Graph8) AND C>Graph9 AND LinRegSlope(EMA(C,17),2)>0;
Sell = Cross(Graph8,C) AND LinRegSlope(C,2)<0;
Buy = ExRem(Buy,Sell);
Sell = ExRem(Sell,Buy);
BuyStop = Ref(Graph8,-1)+ ticker;
BuyPrice = Max(BuyStop,Low);
SellStop= Ref(Graph8,-1);   //IIf(Cross(Graph8,C),Ref(Graph8,-1),0);
SellPrice = Min( SellStop, High )- ticker; 

//ApplyStop(2,3,Optimize("TrailingStop",15,0,20,5),1);

Filter=   (Buy OR Sell)      ;      
NumColumns = 8;
Column0 =  IIf(Buy==1,1,IIf(Sell==1,-1,0) );
Column0Format = 1.0;
Column0Name = "Long/Sell";
Column1 = C;
Column1Name = "Close       ";
Column1Format = 1.2;
Column2 = MA(V,17);
Column2Name = "17 Ma Vol   ";
Column2Format = 1.0;
Column3 = MA(C,17)/MA(C,50);
Column3Name = "% 17/50 ";
Column3Format = 1.2;
Column3Format = 1.2;
Column4= MA(C,17);
Column4Name="17 C ma";
Column4Format = 1.2;
Column5= MA(C,50);
Column5Name="50 C ma";
Column5Format = 1.2;

Column6= BuyPrice;
Column6Name="BuyPrice";
Column6Format = 1.2;
Column7= SellPrice;
Column7Name="Sellprice";
Column7Format = 1.2;