//------------------------------------------------------------------------------
//
//  Formula Name:    <PERSON><PERSON>
//  Author/Uploader: Sainath 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2004-11-07 06:32:34
//  Origin:          Sniper Trading-
//  Keywords:        Sniper trading
//  Level:           basic
//  Flags:           exploration,indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=398
//  Details URL:     http://www.amibroker.com/library/detail.php?id=398
//
//------------------------------------------------------------------------------
//
//  SNiper trading emphasizes on day of week trading, rather than trading every
//  day.Good stuff for those int in day trading and taking positions.
//
//------------------------------------------------------------------------------

R1=(MA(H,3)-MA(Ref(L,-1),3))+L;
R2=((MA(Ref(H,-1),3))-MA(H,3))+H;
R3=H;
R4=2*Avg-L;
Sellno=(R1+R2+R3+R4)/4;
S1=H-(MA(Ref(H,-1),3)-MA(L,3));
S2=L-(MA(Ref(L,-1),3)-MA(L,3));
s3=L;
S4=2*Avg-H;
Buyno=(S1+S2+S3+S4)/4;
//1-Day strength
DS1=(C-L)/(H-L)*100;
//5-Day Strength
DS5=(C-LLV(L,5))/(HHV(H,5)-LLV(L,5))*100;
//LSS-5Day Oscillator
X=HHV(H,5)-Ref(O,-5);
Y=C-LLV(L,5);
LSSO=(X+Y)/((HHV(H,5)-LLV(L,5))*2);
AvgLSSO=MA(LSSO,3)*100;
//Momentum
Mom=AvgLSSO-Ref(AvgLSSO,-3);
Condb1=94.5<DS5>65 AND LSSO>50 AND mom>-10;
Condb2=DS5<83 AND LSSO<50 AND mom>-40;
Conds1=DS5>53 AND LSSO<70 AND mom>-10;
Buy=IIf(Cross(DS5,80),Condb1,DayOfWeek()==1);
Sell=IIf(Cross(20,DS5),Condb2,DayOfWeek()==3);
Filter=Buyno AND  Sellno;
AddColumn(Buyno,"BuyNo");
AddColumn(Sellno,"SellNo");
AddColumn(DS1,"1-Day Strength");
AddColumn(DS5,"5-Day Strength");
AddColumn(AvgLSSO,"AVg LSS 5-Day OSC ");
AddColumn(Mom,"Momentum ");

