/*****************************************************************
** Strategy: Merged Simple Line Momentum & Linear Regression
** Version: 1.0
**
** --- LOGIC ---
** Long:  From "Linear Regression" by Trading Tuitions.
** Enters long based on a filter using Linear Regression,
** VWMA, and various price action conditions.
**
** Short: From "Simple Line Momentum v1.3".
** Enters short on two down-moves of a buffer line
** without a corresponding up-move in the opposite buffer.
**
** Merged by: Gemini
******************************************************************/

//----------------------------------------------------------------
// SECTION: INCLUDES & SETTINGS
//----------------------------------------------------------------
#include <Common.afl>

//----------------------------------------------------------------
// SECTION: PARAMETERS
//----------------------------------------------------------------
_SECTION_BEGIN( "Parameters" );
lastTradeOfTheDayTime	= 151000;
TradingZone           	= ( TimeNum() >= firstTradeOfTheDayTime AND TimeNum() <= lastTradeOfTheDayTime );

// -- General Params --
eqAtRisk = Param( "Risk", 25000, 20000, 45000, 5000 );

// -- Long Strategy Params --
f_Stdev = Param( "F StDev (Long)", 1.4, 1, 3, 0.1 );

// -- Short Strategy Params --
Periods_Short = Param( "Periods (Short)", 90, 20, 200, 2 );
boxSizePercent = Param( "Box %", 0.1, 0.1, 1, 0.1 );
boxSizeORbFactor = Param( "ORB ATR factor", 0.9, 0.2, 1, 0.1 );
p_no_above = Param( "No Above MA (Short)", 60, 5, 200, 5 );

_SECTION_END();

//----------------------------------------------------------------
// SECTION: CUSTOM BAR & INDICATORS
//----------------------------------------------------------------
_SECTION_BEGIN( "Calculations" );

// -- Custom Bar OHLC (Shared Logic) --
firstBar = Day() != Ref( Day(), -1 );
ORbHigh = ValueWhen( firstBar, High );
ORbLow = ValueWhen( firstBar, Low );
ORbCenter = ValueWhen( firstBar, ( ORbHigh + ORbLow ) / 2 );
tr = ATR( 5 * Periods_Short );
ORBAtr = ValueWhen( firstBar, tr );
s_box_size = round( Max( boxSizePercent * ORbCenter / 100, boxSizeORbFactor * ORBAtr ) / 5 ) * 5;

rH = round( H / s_box_size );
myH = s_box_size * IIf( rH < H / s_box_size, rH + 1, rH );
rL = round( L / s_box_size );
myL = s_box_size * IIf( rL > L / s_box_size, rL - 1, rL );
rO = round( O / s_box_size );
myO = s_box_size * IIf( rO > O / s_box_size, rO - 1, rO );
rC = round( C / s_box_size );
myC = s_box_size * IIf( rC > C / s_box_size, rC - 1, rC );

myC = IIf( myH - C < s_box_size / 3, myH, myC );
myC = IIf( C - myL < s_box_size / 3, myL, myC );

// -- Short Strategy Indicators --
Vw_Short = KAMA( myC, Periods_Short );
Vw_Short_1 = Ref( Vw_Short, -1 );
top_Short = HMA( myH, ceil( Periods_Short / 2 ) );
bottom_Short = HMA( myL, ceil( Periods_Short / 2 ) );
Lr_Short = ( top_Short + bottom_Short ) / 2;
Lr_Short_1 = Ref( Lr_Short, -1 );
buffer_line_up = ( round( Vw_Short_1 / s_box_size ) + 5 ) * s_box_size;
buffer_line_down = ( round( Vw_Short_1 / s_box_size ) - 5 ) * s_box_size;
No_Above_MA = BarsSince( C < Vw_Short_1 );
candle_size = ( myH - myL ) / s_box_size;

// -- Long Strategy / Shared Indicators --
mode = Flip( Lr_Short > Vw_Short + s_box_size, Lr_Short < Vw_Short - s_box_size );
Sd = myBox( StDev( C, Periods_Short ) );
four_sd_down = C - f_Stdev * SD;

_SECTION_END();

//----------------------------------------------------------------
// SECTION: TRADING LOGIC
//----------------------------------------------------------------
_SECTION_BEGIN( "Trade Execution" );

// -- Initialize Signal Arrays --
Buy = Sell = Short = Cover = 0;
exit = Null;

// -- State Management & Flags --
buy_flag = 0;
short_flag = 0;
long_state = 0;
short_state = 0;

// -- Loop-Specific Trend Flags --
bl_up_dn = 0;
bl_up_up = 0;
bl_down_dn = 0;
bl_down_up = 0;

// -- Pre-Calculate Stops & Risk --
buy_exit = Max( floor( Vw_Short_1 / s_box_size ) * s_box_size, four_sd_down );
buy_risk = C - buy_exit;
short_exit = floor( Vw_Short_1 / s_box_size ) * s_box_size;
short_risk = short_exit - C;

for( i = 2; i < BarCount; i++ )
{
    // --- Ratchet buffer lines for short logic ---
    buffer_line_down[i] = IIf( myC[i] > buffer_line_down[i]   AND buffer_line_down[i] < buffer_line_down[i - 1], buffer_line_down[i - 1], buffer_line_down[i] );
    buffer_line_up[i]   = IIf( myC[i] < buffer_line_up[i - 1] AND buffer_line_up[i]   > buffer_line_up[i - 1]  , buffer_line_up[i - 1]  , buffer_line_up[i] );

    bl_down_dn[i] = IIf( buffer_line_down[i] > buffer_line_down[i - 1], False, IIf( buffer_line_down[i] < buffer_line_down[i - 1], True, bl_down_dn[i - 1] ) );
    bl_up_dn[i] = IIf( buffer_line_up[i] > buffer_line_up[i - 1], False, IIf( buffer_line_up[i] < buffer_line_up[i - 1], True, bl_up_dn[i - 1] ) );

    bl_down_up[i] = IIf( buffer_line_down[i] < buffer_line_down[i - 1], False, IIf( buffer_line_down[i] > buffer_line_down[i - 1], True, bl_down_up[i - 1] ) );
    bl_up_up[i] = IIf( buffer_line_up[i] < buffer_line_up[i - 1], False, IIf( buffer_line_up[i] > buffer_line_up[i - 1], True, bl_up_up[i - 1] ) );

    short_trend[i] = bl_down_dn[i] && bl_up_dn[i];
    long_trend[i] = bl_down_up[i] && bl_up_up[i];

    s_state = short_trend[i] AND NOT long_trend[i];
    l_state = long_trend[i] AND NOT short_trend[i];

    // --- LONG POSITION MANAGEMENT ---
    if( buy_flag )
    {
        t_exit = buy_exit[i];
        exit[i] = exit[i - 1];

        if( O[i] > t_exit )
        {
            exit[i] = Max( t_exit, exit[i - 1] );
        }
    }

    // --- EXIT TRIGGERS ---
    // SELL (Exit Long Position)
    if( buy_flag AND L[i] < exit[i] )
    {
        Sell[i] = True;
        buy_flag = 0;
        long_state = 1;
        SellPrice[i] = IIf( O[i] > exit[i], exit[i], C[i] );
    }

    // --- SHORT ENTRY LOGIC ---
    // if( s_state ) long_state = 0;

    // --- ENTRY TRIGGERS
    if( long_state == 2 AND candle_size[i] < 7 AND myC[i] > myO[i] AND NOT buy_flag AND myL[i] + s_box_size[i] > Vw_Short_1[i] AND Lr_Short_1[i] > Vw_Short_1[i] AND myH[i] > myH[i - 1] AND TradingZone[i])
    {
        Buy[i] = 1;
        buy_flag = True;
        long_state = 3; // Post-entry state
        exit[i] = buy_exit[i];
    }
    else
        if( long_state == 1 AND Lr_Short_1[i] < Vw_Short_1[i] )
        {
            long_state = 2; // Armed state
        }
        else
            if( long_state == 0 AND C[i] > Vw_Short_1[i] AND NOT s_state )
            {
                long_state = 1; // Armed state
            }


    // --- SHORT POSITION MANAGEMENT ---
    if( short_flag )
    {
        exit[i] = exit[i - 1];

        if( O[i] < Vw_Short_1[i] )
        {
            exit[i] = Min( short_exit[i], exit[i - 1] );
        }
    }

    // COVER (Exit Short Position)
    if( short_flag AND C[i] > exit[i] )
    {
        Cover[i] = True;
        short_flag = 0;
        short_state = 1; // Re-arm short state
    }

    // --- SHORT ENTRY LOGIC ---
    if( C[i] > buffer_line_up[i] ) short_state = 0;

    if( short_state == 1 AND myC[i] < buffer_line_down[i - 1] AND myH[i] < Vw_Short_1[i] AND candle_size[i] < 7 AND myC[i] < myO[i] AND NOT short_flag  AND TradingZone[i])
    {
        Short[i] = 1;
        short_flag = True;
        short_state = 3; // Post-entry state
        exit[i] = short_exit[i];
        long_state = 0;
    }
    else
        if( short_state == 0 AND No_Above_MA[i - 1] > p_no_above AND myC[i] < Vw_Short_1[i] )
        {
            short_state = 1; // Armed state
        }

    state[i] = short_state + 10 * long_state;
}

// Ensure final exit array is rounded
// exit = myRound( exit );

// --- POSITION SIZING ---
buy_risk = Max( 1, buy_risk );
short_risk = Max( 1, short_risk );

bLots = Min( floor( eqAtRisk / ( RoundLotSize * buy_risk ) )  , floor( 7500000 / ( RoundLotSize * C ) ) );
sLots = Min( floor( eqAtRisk / ( RoundLotSize * short_risk ) ), floor( 7500000 / ( RoundLotSize * C ) ) );

PositionSize = IIf( Buy, bLots, IIf( Short, sLots, 0 ) ) * RoundLotSize;
SetPositionSize( PositionSize, spsShares );
myvar  	+= StrFormat( ", lots = %0.0f, %0.0f", bLots, sLots );
myvar  	+= StrFormat( ", box = %0.0f, %0.0f", myBoxC( H + boxSize ), myboxF( L - boxSize ) );
myvar	+= ", risk = " + round( RoundLotSize*buy_risk ) + ", " + round( RoundLotSize*short_risk );

_SECTION_END();

//----------------------------------------------------------------
// SECTION: PLOTTING
//----------------------------------------------------------------
_SECTION_BEGIN( "Chart Display" );

// -- Background Color --
bkcolor = LastValue( IIf( mode, ColorBlend( colorCustom9, colorWhite ), ColorBlend( colorCustom12, colorWhite ) ) );
SetChartBkColor( bkcolor );

// -- Price and Exit Levels --
PlotOHLC( myO, myH, myL, myC, "", colorDefault, styleCandle );
Plot( IIf( exit, exit, Null ), "exit", colorBrown, styleStaircase | styleDashed, Null, Null, Null, -1 );

// -- Indicators from Short Logic --
Plot( buffer_line_up, "", ColorRGB( 68, 134, 238 ) );
Plot( buffer_line_down, "", ColorRGB( 205, 51, 51 ) );
Plot( Lr_Short, "", colorGrey40 );
Plot( Vw_Short, "VWMA (Short)", IIf( mode, colorGreen, colorPink ), styleThick | styleNoLabel );

// -- Trade Shapes --
PlotShapes( shapeSmallUpTriangle * Buy, colorGreen, 0, myL, -10 );
PlotShapes( shapeSmallDownTriangle * Sell, colorDarkGreen, 0, myH, 10 );
PlotShapes( shapeSmallDownTriangle * Short, colorRed, 0, myL, 10 );
PlotShapes( shapeSmallUpTriangle * Cover, colorDarkRed, 0, myL, -10 );

// -- Title --
_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + Name() + ", {{INTERVAL}}, {{DATE}}, "
                         + "Open %0.2f, "
                         + "High %0.2f, "
                         + "Low %0.2f, "
                         + EncodeColor( colorBlack ) + "Close %0.2f (%.2f%%)"
                         + EncodeColor( colorDarkGrey ) + ", {{VALUES}}"
                         + EncodeColor( colorDarkGrey ) + myVar
                         , Open, High, Low, Close, ROC( C, 1 )
                       ) );

_SECTION_END();