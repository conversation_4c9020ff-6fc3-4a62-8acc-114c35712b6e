_SECTION_BEGIN( "OI Diff" );
#include <Common.afl>
#include <Options.afl>

NIFTYC = Foreign( "NIFTY 50.NSE_IDX", "C" );

//=================================================================================
// Put
//=================================================================================
SetForeign( SymbolPE );
strike = GetStrike( SymbolPE );
printf("SymbolPE %s\n", SymbolPE);
ce = 1;
diff = MA(IIf(NIFTYC < strike, C + ce * ( NIFTYC - strike ), 0), 2);
maC = MA(C, 3);

m_fill = OptimizeNot( "fill", 20, 1, 50, 1 ) / 100;
d_PL = Ref( diff, -1 ) * ( 1 - m_fill );
d_PH = Ref( diff, -1 ) * ( 1 + m_fill );
d_AH = LLV( d_PH, 1 + Nz( BarsSince( diff < Ref( diff, -1 ) ), 1 ) );
d_AL = HHV( d_PL, 1 + Nz( BarsSince( diff > Ref( diff, -1 ) ), 1 ) );

m_fill = OptimizeNot( "fill", 10, 1, 50, 1 ) / 100;
c_PL = Ref( maC, -1 ) * ( 1 - m_fill );
c_PH = Ref( maC, -1 ) * ( 1 + m_fill );
c_AH = LLV( c_PH, 1 + Nz( BarsSince( maC < Ref( maC, -1 ) ), 1 ) );
c_AL = HHV( c_PL, 1 + Nz( BarsSince( maC > Ref( maC, -1 ) ), 1 ) );

closeColor = IIf(maC > c_AH, colorLime, IIf(maC < c_AL, colorRed, colorLightGrey));
Plot( C, "Close", closeColor, styleThick | styleCandle );

diffColor = IIf(maC > c_AH AND diff < d_AL AND diff > 0, colorGreen, IIf(maC < c_AL AND diff > d_AH AND diff > 0, colorRed, colorGrey40));
Plot( diff, "diff", diffColor, styleLine | styleThick );
Plot( HHV(Ref(diff, -1), Min(30, BarsSince(firstBarOfTheDay) + 1)), "", colorBlue, styleDashed | styleStaircase);
Plot( LLV(Ref(diff, -1), Min(30, BarsSince(firstBarOfTheDay) + 1)), "", colorBlue, styleDashed | styleStaircase);

vlots = V / RLS;
vCheck = vlots / MA( Ref( vlots, -1 ), 35 );
PlotShapes( IIf( vCheck > 5, shapeDigit5, IIf( vCheck > 3, shapeDigit3, shapeNone ) ), colorBlack, 0, L, -10 );
_N( Title   = StrFormat( "{{VALUES}}") );

Cover = Buy =  maC > c_AH AND diff < d_AL AND diff > 0;
Sell  = maC < c_AL;
Short = maC < c_AL AND diff > d_AH AND diff > 0;
SetPositionSize( RLS, spsShares );
Buy = ExRem( Buy, Sell );
Sell = ExRem( Sell, Buy );
Short = ExRem( Short, Cover );
Cover = ExRem( Cover, Short );
#include <Alert.afl>

RestorePriceArrays();

_N( Title   = StrFormat( EncodeColor( colorDarkGrey ) + SymbolPE + ", {{DATE}}, {{VALUES}}") );


_SECTION_END();