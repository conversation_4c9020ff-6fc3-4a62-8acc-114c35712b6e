_SECTION_BEGIN("FRAMA and T3MA Trading System");

// Set the parameters for FRAMA and T3MA
N = 10;
T3MA_Length = 10;
T3MA_Multiplier = 0.7;

// Calculate FRAMA
Price = ( H + L ) / 2;
N3 = ( HHV( High, N ) - LLV( Low, N ) ) / N;

HH = HHV( High, N / 2 ) ;
LL = LLV( Low, N / 2 );

N1 = ( HH - LL ) / ( N / 2 );

HH = HHV( Ref( High, - N / 2 ), N / 2 );
LL = LLV( Ref( Low, - N / 2 ), N / 2 );

N2 = ( HH - LL ) / ( N / 2 );

Dimen = IIf( N1 > 0 AND N2 > 0 AND N3 > 0, ( log( N1 + N2 ) - log( N3 ) ) / log( 2 ), Null );

alpha = exp( -4.6 * ( Dimen - 1 ) );
alpha = Min( Max( alpha, 0.01 ), 1 ); // bound to 0.01...1 range
Frama = AMA( Price, alpha );

SetPositionSize( RoundLotSize, spsShares );


// Calculate T3MA
E1 = EMA(Close, T3MA_Length);
E2 = EMA(E1, T3MA_Length);
E3 = EMA(E2, T3MA_Length);
C1 = -T3MA_Multiplier * T3MA_Multiplier * T3MA_Multiplier;
C2 = 3 * T3MA_Multiplier * T3MA_Multiplier + 3 * T3MA_Multiplier * T3MA_Multiplier * T3MA_Multiplier;
C3 = -6 * T3MA_Multiplier * T3MA_Multiplier - 3 * T3MA_Multiplier - 3 * T3MA_Multiplier * T3MA_Multiplier * T3MA_Multiplier;
C4 = 1 + 3 * T3MA_Multiplier + 3 * T3MA_Multiplier * T3MA_Multiplier + T3MA_Multiplier * T3MA_Multiplier * T3MA_Multiplier;
T3MA = C1 * E3 + C2 * E2 + C3 * E1 + C4 * Close;

// Generate Buy and Sell signals
Buy = Cross(FRAMA, T3MA) AND Close > FRAMA AND Close > T3MA;
Sell = Cross(T3MA, FRAMA) AND Close < FRAMA AND Close < T3MA;

// Plot the signals on the chart
Plot( Close, "Close", colorBlack, styleNoTitle | GetPriceStyle(), Null, Null, Null );
PlotShapes(IIf(Buy, shapeSquare, shapeNone), colorGreen, 0, L, Offset=-40);
PlotShapes(IIf(Sell, shapeSquare, shapeNone), colorRed, 0, H, Offset=-40);
Plot(T3MA, "T3MA", colorBlue, styleThick);
Plot(FRAMA, "FRAMA", colorPink, styleThick);

// Set the stop-loss and profit target
StopLoss = 0.02 * Close;
ProfitTarget = 0.04 * Close;

// Apply the stop-loss and profit target
ApplyStop(1, 2, StopLoss, 1, True);
ApplyStop(1, 1, ProfitTarget, 1, True);

_SECTION_END();
