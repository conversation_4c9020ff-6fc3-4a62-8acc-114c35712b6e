#include <TimeLeft.afl>

// Volume analysis AFL
// Calculates moving average and multiple of volume for each bar
// Colors the bars based on volume and close price

// Define user inputs
MA_period = Param("MA period", 100, 1, 1000, 1);
WMA_period = Param("WMA period", 50, 1, 1000, 1);

// Calculate indicators
V_MA = MA( V, MA_period );
MVA = Volume*TimeFrame/SecsToGo;
vol_color = IIf (V > V_MA, IIf( C > O, colorBrightGreen, colorPink ), colorWhite);
vol_multiples = Volume/V_MA;

// Plot indicators
Plot(V_MA , "V MA", colorOrange, ParamStyle("MA style", styleLine | styleThick) ); 
Plot( Volume, "Volume", vol_color, styleHistogram | styleThick, maskHistogram );
Plot( vol_multiples, "V Multiples", colorLightBlue, styleLine | styleLeftAxisScale, maskHistogram );

