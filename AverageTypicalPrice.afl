#include<Common.afl>
/**************************************************************************/
/* Oscillates based on difference between Average Price (H+L+O+C)/4, and Typical Price (H+L+C)/3.
Results in a very smooth curve.
Accompanying Price Candles enhanced with Tail-Color based on adjustable Volume Percentage Rise or Fall
   -Nick Molchanoff (d9)
/**************************************************************************/
_SECTION_BEGIN("Settings");

SetBarsRequired(10000,10000);
SetFormulaName("Sample System");
SetTradeDelays( 1, 1, 1, 1 );
SetOption( "initialequity", 100000 );
SetOption( "MaxOpenPositions", 3 );
SetOption( "PriceBoundChecking", 1 );
SetOption( "CommissionMode", 2 );
SetOption( "CommissionAmount", 8.00 );
SetOption( "UsePrevBarEquityForPosSizing", 1 );
SetChartOptions(0,chartShowArrows|chartShowDates|chartWrapTitle|chartLogarithmic);
PositionScore = 100/C;
PositionSize = -10;
GraphXSpace =15;
GraphLabelDecimals = 2;

_SECTION_END();

/**********************************************************************************/

_SECTION_BEGIN("UsefulDefs");

Close1 = C1 = Ref(C, -1);
upClose = upC = (C > C1); 
downClose = dnC = (C <= C1);
UpDay = upD =( C > O); 
downDay = dnD = (C <= O);

open1 = O1 = Ref(O, -1);
upOpen = upO = (O > O1);
downOpen = dnO = (O <= O1);
upDay = upD = (O <= C); 
downDay = dnD = (O > C);

openCloseDiff = ocd = abs(C-O);

High1 = H1 = ref(H, -1);
upHigh = upH = (H > H1);  
downHigh = dnH = (H <= H1);

Low1 = L1 = Ref(L, -1);
upLow = upL = (L > L1);  
downLow = dnL = L <= L1;

top = t= Max(O, C); 
bot = b= Min(O, C);
avp = av4 = ((O+H+L+C)/4);
typ = tp3= ((H+L+C)/3);
avp = av4 = ((TimeFrameGetPrice("O", inDaily)+TimeFrameGetPrice("H", inDaily)+TimeFrameGetPrice("L", inDaily)+TimeFrameGetPrice("C", inDaily))/4);
typ = tp3= ((TimeFrameGetPrice("H", inDaily)+TimeFrameGetPrice("L", inDaily)+TimeFrameGetPrice("C", inDaily))/3);

upperShadow = us = H-t;
lowerShadow = ls = b-L;
shadows = shds = us+ls;
 
downPressure = dnP = IIf(upd,shds, ocd + shds);
upPressure = upP =  IIf(upd, ocd + shds, shds);

Volume1 = V1 = Ref(V, -1);
upVolume =  upV =V > v1;
fib=1.618033889;

Vc= Param("Volatility channel -",10,2,14);
Up=HHV(((((H+L+C)/3)*2)- H),Vc);
Lo=LLV(((((H+L+C)/3)*2)- L),Vc);

upDownColor = udcolor = IIf(C>O, colorDarkGreen, colorDarkRed);
breakOutColor = boColor = IIf(C>up AND upV, colorBrightGreen, colorRed);
nColor = IIf(C>up, bocolor, udcolor);

per = Param("per", 15, 2, 256, 1);

_SECTION_END();


/**************************************************************************/

/**************************************************************************/

_SECTION_BEGIN("AvgTyp Osc");

per   = Param( "Per", 15, 2, 200, 1);
sep   = Param( "Sep", 0, -10, 10, 1);
matp3 = EMA(tp3, per);
maav4 = EMA(av4,per+sep);
diff= matp3-maav4;
// pato: Percent Average/Typical Price Oscillator
pato = 100*diff/maav4;
clr = IIf(matp3>maav4, colorGreen, colorRed);
//Plot(pato, "pato", clr, styleHistogram);
pvcolor = IIf(pato>0, colorDarkGreen, colorDarkRed);
minclip =0; maxclip = 0;
PlotOHLC(pato,pato,0,pato, "", pvColor, styleCloud |styleNoLabel | styleClipMinMax , minclip, maxclip, Null, -2, null );
Plot(pato, "", clr, styleLine|styleNoLabel);
Plot(matp3, "", colorPalegreen, styleLine|styleLeftAxisScale);
Plot(maav4, "", colorOrange, styleline|styleLeftAxisScale);

_SECTION_END();

_SECTION_BEGIN("Price w Volume Colored Shadows");

per = Param("per", 15, 1, 256, 1);
t = Max(O, C); 
b = Min(O, C);
md = (t+b)/2;

hv = HHV(V, per);
lv = LLV(V, per);
noDiv0 = 0.000001;
diffV = hv-lv + noDiv0;

pv  = b +  ( ( (V*1.2 - lv) * ( t - b ) ) / diffV ) ;
pv1 = Ref(pv, -1);
v1  = Ref(V, -1);
av  = wilders(V, per);
PriceOn = ParamToggle("Show Price"," No|Yes", 1);

hc = HHV(C, per);
lc = LLV(C, per);
diffc = hc-lc + noDiv0;
pvf = (V*1.2 - lv) / diffV ;
pcf = (C*1.2 - lc) / diffC ;

hvcf  = pvf > 1.2;

if (PriceOn)
{
SetBarFillColor(IIf(hvcf, colorYellow, colorDarkTeal));
Plot(C, "", colorDefault, styleCandle|styleLeftAxisScale);
Plot(0, "0",colorWhite,styleDashed|styleNoLabel | StyleNoTitle);
}

_SECTION_END();

/*
_SECTION_BEGIN("TITLE");
PrChgPct = (C-Ref(C,-1))*100/Ref(C,-1);
PrChgAmt = C-Ref(C,-1);
_N(Title = 
//Basics...
EncodeColor(colorSkyblue) +"High  = "  + EncodeColor(colorwhite) + NumToStr(H,1.2)+ 
EncodeColor(colorSkyblue) +", Low  = " + EncodeColor(colorwhite) + NumToStr(L,1.2) +
EncodeColor(colorSkyblue) +", Open = " + EncodeColor(colorwhite) + NumToStr(o,1.2) + 
EncodeColor(colorSkyblue) +", Close =" + WriteIf( 
	matp3 > maav4, EncodeColor(colorBrightGreen) + NumToStr(C,1.2) + 
				EncodeColor(colorSkyBlue)      + ", (Chg %) "   + EncodeColor(colorBrightGreen)+ NumToStr(PrChgPct,1.2)+"%" + 
				EncodeColor(colorSkyBlue)      + ", (Chg Amt) " + EncodeColor(colorBrightGreen)+ NumToStr(PrChgAmt,1.2) + 
				EncodeColor(colorSkyBlue)      + ", (tp3) "     + EncodeColor(colorBrightGreen)+ NumToStr(tp3,1.2) +
				EncodeColor(colorSkyBlue)      + ", (av4) "     + EncodeColor(colorBrightGreen)+ NumToStr(av4,1.2),
// else if down
				EncodeColor(colorRed) + NumToStr(C,1.2) +
				EncodeColor(colorSkyBlue)      + ", (Chg %) "   + EncodeColor(colorRed) + NumToStr(PrChgPct,1.2) + "%" +
				EncodeColor(colorSkyBlue)      + ", (Chg Amt) " + EncodeColor(colorRed) + NumToStr(PrChgAmt,1.2) + 
				EncodeColor(colorSkyBlue)      + ", (tp3) "     + EncodeColor(colorRed) + NumToStr(tp3,1.2)+
				EncodeColor(colorSkyBlue)      + ", (av4) "     + EncodeColor(colorRed) + NumToStr(av4,1.2)
) + 
EncodeColor(colorYellow) + ", Volume  =" + WriteIf( Ref(V,-1)<V , EncodeColor(colorLime) + WriteVal( V, 1.0 ), EncodeColor(colorRed) + WriteVal( V, 1.0 ))
						+ EncodeColor( -1 ) + "{{VALUES}}" 
);

_SECTION_END();

*/