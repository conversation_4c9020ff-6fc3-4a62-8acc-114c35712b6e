SetChartOptions(0,chartShowArrows|chartShowDates);
_N(Title = StrFormat("{{NAME}} - {{INTERVAL}} {{DATE}} Open %g, Hi %g, Lo %g, Close %g (%.1f%%) {{VALUES}}", O, H, L, C, SelectedValue( ROC( C, 1 ) ) ));
Plot( C, "Close", ParamColor("Color", colorDefault ), styleNoTitle | ParamStyle("Style") | GetPriceStyle() );

_SECTION_BEGIN( "AlgoFoxAuto" );

APIKEY = "";
IM = ParamToggle("Intraday Mode", "Off|On", 1);
TSTIME = Param( "Trade Start From(HHMM)", 915, 600, 2400, 1 );
NOETIME = Param( "No Entry After(HHMM)", 1445, 900, 2400, 1 );
EXTIME = Param( "Square Off Time(HHMM)", 1515, 900, 2400, 1 );
INSTR = ParamList( "Instrument", "EQ|FUTIDX|FUTSTK|OPTIDX|OPTSTK|FUTCOM|FUTCUR" );
PRDT = ParamList( "Product Type", "MIS|NRML|CNC" );
ORTYPE = ParamList( "Order Type", "Market|Limit" );
PORP = ParamToggle( "Point OR Percent", "Point|Percent", 1 );
LOB = Param( "Limit Order Buffer", 0, -50, 50, 0.01 );
SPR = Param( "Spread", 0.05, 0.001, 10, 0.001 );
QT = Param( "Order Quantity", 1, 0, 100000, 1 );
ECAN = ParamToggle( "Entry Candle", "Live Candle|Completed Candle", 1 );
MERG = ParamToggle( "Merge Orders", "No|Yes" );
DBUY = ParamToggle( "Disable Buy-Sell", "No|Yes" );
DSHORT = ParamToggle( "Disable Short-Cover", "No|Yes" );
STG = ParamStr( "Strategy Tag", "PRO1" );

if ( APIKEY == "" )
APIKEY = ParamStr( "Trading Key", "" );


FC = DateNum() != Ref( DateNum(), -1 );
LC = DateNum() != Ref( DateNum(), 1 );
ENTIME = IIf(IM, TimeNum()>=TSTIME*100 AND TimeNum()<NOETIME*100, 1);
MSO = IIf(IM, ExRem(TimeNum()>=EXTIME*100, LC), 0);
SYM = Name();

global AlgoFox;

AlgoFox = Name() + Interval( 0 );

if ( DBUY )
{
    Buy = Sell = 0;
}

if ( DSHORT )
{
    Short = Cover = 0;
}

if ( ECAN )
{
    Buy = Ref( Buy, -1 );
    Sell = Ref( Sell, -1 );
    Short = Ref( Short, -1 );
    Cover = Ref( Cover, -1 );
}

lastdt = Nz( StaticVarGet( AlgoFox + "lastdt" ) );
lastssdt = Nz( StaticVarGet( AlgoFox + "lastssdt" ) );
lastsdt = Nz( StaticVarGet( AlgoFox + "lastsdt" ) );
lastcdt = Nz( StaticVarGet( AlgoFox + "lastcdt" ) );
dt = LastValue( DateTime() );

function RoundOff( Array )
{
    return round( Array / SPR )*SPR;
}

if ( ORTYPE == "Limit" && PORP  )
{
    LMB = NumToStr( RoundOff(Close[BarCount-1] *(1+ LOB/100)), 1.2, False );
    LMS = NumToStr( RoundOff(Close[BarCount-1] *(1- LOB/100)), 1.2, False );
}
else if ( ORTYPE == "Limit" && !PORP  )
{
    LMB = NumToStr( RoundOff(Close[BarCount-1] + LOB), 1.2, False );
    LMS = NumToStr( RoundOff(Close[BarCount-1] - LOB), 1.2, False );
}
else
{
    LMB = NumToStr( Close[BarCount-1], 1.2, False );
    LMS = NumToStr( Close[BarCount-1], 1.2, False );
}

if ( Status( "action" ) == actionIndicator AND NOT Nz( StaticVarGet( AlgoFox + "autotrade" ) ) )
{
    RTBuy = RTShort = RTSell = RTCover = 0;
}
else
{
    RTBuy = LastValue( Buy AND ENTIME ) AND dt != lastdt;
    RTShort = LastValue( Short AND ENTIME ) AND dt != lastssdt;
    RTSell = LastValue( Sell OR MSO ) AND dt != lastsdt AND Nz(StaticVarGet( AlgoFox + "OL"));
    RTCover = LastValue( Cover OR MSO ) AND dt != lastcdt AND Nz(StaticVarGet( AlgoFox + "OS"));
}

if ( MERG AND RTSell AND RTShort )
{
    QTY = NumToStr( 2 * QT, 1.0, False );
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    FoxInteractive( UID, "SHORT", SYM, ORTYPE + "|" + PRDT, "", LMS, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + "," + "SELL & SHORT" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + " " + "," + LMS + "," + QTY + "," + INSTR + "," + STG );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
    Message = "Sell & Short " + SYM + " @" + LMS + " At " + Now(2);
    StaticVarSetText( "message", Message );
    StaticVarSet( AlgoFox + "lastsdt", dt );
    StaticVarSet( AlgoFox + "lastssdt", dt );
    StaticVarSet( AlgoFox + "OS", True );
    StaticVarSet( AlgoFox + "OL", False );
}

if ( MERG AND RTCover AND RTBuy )
{
    QTY = NumToStr( 2 * QT, 1.0, False );
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    FoxInteractive( UID, "BUY", SYM, ORTYPE + "|" + PRDT, "", LMB, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + "," + "COVER & BUY" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + " " + "," + LMB + "," + QTY + "," + INSTR + "," + STG );
    Message = "Cover & Buy " + SYM + " @" + LMB + " At " + Now(2);
    StaticVarSetText( "message", Message );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
    StaticVarSet( AlgoFox + "lastcdt", dt );
    StaticVarSet( AlgoFox + "lastdt", dt );
    StaticVarSet( AlgoFox + "OL", True );
    StaticVarSet( AlgoFox + "OS", False );
}

if ( (RTSell AND !MERG) OR (RTSell AND !RTShort AND MERG) )
{
    QTY = NumToStr( QT, 1.0, False );
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    FoxInteractive( UID, "SELL", SYM, ORTYPE + "|" + PRDT, "", LMS, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + "," + "SELL" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + " " + "," + LMS + "," + QTY + "," + INSTR + "," + STG );
    Message = "Sell " + SYM + " @" + LMS + " At " + Now(2);
    StaticVarSetText( "message", Message );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
    StaticVarSet( AlgoFox + "lastsdt", dt );
    StaticVarSet( AlgoFox + "OL", False );
    RTSell = False;
}

if ( (RTCover AND !MERG) OR (RTCover AND !RTBuy AND MERG) )
{
    QTY = NumToStr( QT, 1.0, False );
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    FoxInteractive( UID, "COVER", SYM, ORTYPE + "|" + PRDT, "", LMB, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + "," + "COVER" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + " " + "," + LMB + "," + QTY + "," + INSTR + "," + STG );
    Message = "Cover " + SYM + " @" + LMB + " At " + Now(2);
    StaticVarSetText( "message", Message );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
    StaticVarSet( AlgoFox + "lastcdt", dt );
    StaticVarSet( AlgoFox + "OS", False );
    RTCover = False;
}

if ( RTBuy AND !RTCover )
{
    QTY = NumToStr( QT, 1.0, False );
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    FoxInteractive( UID, "BUY", SYM, ORTYPE + "|" + PRDT, "", LMB, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + "," + "BUY" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + " " + "," + LMB + "," + QTY + "," + INSTR + "," + STG );
    Message = "Buy " + SYM + " @" + LMB + " At " + Now(2);
    StaticVarSetText( "message", Message );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
    StaticVarSet( AlgoFox + "lastdt", dt );
    StaticVarSet( AlgoFox + "OL", True );
}

if ( RTShort AND !RTSell )
{
    QTY = NumToStr( QT, 1.0, False );
    UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
    FoxInteractive( UID, "SHORT", SYM, ORTYPE + "|" + PRDT, "", LMS, QTY, INSTR, STG, APIKEY, "0" );
    _TRACE( "#" + UID + "," + "SHORT" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + " " + "," + LMS + "," + QTY + "," + INSTR + "," + STG );
    Message = "Short " + SYM + " @" + LMS + " At " + Now(2);
    StaticVarSetText( "message", Message );
    StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
    StaticVarSet( AlgoFox + "lastssdt", dt );
    StaticVarSet( AlgoFox + "OS", True );
}

procedure PlotTextEx( String, xx, yy, x1, y1, x2, y2 )
{
    GfxRectangle( xx + x1, yy + y1, xx + x2, yy + y2 );
    GfxDrawText( String, xx + x1, yy + y1, xx + x2, yy + y2, 32 + 5 );
}

procedure PlotTextEx1( String, x1, y1, x2, y2, pxWidth, pxHeight, HOR, VER )
{
	x1 = x1 * pxWidth / 100 + HOR;
	y1 = ( 100 - y1 ) * pxHeight / 100 + VER;
	x2 = x2 * pxWidth / 100 + HOR;
	y2 = ( 100 - y2 ) * pxHeight / 100 + VER;

	GfxRectangle( x1, y1, x2, y2 );
	GfxDrawText( String, x1, y1, x2, y2, 32 + 5 );
}

_SECTION_END();

_SECTION_BEGIN("Message Box");

if(Status("action")==actionIndicator)
{
	MSG = ParamToggle( "Message Window", "Hide|Show", 1 );
	HOR = Param( "Horizontal Position", 0, -1000, 2000, 1 );
	VER = Param( "Vertical Position", 0, -1000, 1000, 1 );
	WID = Param( "Width", 5, 1, 10, 0.1);
	HET = Param( "Height", 4, 1, 10, 0.1);
	FONT = Param( "Font Size", 9, 4, 12, 1 );
	pxHeight = Status( "pxchartheight" );
	pxWidth = Status( "pxchartwidth" );

	GfxSetBkMode( 1 );
	GfxSetOverlayMode( 0 );

	if ( Nz( StaticVarGet( AlgoFox + "OL" ) ) )
		GfxSelectSolidBrush( colorGreen );
	else
		if ( Nz( StaticVarGet( AlgoFox + "OS" ) ) )
			GfxSelectSolidBrush( colorDarkRed );
		else
			GfxSelectSolidBrush( colorBlueGrey );

	GfxSelectPen( colorWhite, 1 );
	GfxSetTextColor( colorWhite );
	GfxSetTextAlign( 0 );

	if ( MSG )
	{
		GfxSelectFont( "Lucida Console", FONT, 600, False, False, 0 );
		PlotTextEx1( StaticVarGetText( "message" ), 0, HET, 5*WID, 0, pxWidth, pxHeight, HOR, VER );
	}
}

_SECTION_END();

_SECTION_BEGIN("Button Trading");

if ( Status( "action" ) == actionIndicator )
{
    BTN = ParamToggle( "Button Trading", "Off|On", 1 );
    LEQTY = ParamStr( "Buy Order Quantity", "1" );
    LXQTY = ParamStr( "Sell Order Quantity", "1" );
    SEQTY = ParamStr( "Short Order Quantity", "1" );
    SXQTY = ParamStr( "Cover Order Quantity", "1" );
    LMP = Param( "Limit Price", 100, 0, 100000, 0.05 );
    TRG = Param( "Trigger Price", 100, 0, 100000, 0.05 );
    SL = Param( "Stoploss In Points", 1, 0, 1000, 0.05 );
    TG = Param( "Target In Points", 1, 0, 1000, 0.05 );
    TRSL = Param( "Trailing Ticks", 1, 0, 1000, 1 );

    TRGP = NumToStr( TRG, 1.2, False ) + "|" + NumToStr( SL, 1.2, False ) + "|" + NumToStr( TG, 1.2, False ) + "|" + NumToStr( TRSL, 1.0, False );

    BHOR = Param( "Button Horizontal Position", 0, -1000, 2000, 1 );
    BVER = Param( "Button Vertical Position", 0, -1000, 1000, 1 );

    GfxSetBkMode( 1 );
    GfxSetOverlayMode( 0 );
    yy = Status( "pxcharttop" ) + BVER + 50;
    xx = Status( "pxchartleft" ) + BHOR + 50;
    GfxSelectPen( colorWhite, 1 );
    GfxSetTextColor( colorBlack );
    GfxSetTextAlign( 0 );
    GfxSelectFont( "Lucida Console", 11, 500, False, False, 1 );
    cl = GetCursorMouseButtons() == 9;
    cx = GetCursorXPosition( 1 );
    cy = GetCursorYPosition( 1 );

    bex1 = xx;
    bey1 = yy;
    bex2 = bex1 + 60;
    bey2 = bey1 + 60;
    bxx1 = bex2 + 5;
    bxy1 = yy;
    bxx2 = bxx1 + 60;
    bxy2 = bxy1 + 60;
    sex1 = xx;
    sey1 = bey2 + 5;
    sex2 = sex1 + 60;
    sey2 = sey1 + 60;
    sxx1 = sex2 + 5;
    sxy1 = sey1;
    sxx2 = sxx1 + 60;
    sxy2 = sxy1 + 60;

    if ( BTN )
    {
        GfxSelectSolidBrush( colorBrightGreen );
        GfxRectangle( bex1, bey1, bex2, bey2 );
        GfxTextOut( "BUY", bex1 + 15, bey1 + 20 );
        GfxSelectSolidBrush( colorRed );
        GfxRectangle( bxx1, bxy1, bxx2, bxy2 );
        GfxTextOut( "SELL", bxx1 + 10, bxy1 + 20 );
        GfxRectangle( sex1, sey1, sex2, sey2 );
        GfxTextOut( "SHORT", sex1 + 3, sey1 + 20 );
        GfxSelectSolidBrush( colorBrightGreen );
        GfxRectangle( sxx1, sxy1, sxx2, sxy2 );
        GfxTextOut( "COVER", sxx1 + 3, sxy1 + 20 );

        if ( LMP == 0 )
        {
            LMB = LMS = NumToStr( C[BarCount-1], 1.2, False );
        }
        else
        {
            LMB = LMS = NumToStr( LMP, 1.2, False );
        }

        if ( cl AND cx > bex1 AND cy > bey1 AND cx < bex2 AND cy < bey2 )
        {
            QTY = LEQTY;
            UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
            FoxInteractive( UID, "BUY", SYM, ORTYPE + "|" + PRDT, TRGP, LMB, QTY, INSTR, STG, APIKEY, "0" );
            _TRACE( "#" + UID + "," + "BTN BUY" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + TRGP + "," + LMB + "," + QTY + "," + INSTR + "," + STG );
            AlertIf( 1, "SOUND C:\\Windows\\Media\\Notify.wav", "Trade Alert", 1, 1 + 2, 1 );
            Message = "Button Buy " + SYM + " @" + LMB + " At " + Now(2);
            StaticVarSetText( "message", Message );
            StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
            PopupWindow( "Buy Triggered from button " + Name(), "BUY", 2, -1, -1, -1, -1, False );
        }

        if ( cl AND cx > bxx1 AND cy > bxy1 AND cx < bxx2 AND cy < bxy2 )
        {
            QTY = LXQTY;
            UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
            FoxInteractive( UID, "SELL", SYM, ORTYPE + "|" + PRDT, TRGP, LMS, QTY, INSTR, STG, APIKEY, "0" );
            _TRACE( "#" + UID + "," + "BTN SELL" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + TRGP + "," + LMS + "," + QTY + "," + INSTR + "," + STG );
            AlertIf( 1, "SOUND C:\\Windows\\Media\\Notify.wav", "Trade Alert", 3, 1 + 2, 1 );
            Message = "Button Sell " + SYM + " @" + LMS + " At " + Now(2);
            StaticVarSetText( "message", Message );
            StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
            PopupWindow( "Sell Triggered from button " + Name(), "SELL", 2, -1, -1, -1, -1, False );
        }

        if ( cl AND cx > sex1 AND cy > sey1 AND cx < sex2 AND cy < sey2 )
        {
            QTY = SEQTY;
            UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
            FoxInteractive( UID, "SHORT", SYM, ORTYPE + "|" + PRDT, TRGP, LMS, QTY, INSTR, STG, APIKEY, "0" );
            _TRACE( "#" + UID + "," + "BTN SHORT" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + TRGP + "," + LMS + "," + QTY + "," + INSTR + "," + STG );
            AlertIf( 1, "SOUND C:\\Windows\\Media\\Notify.wav", "Trade Alert", 2, 1 + 2, 1 );
            Message = "Button Short " + SYM + " @" + LMS + " At " + Now(2);
            StaticVarSetText( "message", Message );
            StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
            PopupWindow( "Short Triggered from button " + Name(), "SHORT", 2, -1, -1, -1, -1, False );
        }

        if ( cl AND cx > sxx1 AND cy > sxy1 AND cx < sxx2 AND cy < sxy2 )
        {
            QTY = SXQTY;
            UID = NumToStr( Nz( StaticVarGet( "UID" ), 1 ), 1.0, False );
            FoxInteractive( UID, "COVER", SYM, ORTYPE + "|" + PRDT, TRGP, LMB, QTY, INSTR, STG, APIKEY, "0" );
            _TRACE( "#" + UID + "," + "BTN COVER" + "," + SYM + "," + ORTYPE + "|" + PRDT + "," + TRGP + "," + LMB + "," + QTY + "," + INSTR + "," + STG );
            AlertIf( 1, "SOUND C:\\Windows\\Media\\Notify.wav", "Trade Alert", 4, 1 + 2, 1 );
            Message = "Button Cover " + SYM + " @" + LMB + " At " + Now(2);
            StaticVarSetText( "message", Message );
            StaticVarSet( "UID", Nz( StaticVarGet( "UID" ), 1 ) + 1 );
            PopupWindow( "Cover Triggered from button " + Name(), "COVER", 2, -1, -1, -1, -1, False );
        }

    }

    Chartid = Name() + "  " + NumToStr( Interval( 0 ) / 60, 1.0, False ) + "-Min";

    atx1 = sex1;
    aty1 = sey2 + 5;
    atx2 = sxx2;
    aty2 = sey2 + 40;

    if ( Nz( StaticVarGet( AlgoFox + "autotrade" ) ) )
    {
        GfxSelectSolidBrush( colorBrightGreen );
        GfxRectangle( atx1, aty1, atx2, aty2 ) ;
        GfxTextOut( "AUTO ON", atx1 + 25, aty1 + 8 );
    }
    else
    {
        GfxSelectSolidBrush( colorRed );
        GfxRectangle( atx1, aty1, atx2, aty2 ) ;
        GfxTextOut( "AUTO OFF", atx1 + 25, aty1 + 8 );
    }

    if ( cl AND cx > atx1 AND cy > aty1 AND cx < atx2 AND cy < aty2 )
    {
        StaticVarSet( AlgoFox + "autotrade", !Nz( StaticVarGet( AlgoFox + "autotrade" ) ) );

        if ( StaticVarGet( AlgoFox + "autotrade" ) )
        {
            _TRACE( "Auto Trading Started: " + Chartid );
            AlertIf( 1, "SOUND C:\\Windows\\Media\\notify.wav", "Audio Alert", 1, 2, 1 );
            PopupWindow( "Auto Trade Started: " + Chartid, "Auto Trade", 1, -1, -1 );
        }
        else
        {
            _TRACE( "Auto Trading Stopped: " + Chartid );
            AlertIf( 1, "SOUND C:\\Windows\\Media\\notify.wav", "Audio Alert", 1, 2, 1 );
            PopupWindow( "Auto Trade Stopped: " + Chartid, "Auto Trade", 1, -1, -1 );
        }

        RequestTimedRefresh( 1 );
    }
}

_SECTION_END();
