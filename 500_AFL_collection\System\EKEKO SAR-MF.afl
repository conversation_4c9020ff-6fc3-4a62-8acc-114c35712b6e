//------------------------------------------------------------------------------
//
//  Formula Name:    EKEKO SAR-MF
//  Author/Uploader: RAFAEL GOMEZ 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2006-07-30 23:49:54
//  Origin:          PERU
//  Keywords:        
//  Level:           semi-advanced
//  Flags:           system
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=659
//  Details URL:     http://www.amibroker.com/library/detail.php?id=659
//
//------------------------------------------------------------------------------
//
//  SAR EVENTS WITH SPECIAL PARAMETERS FOR MULTI FRAMES 1 MINUTE, 30 MINUTES
//  (MARKET PROFILE), DAILY, WEEKLY, MOUNTHLY
//
//  SUCESS GUYS
//
//------------------------------------------------------------------------------

//this script detect events with SAR indicators whith special parameters for simulted multi frames times, for this reason is named alineamiento sar


Offset=10;
Filter=1;
NumColumns=4;
Title="Ekeko SAR-MF 1,30,D,W,M";

function MySAR()
{

IAF = Param("sarIAF",0.1,0.01,1,0.01);    // acceleration factor
MaxAF = Param("sarMaxAF",0.25,0.01,1.5,0.01); // max acceleration
ASAR = Param( "ASAR", -1, -50, 1000, 1 );

psar = Close;		// initialize
long = 1;        // assume long for initial conditions
af = IAF;         // init acelleration factor
ep = Low[ 0 ];   // init extreme point
hp = High [ 0 ];
lp = Low [ 0 ];

for( i = 2; i < BarCount; i++ )
{
	if ( long )
	{
		psar [ i ] = psar [ i-1 ] + af * ( hp - psar [ i-1 ] );
	}
	else
	{
		psar [ i ] = psar [ i-1 ] + af * ( lp - psar [ i-1 ] );
	}

	reverse =  0;
	//check for reversal
	if ( long )
	{
		if ( Low [ i ] < psar [ i ]  )
		{
			long = 0; reverse = 1; // reverse position to Short
			psar [ i ] =  hp;       // SAR is High point in prev trade
			lp = Low [ i ];
			af = IAF;
		}
	}
	else
	{
		if ( High [ i ] > psar [ i ]  )
		{
			long = 1; reverse = 1;        //reverse position to long
			psar [ i ] =  lp;
			hp = High [ i ];
			af = IAF;
		}
	}

	if ( reverse == 0 )
	{
		if ( long )
		{
			if ( High [ i ] > hp ) 
			{
				hp = High [ i ]; 
				af = af + IAF; 
				if( af > MaxAF ) af = MaxAF; 
			}
             
			if( Low[ i - 1 ] < psar[ i ] ) psar[ i ] = Low[ i - 1 ];
			if( Low[ i - 2 ] < psar[ i ] ) psar[ i ] = Low[ i - 2 ];
		}
       else
		{
			if ( Low [ i ] < lp )  
			{ 
				lp = Low [ i ]; 
				af = af + IAF; 
				if( af > MaxAF ) af = MaxAF; 
			}	
				
			if( High[ i - 1 ] > psar[ i ] ) psar[ i ] = High[ i - 1 ];
			if( High[ i - 2 ] > psar[ i ] ) psar[ i ] = High[ i - 2 ];

		}
	}
}



  Plot(0,"Null",colorYellow,styleLine|styleNoLabel);

xxxx=SAR(IAF,MaxAF)>Ref(C,-ASAR);//trabajando con close de candela anterior
yyyy=Ref(C,-ASAR)>SAR(IAF,MaxAF) ;

SAREVENTO2=(IIf(yyyy,1,IIf(xxxx,0,1)));

   return(SAREVENTO2);
}

function DrawSAR(style)
{
Offset=Offset+10;

if(style == 0)
  {
  style=in1Minute;
  }

  TimeFrameSet(style);
  SAREVENTO2=TimeFrameExpand(MySAR(),style);
  PlotShapes( IIf(SAREVENTO2<=1,14,0) ,IIf(SAREVENTO2==0,colorWhite,colorGreen)  ,  0, 1, Offset);

  PlotShapes( IIf(SAREVENTO2==0,13,0) ,IIf(SAREVENTO2==1,colorWhite,colorRed), 0, 1, Offset*-1);
  TimeFrameRestore();  
}

Offset=0;

DrawSAR(in1Minute);
DrawSAR(2*in15Minute);
//DrawSAR(2*in5Minute);
//DrawSAR(inHourly);
DrawSAR(inDaily);
DrawSAR(inWeekly);
DrawSAR(inMonthly);


GraphXSpace = 40;