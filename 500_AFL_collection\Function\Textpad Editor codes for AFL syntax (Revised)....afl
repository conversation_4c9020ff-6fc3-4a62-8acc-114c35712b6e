//------------------------------------------------------------------------------
//
//  Formula Name:    Textpad Editor codes for AFL syntax (Revised)...
//  Author/Uploader: dingo 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2004-02-09 23:50:08
//  Origin:          
//  Keywords:        Textpad
//  Level:           basic
//  Flags:           system,exploration,indicator,commentary,function
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=337
//  Details URL:     http://www.amibroker.com/library/detail.php?id=337
//
//------------------------------------------------------------------------------
//
//  Textpad Editor codes for AFL syntax (Revised)...
//
//------------------------------------------------------------------------------

; Syntax file for AFL

C=1

[Syntax]
Namespace1 = 6
IgnoreCase = Yes
InitKeyWordChars = A-Za-z_
KeyWordChars = A-Za-z0-9_
BracketChars=()
OperatorChars=*/+-><
PreprocStart =
SyntaxStart =
SyntaxEnd =
CommentStart = /*
CommentEnd = */
CommentStartAlt = 
CommentEndAlt = 
SingleComment = //
SingleCommentCol =
StringStart = "
StringEnd = "
StringAlt =
StringEsc =
CharStart = "
CharEnd = "
CharEsc =

[Preprocessor keywords]
#include
#pragma

; Following color will be Black
[Keywords 1]
open 
o
close 
c
high 
h
low 
l
volume 
v
openint 
oi
average 
a

; Following color will be Blue
[Keywords 2]
_N
_TRACE
abs
AccDist
acos
AddColumn
AddTextColumn
AddToComposite
ADLine
AdvIssues
AdvVolume
ADX
AlertIf
AMA
AMA2
ApplyStop
atan
ATR
BarIndex
BarsSince
BBandBot
BBandTop
BeginValue
CategoryAddSymbol
CategoryGetName
CategoryGetSymbols
CategoryRemoveSymbol
CCI
ceil
Chaikin
Correlation
cos
CreateObject
CreateStaticObject
Cross
Cum
Date
DateNum
DateTime
Day
DayOfWeek
DayOfYear
DecIssues
DecVolume
DEMA
EMA
EnableRotationalTrading
EnableScript
EnableTextOutput
EncodeColor
EndValue
Equity
exp
ExRem
ExRemSpan
fclose
feof
fgets
Flip
floor
fopen
Foreign
fputs
frac
FullName
GapDown
GapUp
GetBaseIndex
GetCategorySymbols
GetChartID
GetDatabaseName
GetExtraData
GetScriptObject
GroupID
HHV
HHVBars
Highest
HighestBars
HighestSince
HighestSinceBars
Hold
Hour
IIf
IndustryID
Inside
int
Interval
InWatchList
IsEmpty
IsFavorite
IsFinite
IsIndex
IsNan
IsNull
IsTrue
LastValue
LineArray
LinearReg
LinRegIntercept
LinRegSlope
LLV
LLVBars
log
log10
Lowest
LowestBars
LowestSince
LowestSinceBars
MA
MACD
MarketID
Max
MDI
Median
MFI
Min
Minute
Month
Name
Now
NumToStr
NVI
Nz
OBV
Optimize
OscP
OscV
Outside
Param
ParamColor
ParamStr
PDI
Peak
PeakBars
Percentile
Plot
PlotForeign
PlotGrid
PlotOHLC
PlotShapes
PlotVAPOverlay
Prec
Prefs
printf
PVI
Random
Ref
RelStrength
RestorePriceArrays
RMI
ROC
round
RSI
RSIa
RWI
RWIHi
RWILo
SAR
Second
SectorID
SelectedValue
SetBarsRequired
SetForeign
SetFormulaName
SetOption
SetTradeDelays
sign
Signal
sin
sqrt
Status
StdErr
StDev
StochD
StochK
StrExtract
StrFind
StrFormat
StrLeft
StrLen
StrMid
StrRight
StrToNum
Study
Sum
tan
TEMA
TimeFrameCompress
TimeFrameExpand
TimeFrameGetPrice
TimeFrameRestore
TimeFrameSet
TimeNum
Trin
Trix
Trough
TroughBars
TSF
Ultimate
UncIssues
UncVolume
ValueWhen
Version
Wilders
WMA
WriteIf
WriteVal
Year
Zig

; Following color will be Black 
[Keywords 3]
and
BarCount
buy
buyprice
colorAqua
colorBlack
colorBlue
colorBlueGrey
colorBrightGreen
colorBrown
colorCustom1
colorCustom10
colorCustom11
colorCustom12
colorCustom13
colorCustom14
colorCustom15
colorCustom16
colorCustom2
colorCustom3
colorCustom4
colorCustom5
colorCustom6
colorCustom7
colorCustom8
colorCustom9
colorDarkBlue
colorDarkGreen
colorDarkGrey
colorDarkOliveGreen
colorDarkRed
colorDarkTeal
colorDarkYellow
colorGold
colorGreen
colorGrey
colorGrey
colorIndigo
colorLavender
colorLightBlue
colorLightGrey
colorLightOrange
colorLightYellow
colorLime
colorOrange
colorPaleBlue
colorPaleGreen
colorPaleTurquoise
colorPink
colorPlum
colorRed
colorRose
colorSeaGreen
colorSkyblue
colorTan
colorTeal
colorTurquoise
colorViolet
colorWhite
colorYellow
column0 
column0format
column0name
column1
column1format
column1name
column2
column2format
column2name
column3
column3format
column3name
column4
column4format
column4name
column5
column5format
column5name
column6
column6format
column6name
column7
column7format
column7name
column8
column8format
column8name
column9
column9format
column9name
compressHigh 
compressLast 
compressLow 
compressOpen 
compressVolume
cover
coverprice
expandLast 
expandFirst 
expandPoint
false
filter
graph0
graph0barcolor
graph0color
graph0name
graph0style
graph1
graph1barcolor
graph1color
graph1name
graph1style
graph2
graph2barcolor
graph2color
graph2name
graph2style
graph3
graph3barcolor
graph3color
graph3name
graph3style
graph4
graph4barcolor
graph4color
graph4name
graph4style
graph5
graph5barcolor
graph5color
graph5name
graph5style
graph6
graph6barcolor
graph6color
graph6name
graph6style
graph7
graph7barcolor
graph7color
graph7name
graph7style
graph8
graph8barcolor
graph8color
graph8name
graph8style
graph9
graph9barcolor
graph9color
graph9name
graph9style
graphxspace
graphzorder
in15Minute 
in1Minute 
in5Minute 
inDaily 
inHourly 
inMonthly 
inWeekly
margindeposit
maxgraph
not
Null
numcolumns
or
pointvalue
PositionScore
positionsize
roundlotsize
scoreNoRotate
sell
sellprice
shapeCircle
shapeDigit0
shapeDigit1
shapeDigit2
shapeDigit3
shapeDigit4
shapeDigit5
shapeDigit6
shapeDigit7
shapeDigit8
shapeDigit9
shapeDownArrow
shapeDownTriangle
shapeHollowCircle
shapeHollowDownArrow
shapeHollowDownTriangle
shapeHollowSmallCircle
shapeHollowSmallDownTriangle
shapeHollowSmallSquare
shapeHollowSmallUpTriangle
shapeHollowSquare
shapeHollowStar
shapeHollowUpArrow
shapeHollowUpTriangle
shapeNone
shapePositionAbove
shapeSmallCircle
shapeSmallDownTriangle
shapeSmallSquare
shapeSmallUpTriangle
shapeSquare
shapeStar
shapeUpArrow
shapeUpTriangle
short
shortprice
styleArea
styleBar
styleCandle
styleDots
styleHistogram
styleLeftAxisScale
styleLine
styleLog
styleNoDraw
styleNoLabel
styleNoLine
styleNoRescale
styleOwnScale
stylePointAndFigure
styleStaircase
styleSwingDots
styleThick
ticksize
title
true

; Following color will be Red 
[Keywords 4]
do
else
for
function
global
#include
#pragma
if
local
procedure
return
while

