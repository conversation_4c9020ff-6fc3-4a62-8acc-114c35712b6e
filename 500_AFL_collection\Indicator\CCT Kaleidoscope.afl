//------------------------------------------------------------------------------
//
//  Formula Name:    CCT Kaleidoscope
//  Author/Uploader: <PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-07-20 09:57:34
//  Origin:          <PERSON>,  http://www.cedarcreektrading.com
//  Keywords:        linear regression, momentum
//  Level:           medium
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=75
//  Details URL:     http://www.amibroker.com/library/detail.php?id=75
//
//------------------------------------------------------------------------------
//
//  <PERSON> wrote:
//
//  "The CCT Kaleidoscope is an interesting combination of three separate
//  momentum oscillators. I&amp;#8217;ve combined the MetaStock LinRegSlope
//  indicator with the Stochastic Momentum Indicator and added the True
//  Strength Index. As always, I have "tweaked" the formula numbers to various
//  fibonacci numbers and assigned various weights to each of the parts. The
//  SMI is a "double-smoothed stochastic" introduced by William Blau. Mr. Blau
//  also has written about the True Strength Index, which in his words is a
//  "cross between a relative strength indicator and a moving average
//  convergence/divergence indicator with many of the desirable properties from
//  each". The combination of the three is a super powerful tool I call the
//  Kaleidoscope."
//
//------------------------------------------------------------------------------

/* CCT Kaleidoscope
**
** Originally developed by Steve Karnish 
** http://www.cedarcreektrading.com
**
** AFL translation by Tomasz Janeczko
**
** Set scaling: Automatic
** Grid: Middle
*/

graph0=LinRegSlope(C,13) 
+ 100 * ( EMA( EMA( C - Ref( C, -1 ) ,34 ) ,21) 
/ EMA( EMA( Abs( C - Ref( C, -1) ),34 ), 21 ) ) 
+100 * ( EMA( EMA( C - (0.5 * ( HHV(H,13) + LLV(L,13))),21),3) 
/ (0.5 * EMA( EMA( HHV(H,13) - LLV(L,13),21),3 ) ) );

