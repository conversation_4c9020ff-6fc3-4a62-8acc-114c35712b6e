//------------------------------------------------------------------------------
//
//  Formula Name:    Automatic Trend-line
//  Author/Uploader: <PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2001-06-16 07:56:58
//  Origin:          Created by <PERSON><PERSON>
//  Keywords:        trend
//  Level:           medium
//  Flags:           indicator
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=3
//  Details URL:     http://www.amibroker.com/library/detail.php?id=3
//
//------------------------------------------------------------------------------
//
//  A trend line is a sloping line drawn between two prominent points on a
//  chart. Rising trend lines are usually drawn between two troughs (low
//  points) to illustrate price support while falling trend lines are usually
//  drawn between two peaks (high points) to illustrate upside price
//  resistance. The consensus is that once a trend has been formed (two or more
//  peaks/troughs have touched the trend line and reversed direction) it will
//  remain intact until broken.
//
//  The trend line is described by well-know linear equation:
//
//  y = ax + b
//
//  where x represents time (bar number), y represents price, a defines the
//  slope of the line and b defines initial offset. The main problem in
//  defining appropriate AFL formula is finding the values of these two latter
//  coefficients. If a trend line is drawn between two important lows the slope
//  of the line could be calculated by subtracting the second low price from
//  the first low price and dividing the result by a number of bars between the
//  lows:
//
//  a = ( low2 - low1 ) / ( bars2 - bars1 )
//
//  Calculating offset (b) value is trivial when we shift the time scale so x=0
//  is located at the first low. In this case b=low1.
//
//  So our mathematical formula for the trendline between two important lows
//  will look like this:
//
//  y = ( x - bars1 ) * ( low2 - low1 ) / ( bars2 - bars1 ) + low1
//
//  While determining low prices is simple (just point your mouse over the
//  dominant low and read the low price from a data tooltip that appears on the
//  screen), determining the bar number is not that simple. You can of course
//  count bars by hand but this is simply too much work (especially when you
//  don't have Florida volunteers for a recount :-) ). Luckily we have AFL that
//  allows us to do it in automatic way. All we have to do is to make a running
//  total of bars (our x coordinate) using cum() function:
//
//  x = cum( 1 );
//
//  and then find out where low occured using valuewhen() function:
//
//  bar1 = valuewhen( low == low1, x, 1 );
//
//  bar2 = valuewhen( low == low2, x, 1 );
//
//  For more detailed description please check out:
//
//  <a href="http://www.amibroker.com/newsletter/04-2000.html">Newsletter
//  04/2000</a>
//
//------------------------------------------------------------------------------

x = Cum(1);

perchg = 0.3*LastValue( Highest( ROC( Low, 50 ) ));

startvalue = LastValue( Trough( Low, perchg, 1 ) );
endvalue1 = LastValue( Trough( Low, perchg, 2 ) );

startbar = LastValue( ValueWhen( Low == startvalue, x, 1 ) );
endbar = LastValue( ValueWhen( Low == endvalue1, x, 1 ) );

Aa = (endvalue1-startvalue)/(endbar-startbar);
b = startvalue;

trendline = Aa * ( x  - startbar ) + b; 

Plot( Close, "Price", colorBlue, styleCandle );
Plot( IIf( x >= endbar, trendline, Null ), "Trendline", colorRed );