/***************************************
** Strategy: Simple Line Momentum v1.3
** Logic:    Enter long on 2 up-moves of
** BreakoutLine without a
** down-move in BreakdownLine.
***************************************/

OptimizerSetEngine( "cmae" );

_SECTION_BEGIN("Params");
LookbackPeriod = Optimize("Lookback Period", 30, 1, 100, 1);

// -- Custom Bar (Box) Parameters --
boxSizePercent = Param("Box %", 0.3, 0.1, 3, 0.1);
boxSizeORbFactor = Param("ORB ATR factor", 0.2, 0.2, 3, 0.2);
_SECTION_END();


_SECTION_BEGIN("Custom Bar OHLC");
firstBar = Day() != Ref(Day(), -1);
ORbHigh = ValueWhen(firstBar, High);
ORbLow = ValueWhen(firstBar, Low);
ORbCenter = ValueWhen(firstBar, (ORbHigh + ORbLow) / 2);
tr = ATR(5 * LookbackPeriod);
ORBAtr = ValueWhen(firstBar, tr);

boxSize = round(Max(boxSizePercent * ORbCenter / 100, boxSizeORbFactor * ORBAtr) / 5 ) * 5;

// Rounded Custom Bars
rH = round(H / boxSize);
myH = boxSize * IIf(rH < H / boxSize, rH + 1, rH);

rL = round(L / boxSize);
myL = boxSize * IIf(rL > L / boxSize, rL - 1, rL);

rO = round(O / boxSize);
myO = boxSize * IIf(rO > O / boxSize, rO - 1, rO);

rC = round(C / boxSize);
myC = boxSize * IIf(rC > C / boxSize, rC - 1, rC);

// Adjust myC to wick edges
myC = IIf(myH - C < boxSize / 3, myH, myC);
myC = IIf(C - myL < boxSize / 3, myL, myC);
_SECTION_END();


_SECTION_BEGIN("Trade Logic");
// --- Indicator Lines ---
BreakoutLine = Min(myH + 5 * boxSize, HHV(myH, LookbackPeriod));
BreakdownLine = Max(myL - 5 * boxSize, LLV(myL, LookbackPeriod));

for( i = 2; i < BarCount; i++ )
{
	BreakdownLine[i] = IIf(myL[i] == BreakdownLine[i], BreakdownLine[i], Max(BreakdownLine[i], BreakdownLine[i - 1]) );
	BreakoutLine[i] = IIf(myH[i] == BreakoutLine[i], BreakoutLine[i], Min(BreakoutLine[i], BreakoutLine[i - 1]) );
}
// --- Entry and Exit Conditions ---
// --- State Machine Implementation ---
Buy = Sell = Short = Cover = 0; // Initialize arrays

// State variables for the loop
long_state = 0;  // 0: idle, 1: saw first up-move, 2: saw flat move
short_state = 0; // 0: idle, 1: saw first down-move, 2: saw flat move

for( i = 2; i < BarCount; i++ )
{
    // --- Long State Machine ---
    // Reset condition: if BreakdownLine moves down, reset the long state.
    if( BreakdownLine[i] < BreakdownLine[i-1] OR BreakoutLine[i] < BreakoutLine[i-1]  )
    {
        long_state = 0;
    }

    // Sequence detection for Long Entry
    if( long_state == 2 AND BreakoutLine[i] > BreakoutLine[i-1] )
    {
        Buy[i] = 1;      // Stage 3: Second up-move detected, trigger Buy
        long_state = 0;  // Reset state after Buy signal
        short_state = 0; // Also reset short state to prevent conflict
    }
    else if( long_state == 1 AND BreakoutLine[i] == BreakoutLine[i-1] )
    {
        long_state = 2; // Stage 2: Flat move detected
    }
    else if( long_state == 0 AND BreakoutLine[i] > BreakoutLine[i-1] )
    {
        long_state = 1; // Stage 1: First up-move detected
    }

    // --- Short State Machine ---
    // Reset condition: if BreakoutLine moves up, reset the short state.
    if( BreakoutLine[i] > BreakoutLine[i-1] OR BreakdownLine[i] > BreakdownLine[i-1] )
    {
        short_state = 0;
    }
    
    // Sequence detection for Short Entry
    if( short_state == 2 AND BreakdownLine[i] < BreakdownLine[i-1] )
    {
        Short[i] = 1;    // Stage 3: Second down-move detected, trigger Short
        short_state = 0; // Reset state after Short signal
        long_state = 0;  // Also reset long state to prevent conflict
    }
    else if( short_state == 1 AND BreakdownLine[i] == BreakdownLine[i-1] )
    {
        short_state = 2; // Stage 2: Flat move detected
    }
    else if( short_state == 0 AND BreakdownLine[i] < BreakdownLine[i-1] )
    {
        short_state = 1; // Stage 1: First down-move detected
    }
}

Sell = BreakdownLine < Ref(BreakdownLine, -1);
Cover = BreakoutLine > Ref(BreakoutLine, -1);

Sell = Sell OR Short;
Cover = Cover OR Buy;

// --- Clean and manage signals ---
Buy = ExRem(Buy, Sell);
Sell = ExRem(Sell, Buy);

Short = ExRem(Short, Cover);
Cover = ExRem(Cover, Short);

BuyPrice = C;
SellPrice = C;
ShortPrice = C;
CoverPrice = C;
SetPositionSize(RoundLotSize, spsShares);
_SECTION_END();


_SECTION_BEGIN("Plotting");
SetChartOptions(0, chartShowArrows | chartShowDates);
PlotOHLC(myO, myH, myL, myC, "CustomBars", colorDefault, styleCandle);

Plot(BreakoutLine, "Breakout Line", colorBlue, styleThick);
Plot(BreakdownLine, "Breakdown Line", colorRed, styleThick);

// --- Plot Buy and Sell arrows ---
PlotShapes(IIf(Buy, shapeUpArrow, shapeNone), colorGreen, 0, myL, -20);
PlotShapes(IIf(Sell, shapeDownArrow, shapeNone), colorPlum, 0, myH, 20);
PlotShapes(IIf(Short, shapeDownArrow, shapeNone), colorRed, 0, myH, 20);
PlotShapes(IIf(Cover, shapeUpArrow, shapeNone), colorPlum, 0, myL, -20);

Title = StrFormat("{{NAME}} - {{INTERVAL}} | O: %g, H: %g, L: %g, C: %g (%.2f%%) | Vol: %g | box: %g",
    O, H, L, C, SelectedValue(ROC(C, 1)), V, boxSize);
_SECTION_END();
