//------------------------------------------------------------------------------
//
//  Formula Name:    Bull Fear / Bear Fear
//  Author/Uploader: <PERSON><PERSON> 
//  E-mail:          <EMAIL>
//  Date/Time Added: 2002-01-24 09:58:19
//  Origin:          
//  Keywords:        
//  Level:           basic
//  Flags:           system
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=152
//  Details URL:     http://www.amibroker.com/library/detail.php?id=152
//
//------------------------------------------------------------------------------
//
//  Simple Trading System
//
//  http://www.eis.pl/kr/AFM/e-st-<PERSON>_Fear-Bear_Fear_with_DX.html
//
//------------------------------------------------------------------------------

// BBFEAR.AFL v 0.001 24/01/2002
// Bull Fear / Bear Fear with DX Trading System
// Developed by Jef
// Published in http://www.nt-tech.com.au/guppy/
// Coded by Marek Chlopek, January 2002


// ****************************************************************************************
// INITIALIZATION OF EXPLORATION IN AMIBROKER 
// ****************************************************************************************
Filter = 1;
NumColumns = 5;
Column0 = O; Column0Name = "O"; Column0Format = 1.2;
Column1 = H; Column1Name = "H"; Column1Format = 1.2;
Column2 = L; Column2Name = "L"; Column2Format = 1.2;
Column3 = C; Column3Name = "C"; Column3Format = 1.2;
Column4 = V; Column4Name = "V"; Column4Format = 1.0;

// END OF "INITIALIZATION OF EXPLORATION IN AMIBROKER" SECTION


// ****************************************************************************************
// MAIN CODE
// ****************************************************************************************
Opt1 = Optimize("DX Level", 16, 10, 30, 1);
Opt2 = Optimize("Period", 15, 10, 50, 1); // n

n = Opt2;
BullFear = (HHV(High,n) - LLV(High,n))/2 + LLV(High,n);
BearFear = (HHV(Low,n) - LLV(Low,n))/2 + LLV(Low,n);

// Exploration in Amibroker
AddColumn(BullFear, "BullFear", format=1.2);
AddColumn(BearFear, "BearFear", format=1.2);

// END OF "MAIN CODE" SECTION


// ****************************************************************************************
// TRADING SYSTEM ENTRY FORMULAS
// ****************************************************************************************
Buy   = Cross(Close, BullFear) AND ADX(10) > Opt1;
Short = Cross(BearFear, Close);

// Exploration in Amibroker
AddColumn(Buy,        "Buy",        format=1.0);
AddColumn(Short,      "Short",      format=1.0);
//AddColumn(BuyPrice,   "BuyPrice",   format=1.2);
//AddColumn(ShortPrice, "ShortPrice", format=1.2);

// END OF "TRADING SYSTEM ENTRY FORMULAS" SECTION


// ****************************************************************************************
// TRADING SYSTEM EXIT FORMULAS
// ****************************************************************************************
Sell  = Short;
Cover = Buy;

// Exploration in Amibroker
AddColumn(Sell,       "Sell",       format=1.0);
AddColumn(Cover,      "Cover",      format=1.0);
//AddColumn(SellPrice,  "SellPrice",  format=1.2);
//AddColumn(CoverPrice, "CoverPrice", format=1.2);

// END OF "TRADING SYSTEM EXIT FORMULAS" SECTION


// ****************************************************************************************
// TRADING SYSTEM EXCESSIVE ENTRY/EXIT SIGNALS REMOVING FORMULAS
// ****************************************************************************************
Buy   = ExRem(Buy, Sell);
Sell  = ExRem(Sell, Buy);
Short = ExRem(Short, Cover);
Cover = ExRem(Cover, Short);

// END OF "TRADING SYSTEM EXCESSIVE ENTRY/EXIT SIGNALS REMOVING FORMULAS" SECTION


// ****************************************************************************************
// GRAPHIC PRESENTATION IN AMIBROKER
// ****************************************************************************************
MaxGraph = 3;
Graph0 = C; Graph0Style = 4; Graph0Color = 1;
Graph1 = BullFear; Graph1Style = 1; Graph1Color = 5;
Graph2 = BearFear; Graph2Style = 1; Graph2Color = 4;

Title = Name()
        + " - C (Black) = " + WriteVal(Graph0, 1.2)
        + ", BullFear (Green) = " + WriteVal(Graph1, 1.2)
        + ", BearFear (Red) = " + WriteVal(Graph2, 1.2);

// END OF "GRAPHIC PRESENTATION IN AMIBROKER" SECTION


// ****************************************************************************************
// END OF CODE (BBFEAR.AFL)
// ****************************************************************************************
/**/