//------------------------------------------------------------------------------
//
//  Formula Name:    Reaction Trend System
//  Author/Uploader: Ed
//  E-mail:          <EMAIL>
//  Date/Time Added: 2004-05-20 13:32:18
//  Origin:          translated from <PERSON><PERSON> <PERSON>. using New Concepts in Technical Trading 1978
//  Keywords:        trading system trend
//  Level:           advanced
//  Flags:           system
//  Formula URL:     http://www.amibroker.com/library/formula.php?id=357
//  Details URL:     http://www.amibroker.com/library/detail.php?id=357
//
//------------------------------------------------------------------------------
//
//  see code for details.
//
//------------------------------------------------------------------------------
#include <ReactionTrendSystem.afl>
// --------------------------------------
// For Chart
Plot( C, "", 1, 64 );
brisk = ceil( ( BuyPrice - LBOP ) * RoundLotSize );
blots = floor( 25000 / brisk );
srisk = ceil( ( S1  - SellPrice ) * RoundLotSize );
slots = floor( 25000 / srisk );
myVar = ", risk = (" + brisk + ", " + sRisk + "), blots = (" + blots + ", " + slots + ")";
SetPositionSize( IIf( Buy, blots, 1 )*RoundLotSize, spsShares );
// define title
Title = Name() + ",  O: " + WriteVal( O ) + ",  H: " + WriteVal( H ) + ",  L: " + WriteVal( L ) + ",  C: " + WriteVal( C ) +
        EncodeColor( colorOrange ) + ",  HBOP: " + EncodeColor( colorWhite ) + WriteVal( HBOP ) +
        EncodeColor( colorRed ) + ",  S1: " + EncodeColor( colorWhite ) + WriteVal( HBOP ) +
        EncodeColor( colorBrightGreen ) + ",  B1: " + EncodeColor( colorWhite ) + WriteVal( HBOP ) +
        EncodeColor( colorGreen ) + ",  LBOP: " + EncodeColor( colorWhite ) + WriteVal( HBOP ) + EncodeColor( colorDarkGrey ) + myVar
        ;
// buy, sell, short and cover symbols
PlotShapes( shapeDownArrow*Buy, colorGreen, 0, BuyPrice, 0 );
PlotShapes( shapeupArrow*Sell, colorYellow, 0, SellPrice, 0 );
PlotShapes( shapeHollowUpArrow*Short, colorRed, 0, ShortPrice, -15 );
PlotShapes( shapeHollowDownArrow*Cover, colorWhite, 0, CoverPrice, 15 );
// triagle show breakouts
PlotShapes( shapeDownTriangle*tpsl, colorGreen, 0, BuyPrice, 0 );
PlotShapes( shapeUpTriangle*tpss, colorRed, 0, ShortPrice, -15 );
// 1 = "B" day, 2 = "O" day, 3 = "S" day
PlotShapes( shapeDigit1 * ( hlp_phase == 1 ), IIF( hlp_b == 1, colorYellow, colorWhite ), 0, HBOP, 0 );
PlotShapes( shapeDigit2 * ( hlp_phase == 2 ), colorWhite, 0, HBOP, 0 );
PlotShapes( shapeDigit3 * ( hlp_phase == 3 ), IIF( hlp_s == 1, colorYellow, colorWhite ), 0, HBOP, 0 );
// lightblue digits at days where original phase is used for the calculation.
PlotShapes( shapeDigit1 * ( hlp_phase_l1 == 1 ), colorLightBlue, 0, HBOP, -15 );
PlotShapes( shapeDigit3 * ( hlp_phase_l1 == 3 ), colorLightBlue, 0, HBOP, -15 );
// draw levels
Plot( B1, "", colorBrightGreen, 1 );
Plot( S1, "", colorRed, 1 );
Plot( HBOP, "", colorOrange, 1 );
Plot( LBOP, "", colorGreen, 1 );
// for analysis purposes
Filter = 1;
AddColumn( phase_arr, "Phase" );
Addcolumn( LBOP, "LBOP" );
AddColumn( O, "O" );
AddColumn( H, "H" );
AddColumn( L, "L" );
AddColumn( C, "C" );
AddColumn( buy, "buy" );
AddColumn( sell, "sell" );
AddColumn( short, "short" );
AddColumn( cover, "cover" );
AddColumn( tpsl, "tpsl" );
AddColumn( tpss, "tpss" );
